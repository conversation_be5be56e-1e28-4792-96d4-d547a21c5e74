const express = require('express');
const router = express.Router();
const approverController = require('../controllers/approverController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validate, validateParams } = require('../middleware/validation');
const { approverSchemas, commonSchemas } = require('../utils/validation');

// 获取审批人列表
router.get('/', authenticateToken, approverController.getApprovers);

// 添加审批人
router.post('/', 
  authenticateToken, 
  requireAdmin,
  validate(approverSchemas.createApprover), 
  approverController.createApprover
);

// 更新审批人信息
router.put('/:id', 
  authenticateToken, 
  requireAdmin,
  validateParams(commonSchemas.idParam),
  validate(approverSchemas.updateApprover), 
  approverController.updateApprover
);

// 删除审批人
router.delete('/:id', 
  authenticateToken, 
  requireAdmin,
  validateParams(commonSchemas.idParam),
  approverController.deleteApprover
);

module.exports = router;
