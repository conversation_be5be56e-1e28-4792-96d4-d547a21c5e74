{"version": 3, "sources": ["../../../src/dialects/mssql/query.js"], "sourcesContent": ["'use strict';\n\nconst AbstractQuery = require('../abstract/query');\nconst sequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('mssql');\nconst _ = require('lodash');\nconst { logger } = require('../../utils/logger');\n\nconst debug = logger.debugContext('sql:mssql');\n\nconst minSafeIntegerAsBigInt = BigInt(Number.MIN_SAFE_INTEGER);\nconst maxSafeIntegerAsBigInt = BigInt(Number.MAX_SAFE_INTEGER);\n\nfunction getScale(aNum) {\n  if (!Number.isFinite(aNum)) return 0;\n  let e = 1;\n  while (Math.round(aNum * e) / e !== aNum) e *= 10;\n  return Math.log10(e);\n}\n\nclass Query extends AbstractQuery {\n  getInsertIdField() {\n    return 'id';\n  }\n\n  getSQLTypeFromJsType(value, TYPES) {\n    const paramType = { type: TYPES.NVarChar, typeOptions: {}, value };\n    if (typeof value === 'number') {\n      if (Number.isInteger(value)) {\n        if (value >= -2147483648 && value <= 2147483647) {\n          paramType.type = TYPES.Int;\n        } else {\n          paramType.type = TYPES.BigInt;\n        }\n      } else {\n        paramType.type = TYPES.Numeric;\n        //Default to a reasonable numeric precision/scale pending more sophisticated logic\n        paramType.typeOptions = { precision: 30, scale: getScale(value) };\n      }\n    } else if (typeof value === 'bigint') {\n      if (value < minSafeIntegerAsBigInt || value > maxSafeIntegerAsBigInt) {\n        paramType.type = TYPES.VarChar;\n        paramType.value = value.toString();\n      } else {\n        return this.getSQLTypeFromJsType(Number(value), TYPES);\n      }\n    } else if (typeof value === 'boolean') {\n      paramType.type = TYPES.Bit;\n    }\n    if (Buffer.isBuffer(value)) {\n      paramType.type = TYPES.VarBinary;\n    }\n    return paramType;\n  }\n\n  async _run(connection, sql, parameters, errStack) {\n    this.sql = sql;\n    const { options } = this;\n\n    const complete = this._logQuery(sql, debug, parameters);\n\n    const query = new Promise((resolve, reject) => {\n      // TRANSACTION SUPPORT\n      if (sql.startsWith('BEGIN TRANSACTION')) {\n        return connection.beginTransaction(error => error ? reject(error) : resolve([]), options.transaction.name, connection.lib.ISOLATION_LEVEL[options.isolationLevel]);\n      }\n      if (sql.startsWith('COMMIT TRANSACTION')) {\n        return connection.commitTransaction(error => error ? reject(error) : resolve([]));\n      }\n      if (sql.startsWith('ROLLBACK TRANSACTION')) {\n        return connection.rollbackTransaction(error => error ? reject(error) : resolve([]), options.transaction.name);\n      }\n      if (sql.startsWith('SAVE TRANSACTION')) {\n        return connection.saveTransaction(error => error ? reject(error) : resolve([]), options.transaction.name);\n      }\n\n      const rows = [];\n      const request = new connection.lib.Request(sql, (err, rowCount) => err ? reject(err) : resolve([rows, rowCount]));\n\n      if (parameters) {\n        _.forOwn(parameters, (value, key) => {\n          const paramType = this.getSQLTypeFromJsType(value, connection.lib.TYPES);\n          request.addParameter(key, paramType.type, value, paramType.typeOptions);\n        });\n      }\n\n      request.on('row', columns => {\n        rows.push(columns);\n      });\n\n      connection.execSql(request);\n    });\n\n    let rows, rowCount;\n\n    try {\n      [rows, rowCount] = await query;\n    } catch (err) {\n      err.sql = sql;\n      err.parameters = parameters;\n\n      throw this.formatError(err, errStack);\n    }\n\n    complete();\n\n    if (Array.isArray(rows)) {\n      rows = rows.map(columns => {\n        const row = {};\n        for (const column of columns) {\n          const typeid = column.metadata.type.id;\n          const parse = parserStore.get(typeid);\n          let value = column.value;\n\n          if (value !== null & !!parse) {\n            value = parse(value);\n          }\n          row[column.metadata.colName] = value;\n        }\n        return row;\n      });\n    }\n\n    return this.formatResults(rows, rowCount);\n  }\n\n  run(sql, parameters) {\n    const errForStack = new Error();\n    return this.connection.queue.enqueue(() =>\n      this._run(this.connection, sql, parameters, errForStack.stack)\n    );\n  }\n\n  static formatBindParameters(sql, values, dialect) {\n    const bindParam = {};\n    const replacementFunc = (match, key, values) => {\n      if (values[key] !== undefined) {\n        bindParam[key] = values[key];\n        return `@${key}`;\n      }\n      return undefined;\n    };\n    sql = AbstractQuery.formatBindParameters(sql, values, dialect, replacementFunc)[0];\n\n    return [sql, bindParam];\n  }\n\n  /**\n   * High level function that handles the results of a query execution.\n   *\n   * @param {Array} data - The result of the query execution.\n   * @param {number} rowCount\n   * @private\n   * @example\n   * Example:\n   *  query.formatResults([\n   *    {\n   *      id: 1,              // this is from the main table\n   *      attr2: 'snafu',     // this is from the main table\n   *      Tasks.id: 1,        // this is from the associated table\n   *      Tasks.title: 'task' // this is from the associated table\n   *    }\n   *  ])\n   */\n  formatResults(data, rowCount) {\n    if (this.isInsertQuery(data)) {\n      this.handleInsertQuery(data);\n      return [this.instance || data, rowCount];\n    }\n    if (this.isShowTablesQuery()) {\n      return this.handleShowTablesQuery(data);\n    }\n    if (this.isDescribeQuery()) {\n      const result = {};\n      for (const _result of data) {\n        if (_result.Default) {\n          _result.Default = _result.Default.replace(\"('\", '').replace(\"')\", '').replace(/'/g, '');\n        }\n\n        result[_result.Name] = {\n          type: _result.Type.toUpperCase(),\n          allowNull: _result.IsNull === 'YES' ? true : false,\n          defaultValue: _result.Default,\n          primaryKey: _result.Constraint === 'PRIMARY KEY',\n          autoIncrement: _result.IsIdentity === 1,\n          comment: _result.Comment\n        };\n\n        if (\n          result[_result.Name].type.includes('CHAR')\n          && _result.Length\n        ) {\n          if (_result.Length === -1) {\n            result[_result.Name].type += '(MAX)';\n          } else {\n            result[_result.Name].type += `(${_result.Length})`;\n          }\n        }\n      }\n      return result;\n    }\n    if (this.isSelectQuery()) {\n      return this.handleSelectQuery(data);\n    }\n    if (this.isShowIndexesQuery()) {\n      return this.handleShowIndexesQuery(data);\n    }\n    if (this.isCallQuery()) {\n      return data[0];\n    }\n    if (this.isBulkUpdateQuery()) {\n      if (this.options.returning) {\n        return this.handleSelectQuery(data);\n      }\n\n      return rowCount;\n    }\n    if (this.isBulkDeleteQuery()) {\n      return data[0] ? data[0].AFFECTEDROWS : 0;\n    }\n    if (this.isVersionQuery()) {\n      return data[0].version;\n    }\n    if (this.isForeignKeysQuery()) {\n      return data;\n    }\n    if (this.isUpsertQuery()) {\n      // if this was an upsert and no data came back, that means the record exists, but the update was a noop.\n      // return the current instance and mark it as an \"not an insert\".\n      if (data && data.length === 0) {\n        return [this.instance || data, false];\n      }\n      this.handleInsertQuery(data);\n      return [this.instance || data, data[0].$action === 'INSERT'];\n    }\n    if (this.isUpdateQuery()) {\n      return [this.instance || data, rowCount];\n    }\n    if (this.isShowConstraintsQuery()) {\n      return this.handleShowConstraintsQuery(data);\n    }\n    if (this.isRawQuery()) {\n      return [data, rowCount];\n    }\n    return data;\n  }\n\n  handleShowTablesQuery(results) {\n    return results.map(resultSet => {\n      return {\n        tableName: resultSet.TABLE_NAME,\n        schema: resultSet.TABLE_SCHEMA\n      };\n    });\n  }\n\n  handleShowConstraintsQuery(data) {\n    //Convert snake_case keys to camelCase as it's generated by stored procedure\n    return data.slice(1).map(result => {\n      const constraint = {};\n      for (const key in result) {\n        constraint[_.camelCase(key)] = result[key];\n      }\n      return constraint;\n    });\n  }\n\n  formatError(err, errStack) {\n    let match;\n\n    match = err.message.match(/Violation of (?:UNIQUE|PRIMARY) KEY constraint '([^']*)'. Cannot insert duplicate key in object '.*'.(:? The duplicate key value is \\((.*)\\).)?/);\n    match = match || err.message.match(/Cannot insert duplicate key row in object .* with unique index '(.*)'/);\n    if (match && match.length > 1) {\n      let fields = {};\n      const uniqueKey = this.model && this.model.uniqueKeys[match[1]];\n      let message = 'Validation error';\n\n      if (uniqueKey && !!uniqueKey.msg) {\n        message = uniqueKey.msg;\n      }\n      if (match[3]) {\n        const values = match[3].split(',').map(part => part.trim());\n        if (uniqueKey) {\n          fields = _.zipObject(uniqueKey.fields, values);\n        } else {\n          fields[match[1]] = match[3];\n        }\n      }\n\n      const errors = [];\n      _.forOwn(fields, (value, field) => {\n        errors.push(new sequelizeErrors.ValidationErrorItem(\n          this.getUniqueConstraintErrorMessage(field),\n          'unique violation', // sequelizeErrors.ValidationErrorItem.Origins.DB,\n          field,\n          value,\n          this.instance,\n          'not_unique'\n        ));\n      });\n\n      return new sequelizeErrors.UniqueConstraintError({ message, errors, parent: err, fields, stack: errStack });\n    }\n\n    match = err.message.match(/Failed on step '(.*)'.Could not create constraint. See previous errors./) ||\n      err.message.match(/The DELETE statement conflicted with the REFERENCE constraint \"(.*)\". The conflict occurred in database \"(.*)\", table \"(.*)\", column '(.*)'./) ||\n      err.message.match(/The (?:INSERT|MERGE|UPDATE) statement conflicted with the FOREIGN KEY constraint \"(.*)\". The conflict occurred in database \"(.*)\", table \"(.*)\", column '(.*)'./);\n    if (match && match.length > 0) {\n      return new sequelizeErrors.ForeignKeyConstraintError({\n        fields: null,\n        index: match[1],\n        parent: err,\n        stack: errStack\n      });\n    }\n\n    match = err.message.match(/Could not drop constraint. See previous errors./);\n    if (match && match.length > 0) {\n      let constraint = err.sql.match(/(?:constraint|index) \\[(.+?)\\]/i);\n      constraint = constraint ? constraint[1] : undefined;\n      let table = err.sql.match(/table \\[(.+?)\\]/i);\n      table = table ? table[1] : undefined;\n\n      return new sequelizeErrors.UnknownConstraintError({\n        message: match[1],\n        constraint,\n        table,\n        parent: err,\n        stack: errStack\n      });\n    }\n\n    return new sequelizeErrors.DatabaseError(err, { stack: errStack });\n  }\n\n  isShowOrDescribeQuery() {\n    let result = false;\n\n    result = result || this.sql.toLowerCase().startsWith(\"select c.column_name as 'name', c.data_type as 'type', c.is_nullable as 'isnull'\");\n    result = result || this.sql.toLowerCase().startsWith('select tablename = t.name, name = ind.name,');\n    result = result || this.sql.toLowerCase().startsWith('exec sys.sp_helpindex @objname');\n\n    return result;\n  }\n\n  isShowIndexesQuery() {\n    return this.sql.toLowerCase().startsWith('exec sys.sp_helpindex @objname');\n  }\n\n  handleShowIndexesQuery(data) {\n    // Group by index name, and collect all fields\n    data = data.reduce((acc, item) => {\n      if (!(item.index_name in acc)) {\n        acc[item.index_name] = item;\n        item.fields = [];\n      }\n\n      item.index_keys.split(',').forEach(column => {\n        let columnName = column.trim();\n        if (columnName.includes('(-)')) {\n          columnName = columnName.replace('(-)', '');\n        }\n\n        acc[item.index_name].fields.push({\n          attribute: columnName,\n          length: undefined,\n          order: column.includes('(-)') ? 'DESC' : 'ASC',\n          collate: undefined\n        });\n      });\n      delete item.index_keys;\n      return acc;\n    }, {});\n\n    return _.map(data, item => ({\n      primary: item.index_name.toLowerCase().startsWith('pk'),\n      fields: item.fields,\n      name: item.index_name,\n      tableName: undefined,\n      unique: item.index_description.toLowerCase().includes('unique'),\n      type: undefined\n    }));\n  }\n\n  handleInsertQuery(results, metaData) {\n    if (this.instance) {\n      // add the inserted row id to the instance\n      const autoIncrementAttribute = this.model.autoIncrementAttribute;\n      let id = null;\n      let autoIncrementAttributeAlias = null;\n\n      if (Object.prototype.hasOwnProperty.call(this.model.rawAttributes, autoIncrementAttribute) &&\n        this.model.rawAttributes[autoIncrementAttribute].field !== undefined)\n        autoIncrementAttributeAlias = this.model.rawAttributes[autoIncrementAttribute].field;\n\n      id = id || results && results[0][this.getInsertIdField()];\n      id = id || metaData && metaData[this.getInsertIdField()];\n      id = id || results && results[0][autoIncrementAttribute];\n      id = id || autoIncrementAttributeAlias && results && results[0][autoIncrementAttributeAlias];\n\n      this.instance[autoIncrementAttribute] = id;\n\n      if (this.instance.dataValues) {\n        for (const key in results[0]) {\n          if (Object.prototype.hasOwnProperty.call(results[0], key)) {\n            const record = results[0][key];\n\n            const attr = _.find(this.model.rawAttributes, attribute => attribute.fieldName === key || attribute.field === key);\n\n            this.instance.dataValues[attr && attr.fieldName || key] = record;\n          }\n        }\n      }\n\n    }\n  }\n}\n\nmodule.exports = Query;\nmodule.exports.Query = Query;\nmodule.exports.default = Query;\n"], "mappings": ";AAEA,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,IAAI,QAAQ;AAClB,MAAM,EAAE,WAAW,QAAQ;AAE3B,MAAM,QAAQ,OAAO,aAAa;AAElC,MAAM,yBAAyB,OAAO,OAAO;AAC7C,MAAM,yBAAyB,OAAO,OAAO;AAE7C,kBAAkB,MAAM;AACtB,MAAI,CAAC,OAAO,SAAS;AAAO,WAAO;AACnC,MAAI,IAAI;AACR,SAAO,KAAK,MAAM,OAAO,KAAK,MAAM;AAAM,SAAK;AAC/C,SAAO,KAAK,MAAM;AAAA;AAGpB,oBAAoB,cAAc;AAAA,EAChC,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,qBAAqB,OAAO,OAAO;AACjC,UAAM,YAAY,EAAE,MAAM,MAAM,UAAU,aAAa,IAAI;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,OAAO,UAAU,QAAQ;AAC3B,YAAI,SAAS,eAAe,SAAS,YAAY;AAC/C,oBAAU,OAAO,MAAM;AAAA,eAClB;AACL,oBAAU,OAAO,MAAM;AAAA;AAAA,aAEpB;AACL,kBAAU,OAAO,MAAM;AAEvB,kBAAU,cAAc,EAAE,WAAW,IAAI,OAAO,SAAS;AAAA;AAAA,eAElD,OAAO,UAAU,UAAU;AACpC,UAAI,QAAQ,0BAA0B,QAAQ,wBAAwB;AACpE,kBAAU,OAAO,MAAM;AACvB,kBAAU,QAAQ,MAAM;AAAA,aACnB;AACL,eAAO,KAAK,qBAAqB,OAAO,QAAQ;AAAA;AAAA,eAEzC,OAAO,UAAU,WAAW;AACrC,gBAAU,OAAO,MAAM;AAAA;AAEzB,QAAI,OAAO,SAAS,QAAQ;AAC1B,gBAAU,OAAO,MAAM;AAAA;AAEzB,WAAO;AAAA;AAAA,QAGH,KAAK,YAAY,KAAK,YAAY,UAAU;AAChD,SAAK,MAAM;AACX,UAAM,EAAE,YAAY;AAEpB,UAAM,WAAW,KAAK,UAAU,KAAK,OAAO;AAE5C,UAAM,QAAQ,IAAI,QAAQ,CAAC,SAAS,WAAW;AAE7C,UAAI,IAAI,WAAW,sBAAsB;AACvC,eAAO,WAAW,iBAAiB,WAAS,QAAQ,OAAO,SAAS,QAAQ,KAAK,QAAQ,YAAY,MAAM,WAAW,IAAI,gBAAgB,QAAQ;AAAA;AAEpJ,UAAI,IAAI,WAAW,uBAAuB;AACxC,eAAO,WAAW,kBAAkB,WAAS,QAAQ,OAAO,SAAS,QAAQ;AAAA;AAE/E,UAAI,IAAI,WAAW,yBAAyB;AAC1C,eAAO,WAAW,oBAAoB,WAAS,QAAQ,OAAO,SAAS,QAAQ,KAAK,QAAQ,YAAY;AAAA;AAE1G,UAAI,IAAI,WAAW,qBAAqB;AACtC,eAAO,WAAW,gBAAgB,WAAS,QAAQ,OAAO,SAAS,QAAQ,KAAK,QAAQ,YAAY;AAAA;AAGtG,YAAM,QAAO;AACb,YAAM,UAAU,IAAI,WAAW,IAAI,QAAQ,KAAK,CAAC,KAAK,cAAa,MAAM,OAAO,OAAO,QAAQ,CAAC,OAAM;AAEtG,UAAI,YAAY;AACd,UAAE,OAAO,YAAY,CAAC,OAAO,QAAQ;AACnC,gBAAM,YAAY,KAAK,qBAAqB,OAAO,WAAW,IAAI;AAClE,kBAAQ,aAAa,KAAK,UAAU,MAAM,OAAO,UAAU;AAAA;AAAA;AAI/D,cAAQ,GAAG,OAAO,aAAW;AAC3B,cAAK,KAAK;AAAA;AAGZ,iBAAW,QAAQ;AAAA;AAGrB,QAAI,MAAM;AAEV,QAAI;AACF,OAAC,MAAM,YAAY,MAAM;AAAA,aAClB,KAAP;AACA,UAAI,MAAM;AACV,UAAI,aAAa;AAEjB,YAAM,KAAK,YAAY,KAAK;AAAA;AAG9B;AAEA,QAAI,MAAM,QAAQ,OAAO;AACvB,aAAO,KAAK,IAAI,aAAW;AACzB,cAAM,MAAM;AACZ,mBAAW,UAAU,SAAS;AAC5B,gBAAM,SAAS,OAAO,SAAS,KAAK;AACpC,gBAAM,QAAQ,YAAY,IAAI;AAC9B,cAAI,QAAQ,OAAO;AAEnB,cAAI,UAAU,OAAO,CAAC,CAAC,OAAO;AAC5B,oBAAQ,MAAM;AAAA;AAEhB,cAAI,OAAO,SAAS,WAAW;AAAA;AAEjC,eAAO;AAAA;AAAA;AAIX,WAAO,KAAK,cAAc,MAAM;AAAA;AAAA,EAGlC,IAAI,KAAK,YAAY;AACnB,UAAM,cAAc,IAAI;AACxB,WAAO,KAAK,WAAW,MAAM,QAAQ,MACnC,KAAK,KAAK,KAAK,YAAY,KAAK,YAAY,YAAY;AAAA;AAAA,SAIrD,qBAAqB,KAAK,QAAQ,SAAS;AAChD,UAAM,YAAY;AAClB,UAAM,kBAAkB,CAAC,OAAO,KAAK,YAAW;AAC9C,UAAI,QAAO,SAAS,QAAW;AAC7B,kBAAU,OAAO,QAAO;AACxB,eAAO,IAAI;AAAA;AAEb,aAAO;AAAA;AAET,UAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,iBAAiB;AAEhF,WAAO,CAAC,KAAK;AAAA;AAAA,EAoBf,cAAc,MAAM,UAAU;AAC5B,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,kBAAkB;AACvB,aAAO,CAAC,KAAK,YAAY,MAAM;AAAA;AAEjC,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,sBAAsB;AAAA;AAEpC,QAAI,KAAK,mBAAmB;AAC1B,YAAM,SAAS;AACf,iBAAW,WAAW,MAAM;AAC1B,YAAI,QAAQ,SAAS;AACnB,kBAAQ,UAAU,QAAQ,QAAQ,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;AAAA;AAGtF,eAAO,QAAQ,QAAQ;AAAA,UACrB,MAAM,QAAQ,KAAK;AAAA,UACnB,WAAW,QAAQ,WAAW,QAAQ,OAAO;AAAA,UAC7C,cAAc,QAAQ;AAAA,UACtB,YAAY,QAAQ,eAAe;AAAA,UACnC,eAAe,QAAQ,eAAe;AAAA,UACtC,SAAS,QAAQ;AAAA;AAGnB,YACE,OAAO,QAAQ,MAAM,KAAK,SAAS,WAChC,QAAQ,QACX;AACA,cAAI,QAAQ,WAAW,IAAI;AACzB,mBAAO,QAAQ,MAAM,QAAQ;AAAA,iBACxB;AACL,mBAAO,QAAQ,MAAM,QAAQ,IAAI,QAAQ;AAAA;AAAA;AAAA;AAI/C,aAAO;AAAA;AAET,QAAI,KAAK,iBAAiB;AACxB,aAAO,KAAK,kBAAkB;AAAA;AAEhC,QAAI,KAAK,sBAAsB;AAC7B,aAAO,KAAK,uBAAuB;AAAA;AAErC,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA;AAEd,QAAI,KAAK,qBAAqB;AAC5B,UAAI,KAAK,QAAQ,WAAW;AAC1B,eAAO,KAAK,kBAAkB;AAAA;AAGhC,aAAO;AAAA;AAET,QAAI,KAAK,qBAAqB;AAC5B,aAAO,KAAK,KAAK,KAAK,GAAG,eAAe;AAAA;AAE1C,QAAI,KAAK,kBAAkB;AACzB,aAAO,KAAK,GAAG;AAAA;AAEjB,QAAI,KAAK,sBAAsB;AAC7B,aAAO;AAAA;AAET,QAAI,KAAK,iBAAiB;AAGxB,UAAI,QAAQ,KAAK,WAAW,GAAG;AAC7B,eAAO,CAAC,KAAK,YAAY,MAAM;AAAA;AAEjC,WAAK,kBAAkB;AACvB,aAAO,CAAC,KAAK,YAAY,MAAM,KAAK,GAAG,YAAY;AAAA;AAErD,QAAI,KAAK,iBAAiB;AACxB,aAAO,CAAC,KAAK,YAAY,MAAM;AAAA;AAEjC,QAAI,KAAK,0BAA0B;AACjC,aAAO,KAAK,2BAA2B;AAAA;AAEzC,QAAI,KAAK,cAAc;AACrB,aAAO,CAAC,MAAM;AAAA;AAEhB,WAAO;AAAA;AAAA,EAGT,sBAAsB,SAAS;AAC7B,WAAO,QAAQ,IAAI,eAAa;AAC9B,aAAO;AAAA,QACL,WAAW,UAAU;AAAA,QACrB,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA,EAKxB,2BAA2B,MAAM;AAE/B,WAAO,KAAK,MAAM,GAAG,IAAI,YAAU;AACjC,YAAM,aAAa;AACnB,iBAAW,OAAO,QAAQ;AACxB,mBAAW,EAAE,UAAU,QAAQ,OAAO;AAAA;AAExC,aAAO;AAAA;AAAA;AAAA,EAIX,YAAY,KAAK,UAAU;AACzB,QAAI;AAEJ,YAAQ,IAAI,QAAQ,MAAM;AAC1B,YAAQ,SAAS,IAAI,QAAQ,MAAM;AACnC,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,UAAI,SAAS;AACb,YAAM,YAAY,KAAK,SAAS,KAAK,MAAM,WAAW,MAAM;AAC5D,UAAI,UAAU;AAEd,UAAI,aAAa,CAAC,CAAC,UAAU,KAAK;AAChC,kBAAU,UAAU;AAAA;AAEtB,UAAI,MAAM,IAAI;AACZ,cAAM,SAAS,MAAM,GAAG,MAAM,KAAK,IAAI,UAAQ,KAAK;AACpD,YAAI,WAAW;AACb,mBAAS,EAAE,UAAU,UAAU,QAAQ;AAAA,eAClC;AACL,iBAAO,MAAM,MAAM,MAAM;AAAA;AAAA;AAI7B,YAAM,SAAS;AACf,QAAE,OAAO,QAAQ,CAAC,OAAO,UAAU;AACjC,eAAO,KAAK,IAAI,gBAAgB,oBAC9B,KAAK,gCAAgC,QACrC,oBACA,OACA,OACA,KAAK,UACL;AAAA;AAIJ,aAAO,IAAI,gBAAgB,sBAAsB,EAAE,SAAS,QAAQ,QAAQ,KAAK,QAAQ,OAAO;AAAA;AAGlG,YAAQ,IAAI,QAAQ,MAAM,8EACxB,IAAI,QAAQ,MAAM,mJAClB,IAAI,QAAQ,MAAM;AACpB,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,aAAO,IAAI,gBAAgB,0BAA0B;AAAA,QACnD,QAAQ;AAAA,QACR,OAAO,MAAM;AAAA,QACb,QAAQ;AAAA,QACR,OAAO;AAAA;AAAA;AAIX,YAAQ,IAAI,QAAQ,MAAM;AAC1B,QAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,UAAI,aAAa,IAAI,IAAI,MAAM;AAC/B,mBAAa,aAAa,WAAW,KAAK;AAC1C,UAAI,QAAQ,IAAI,IAAI,MAAM;AAC1B,cAAQ,QAAQ,MAAM,KAAK;AAE3B,aAAO,IAAI,gBAAgB,uBAAuB;AAAA,QAChD,SAAS,MAAM;AAAA,QACf;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,OAAO;AAAA;AAAA;AAIX,WAAO,IAAI,gBAAgB,cAAc,KAAK,EAAE,OAAO;AAAA;AAAA,EAGzD,wBAAwB;AACtB,QAAI,SAAS;AAEb,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AACrD,aAAS,UAAU,KAAK,IAAI,cAAc,WAAW;AAErD,WAAO;AAAA;AAAA,EAGT,qBAAqB;AACnB,WAAO,KAAK,IAAI,cAAc,WAAW;AAAA;AAAA,EAG3C,uBAAuB,MAAM;AAE3B,WAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAChC,UAAI,CAAE,MAAK,cAAc,MAAM;AAC7B,YAAI,KAAK,cAAc;AACvB,aAAK,SAAS;AAAA;AAGhB,WAAK,WAAW,MAAM,KAAK,QAAQ,YAAU;AAC3C,YAAI,aAAa,OAAO;AACxB,YAAI,WAAW,SAAS,QAAQ;AAC9B,uBAAa,WAAW,QAAQ,OAAO;AAAA;AAGzC,YAAI,KAAK,YAAY,OAAO,KAAK;AAAA,UAC/B,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,OAAO,OAAO,SAAS,SAAS,SAAS;AAAA,UACzC,SAAS;AAAA;AAAA;AAGb,aAAO,KAAK;AACZ,aAAO;AAAA,OACN;AAEH,WAAO,EAAE,IAAI,MAAM,UAAS;AAAA,MAC1B,SAAS,KAAK,WAAW,cAAc,WAAW;AAAA,MAClD,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,MACX,WAAW;AAAA,MACX,QAAQ,KAAK,kBAAkB,cAAc,SAAS;AAAA,MACtD,MAAM;AAAA;AAAA;AAAA,EAIV,kBAAkB,SAAS,UAAU;AACnC,QAAI,KAAK,UAAU;AAEjB,YAAM,yBAAyB,KAAK,MAAM;AAC1C,UAAI,KAAK;AACT,UAAI,8BAA8B;AAElC,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,MAAM,eAAe,2BACjE,KAAK,MAAM,cAAc,wBAAwB,UAAU;AAC3D,sCAA8B,KAAK,MAAM,cAAc,wBAAwB;AAEjF,WAAK,MAAM,WAAW,QAAQ,GAAG,KAAK;AACtC,WAAK,MAAM,YAAY,SAAS,KAAK;AACrC,WAAK,MAAM,WAAW,QAAQ,GAAG;AACjC,WAAK,MAAM,+BAA+B,WAAW,QAAQ,GAAG;AAEhE,WAAK,SAAS,0BAA0B;AAExC,UAAI,KAAK,SAAS,YAAY;AAC5B,mBAAW,OAAO,QAAQ,IAAI;AAC5B,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,IAAI,MAAM;AACzD,kBAAM,SAAS,QAAQ,GAAG;AAE1B,kBAAM,OAAO,EAAE,KAAK,KAAK,MAAM,eAAe,eAAa,UAAU,cAAc,OAAO,UAAU,UAAU;AAE9G,iBAAK,SAAS,WAAW,QAAQ,KAAK,aAAa,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAStE,OAAO,UAAU;AACjB,OAAO,QAAQ,QAAQ;AACvB,OAAO,QAAQ,UAAU;", "names": []}