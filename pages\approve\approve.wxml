
<view class="container">
  <view class="page-title">用车审批</view>
  
  <view class="approval-list">
    <block wx:if="{{pendingList.length > 0}}">
      <view class="approval-item" wx:for="{{pendingList}}" wx:key="id">
        <view class="approval-header">
          <view class="applicant">{{item.applicant}}</view>
          <view class="department">{{item.department}}</view>
        </view>
        
        <view class="approval-content">
          <view class="info-row">
            <view class="info-label">用车目的：</view>
            <view class="info-value">{{item.purpose}}</view>
          </view>
          <view class="info-row">
            <view class="info-label">用车时间：</view>
            <view class="info-value">{{item.startTime}} 至 {{item.endTime}}</view>
          </view>
          <view class="info-row">
            <view class="info-label">目的地：</view>
            <view class="info-value">{{item.destination}}</view>
          </view>
        </view>
        
        <view class="approval-actions" wx:if="{{item.status === 'pending'}}">
          <button class="action-btn approve" data-id="{{item.id}}" bindtap="approveApplication">批准</button>
          <button class="action-btn reject" data-id="{{item.id}}" bindtap="rejectApplication">拒绝</button>
        </view>
        
        <view class="approval-status" wx:else>
          <text class="status {{item.status}}">{{item.status === 'approved' ? '已批准' : '已拒绝'}}</text>
        </view>
      </view>
    </block>
    
    <view class="empty-list" wx:else>
      <text>暂无待审批的用车申请</text>
    </view>
  </view>
</view>