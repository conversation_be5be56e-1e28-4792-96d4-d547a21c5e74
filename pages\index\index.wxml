<!--index.wxml-->
<view class="container">
  <view class="userinfo">
    <button wx:if="{{!hasUserInfo && canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo"> 获取头像昵称 </button>
    <block wx:else>
      <view bindtap="bindViewTap" class="userinfo-avatar">
        <text>{{userInfo.nickName ? userInfo.nickName.charAt(0) : '?'}}</text>
      </view>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
  </view>
  <view class="function-list">
    <block wx:for="{{functions}}" wx:key="name">
      <view class="function-item" bindtap="navigateTo" data-url="{{item.url}}">
        <view class="iconfont icon-{{item.icon}}"></view>
        <text>{{item.name}}</text>
      </view>
    </block>
  </view>
</view>