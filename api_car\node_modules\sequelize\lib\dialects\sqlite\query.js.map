{"version": 3, "sources": ["../../../src/dialects/sqlite/query.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst AbstractQuery = require('../abstract/query');\nconst QueryTypes = require('../../query-types');\nconst sequelizeErrors = require('../../errors');\nconst parserStore = require('../parserStore')('sqlite');\nconst { logger } = require('../../utils/logger');\n\nconst debug = logger.debugContext('sql:sqlite');\n\n// sqlite3 currently ignores bigint values, so we have to translate to string for now\n// There's a WIP here: https://github.com/TryGhost/node-sqlite3/pull/1501\nfunction stringifyIfBigint(value) {\n  if (typeof value === 'bigint') {\n    return value.toString();\n  }\n\n  return value;\n}\n\nclass Query extends AbstractQuery {\n  getInsertIdField() {\n    return 'lastID';\n  }\n\n  /**\n   * rewrite query with parameters.\n   *\n   * @param {string} sql\n   * @param {Array|object} values\n   * @param {string} dialect\n   * @private\n   */\n  static formatBindParameters(sql, values, dialect) {\n    let bindParam;\n    if (Array.isArray(values)) {\n      bindParam = {};\n      values.forEach((v, i) => {\n        bindParam[`$${i + 1}`] = v;\n      });\n      sql = AbstractQuery.formatBindParameters(sql, values, dialect, { skipValueReplace: true })[0];\n    } else {\n      bindParam = {};\n      if (typeof values === 'object') {\n        for (const k of Object.keys(values)) {\n          bindParam[`$${k}`] = values[k];\n        }\n      }\n      sql = AbstractQuery.formatBindParameters(sql, values, dialect, { skipValueReplace: true })[0];\n    }\n    return [sql, bindParam];\n  }\n\n  _collectModels(include, prefix) {\n    const ret = {};\n\n    if (include) {\n      for (const _include of include) {\n        let key;\n        if (!prefix) {\n          key = _include.as;\n        } else {\n          key = `${prefix}.${_include.as}`;\n        }\n        ret[key] = _include.model;\n\n        if (_include.include) {\n          _.merge(ret, this._collectModels(_include.include, key));\n        }\n      }\n    }\n\n    return ret;\n  }\n\n  _handleQueryResponse(metaData, columnTypes, err, results, errStack) {\n    if (err) {\n      err.sql = this.sql;\n      throw this.formatError(err, errStack);\n    }\n    let result = this.instance;\n\n    // add the inserted row id to the instance\n    if (this.isInsertQuery(results, metaData) || this.isUpsertQuery()) {\n      this.handleInsertQuery(results, metaData);\n      if (!this.instance) {\n        // handle bulkCreate AI primary key\n        if (\n          metaData.constructor.name === 'Statement'\n          && this.model\n          && this.model.autoIncrementAttribute\n          && this.model.autoIncrementAttribute === this.model.primaryKeyAttribute\n          && this.model.rawAttributes[this.model.primaryKeyAttribute]\n        ) {\n          const startId = metaData[this.getInsertIdField()] - metaData.changes + 1;\n          result = [];\n          for (let i = startId; i < startId + metaData.changes; i++) {\n            result.push({ [this.model.rawAttributes[this.model.primaryKeyAttribute].field]: i });\n          }\n        } else {\n          result = metaData[this.getInsertIdField()];\n        }\n      }\n    }\n\n    if (this.isShowTablesQuery()) {\n      return results.map(row => row.name);\n    }\n    if (this.isShowConstraintsQuery()) {\n      result = results;\n      if (results && results[0] && results[0].sql) {\n        result = this.parseConstraintsFromSql(results[0].sql);\n      }\n      return result;\n    }\n    if (this.isSelectQuery()) {\n      if (this.options.raw) {\n        return this.handleSelectQuery(results);\n      }\n      // This is a map of prefix strings to models, e.g. user.projects -> Project model\n      const prefixes = this._collectModels(this.options.include);\n\n      results = results.map(result => {\n        return _.mapValues(result, (value, name) => {\n          let model;\n          if (name.includes('.')) {\n            const lastind = name.lastIndexOf('.');\n\n            model = prefixes[name.substr(0, lastind)];\n\n            name = name.substr(lastind + 1);\n          } else {\n            model = this.options.model;\n          }\n\n          const tableName = model.getTableName().toString().replace(/`/g, '');\n          const tableTypes = columnTypes[tableName] || {};\n\n          if (tableTypes && !(name in tableTypes)) {\n            // The column is aliased\n            _.forOwn(model.rawAttributes, (attribute, key) => {\n              if (name === key && attribute.field) {\n                name = attribute.field;\n                return false;\n              }\n            });\n          }\n\n          return Object.prototype.hasOwnProperty.call(tableTypes, name)\n            ? this.applyParsers(tableTypes[name], value)\n            : value;\n        });\n      });\n\n      return this.handleSelectQuery(results);\n    }\n    if (this.isShowOrDescribeQuery()) {\n      return results;\n    }\n    if (this.sql.includes('PRAGMA INDEX_LIST')) {\n      return this.handleShowIndexesQuery(results);\n    }\n    if (this.sql.includes('PRAGMA INDEX_INFO')) {\n      return results;\n    }\n    if (this.sql.includes('PRAGMA TABLE_INFO')) {\n      // this is the sqlite way of getting the metadata of a table\n      result = {};\n\n      let defaultValue;\n      for (const _result of results) {\n        if (_result.dflt_value === null) {\n          // Column schema omits any \"DEFAULT ...\"\n          defaultValue = undefined;\n        } else if (_result.dflt_value === 'NULL') {\n          // Column schema is a \"DEFAULT NULL\"\n          defaultValue = null;\n        } else {\n          defaultValue = _result.dflt_value;\n        }\n\n        result[_result.name] = {\n          type: _result.type,\n          allowNull: _result.notnull === 0,\n          defaultValue,\n          primaryKey: _result.pk !== 0\n        };\n\n        if (result[_result.name].type === 'TINYINT(1)') {\n          result[_result.name].defaultValue = { '0': false, '1': true }[result[_result.name].defaultValue];\n        }\n\n        if (typeof result[_result.name].defaultValue === 'string') {\n          result[_result.name].defaultValue = result[_result.name].defaultValue.replace(/'/g, '');\n        }\n      }\n      return result;\n    }\n    if (this.sql.includes('PRAGMA foreign_keys;')) {\n      return results[0];\n    }\n    if (this.sql.includes('PRAGMA foreign_keys')) {\n      return results;\n    }\n    if (this.sql.includes('PRAGMA foreign_key_list')) {\n      return results;\n    }\n    if ([QueryTypes.BULKUPDATE, QueryTypes.BULKDELETE].includes(this.options.type)) {\n      return metaData.changes;\n    }\n    if (this.options.type === QueryTypes.VERSION) {\n      return results[0].version;\n    }\n    if (this.options.type === QueryTypes.RAW) {\n      return [results, metaData];\n    }\n    if (this.isUpsertQuery()) {\n      return [result, null];\n    }\n    if (this.isUpdateQuery() || this.isInsertQuery()) {\n      return [result, metaData.changes];\n    }\n    return result;\n  }\n\n  async run(sql, parameters) {\n    const conn = this.connection;\n    this.sql = sql;\n    const method = this.getDatabaseMethod();\n    const complete = this._logQuery(sql, debug, parameters);\n\n    return new Promise((resolve, reject) => conn.serialize(async () => {\n      const columnTypes = {};\n      const errForStack = new Error();\n      const executeSql = () => {\n        if (sql.startsWith('-- ')) {\n          return resolve();\n        }\n        const query = this;\n        // cannot use arrow function here because the function is bound to the statement\n        function afterExecute(executionError, results) {\n          try {\n            complete();\n            // `this` is passed from sqlite, we have no control over this.\n            // eslint-disable-next-line no-invalid-this\n            resolve(query._handleQueryResponse(this, columnTypes, executionError, results, errForStack.stack));\n            return;\n          } catch (error) {\n            reject(error);\n          }\n        }\n\n        if (!parameters) parameters = [];\n\n        if (_.isPlainObject(parameters)) {\n          const newParameters = Object.create(null);\n          for (const key of Object.keys(parameters)) {\n            newParameters[`${key}`] = stringifyIfBigint(parameters[key]);\n          }\n          parameters = newParameters;\n        } else {\n          parameters = parameters.map(stringifyIfBigint);\n        }\n\n        conn[method](sql, parameters, afterExecute);\n\n        return null;\n      };\n\n      if (this.getDatabaseMethod() === 'all') {\n        let tableNames = [];\n        if (this.options && this.options.tableNames) {\n          tableNames = this.options.tableNames;\n        } else if (/FROM `(.*?)`/i.exec(this.sql)) {\n          tableNames.push(/FROM `(.*?)`/i.exec(this.sql)[1]);\n        }\n\n        // If we already have the metadata for the table, there's no need to ask for it again\n        tableNames = tableNames.filter(tableName => !(tableName in columnTypes) && tableName !== 'sqlite_master');\n\n        if (!tableNames.length) {\n          return executeSql();\n        }\n        await Promise.all(tableNames.map(tableName =>\n          new Promise(resolve => {\n            tableName = tableName.replace(/`/g, '');\n            columnTypes[tableName] = {};\n\n            conn.all(`PRAGMA table_info(\\`${tableName}\\`)`, (err, results) => {\n              if (!err) {\n                for (const result of results) {\n                  columnTypes[tableName][result.name] = result.type;\n                }\n              }\n              resolve();\n            });\n          })));\n      }\n      return executeSql();\n    }));\n  }\n\n  parseConstraintsFromSql(sql) {\n    let constraints = sql.split('CONSTRAINT ');\n    let referenceTableName, referenceTableKeys, updateAction, deleteAction;\n    constraints.splice(0, 1);\n    constraints = constraints.map(constraintSql => {\n      //Parse foreign key snippets\n      if (constraintSql.includes('REFERENCES')) {\n        //Parse out the constraint condition form sql string\n        updateAction = constraintSql.match(/ON UPDATE (CASCADE|SET NULL|RESTRICT|NO ACTION|SET DEFAULT){1}/);\n        deleteAction = constraintSql.match(/ON DELETE (CASCADE|SET NULL|RESTRICT|NO ACTION|SET DEFAULT){1}/);\n\n        if (updateAction) {\n          updateAction = updateAction[1];\n        }\n\n        if (deleteAction) {\n          deleteAction = deleteAction[1];\n        }\n\n        const referencesRegex = /REFERENCES.+\\((?:[^)(]+|\\((?:[^)(]+|\\([^)(]*\\))*\\))*\\)/;\n        const referenceConditions = constraintSql.match(referencesRegex)[0].split(' ');\n        referenceTableName = Utils.removeTicks(referenceConditions[1]);\n        let columnNames = referenceConditions[2];\n        columnNames = columnNames.replace(/\\(|\\)/g, '').split(', ');\n        referenceTableKeys = columnNames.map(column => Utils.removeTicks(column));\n      }\n\n      const constraintCondition = constraintSql.match(/\\((?:[^)(]+|\\((?:[^)(]+|\\([^)(]*\\))*\\))*\\)/)[0];\n      constraintSql = constraintSql.replace(/\\(.+\\)/, '');\n      const constraint = constraintSql.split(' ');\n\n      if (['PRIMARY', 'FOREIGN'].includes(constraint[1])) {\n        constraint[1] += ' KEY';\n      }\n\n      return {\n        constraintName: Utils.removeTicks(constraint[0]),\n        constraintType: constraint[1],\n        updateAction,\n        deleteAction,\n        sql: sql.replace(/\"/g, '`'), //Sqlite returns double quotes for table name\n        constraintCondition,\n        referenceTableName,\n        referenceTableKeys\n      };\n    });\n\n    return constraints;\n  }\n\n  applyParsers(type, value) {\n    if (type.includes('(')) {\n      // Remove the length part\n      type = type.substr(0, type.indexOf('('));\n    }\n    type = type.replace('UNSIGNED', '').replace('ZEROFILL', '');\n    type = type.trim().toUpperCase();\n    const parse = parserStore.get(type);\n\n    if (value !== null && parse) {\n      return parse(value, { timezone: this.sequelize.options.timezone });\n    }\n    return value;\n  }\n\n  formatError(err, errStack) {\n\n    switch (err.code) {\n      case 'SQLITE_CONSTRAINT_UNIQUE':\n      case 'SQLITE_CONSTRAINT_PRIMARYKEY':\n      case 'SQLITE_CONSTRAINT_TRIGGER':\n      case 'SQLITE_CONSTRAINT_FOREIGNKEY':\n      case 'SQLITE_CONSTRAINT': {\n        if (err.message.includes('FOREIGN KEY constraint failed')) {\n          return new sequelizeErrors.ForeignKeyConstraintError({\n            parent: err,\n            stack: errStack\n          });\n        }\n\n        let fields = [];\n\n        // Sqlite pre 2.2 behavior - Error: SQLITE_CONSTRAINT: columns x, y are not unique\n        let match = err.message.match(/columns (.*?) are/);\n        if (match !== null && match.length >= 2) {\n          fields = match[1].split(', ');\n        } else {\n\n          // Sqlite post 2.2 behavior - Error: SQLITE_CONSTRAINT: UNIQUE constraint failed: table.x, table.y\n          match = err.message.match(/UNIQUE constraint failed: (.*)/);\n          if (match !== null && match.length >= 2) {\n            fields = match[1].split(', ').map(columnWithTable => columnWithTable.split('.')[1]);\n          }\n        }\n\n        const errors = [];\n        let message = 'Validation error';\n\n        for (const field of fields) {\n          errors.push(new sequelizeErrors.ValidationErrorItem(\n            this.getUniqueConstraintErrorMessage(field),\n            'unique violation', // sequelizeErrors.ValidationErrorItem.Origins.DB,\n            field,\n            this.instance && this.instance[field],\n            this.instance,\n            'not_unique'\n          ));\n        }\n\n        if (this.model) {\n          _.forOwn(this.model.uniqueKeys, constraint => {\n            if (_.isEqual(constraint.fields, fields) && !!constraint.msg) {\n              message = constraint.msg;\n              return false;\n            }\n          });\n        }\n\n        return new sequelizeErrors.UniqueConstraintError({ message, errors, parent: err, fields, stack: errStack });\n      }\n      case 'SQLITE_BUSY':\n        return new sequelizeErrors.TimeoutError(err, { stack: errStack });\n\n      default:\n        return new sequelizeErrors.DatabaseError(err, { stack: errStack });\n    }\n  }\n\n  async handleShowIndexesQuery(data) {\n    // Sqlite returns indexes so the one that was defined last is returned first. Lets reverse that!\n    return Promise.all(data.reverse().map(async item => {\n      item.fields = [];\n      item.primary = false;\n      item.unique = !!item.unique;\n      item.constraintName = item.name;\n      const columns = await this.run(`PRAGMA INDEX_INFO(\\`${item.name}\\`)`);\n      for (const column of columns) {\n        item.fields[column.seqno] = {\n          attribute: column.name,\n          length: undefined,\n          order: undefined\n        };\n      }\n\n      return item;\n    }));\n  }\n\n  getDatabaseMethod() {\n    if (this.isInsertQuery() || this.isUpdateQuery() || this.isUpsertQuery() || this.isBulkUpdateQuery() || this.sql.toLowerCase().includes('CREATE TEMPORARY TABLE'.toLowerCase()) || this.options.type === QueryTypes.BULKDELETE) {\n      return 'run';\n    }\n    return 'all';\n  }\n}\n\nmodule.exports = Query;\nmodule.exports.Query = Query;\nmodule.exports.default = Query;\n"], "mappings": ";AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,aAAa,QAAQ;AAC3B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ,kBAAkB;AAC9C,MAAM,EAAE,WAAW,QAAQ;AAE3B,MAAM,QAAQ,OAAO,aAAa;AAIlC,2BAA2B,OAAO;AAChC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,MAAM;AAAA;AAGf,SAAO;AAAA;AAGT,oBAAoB,cAAc;AAAA,EAChC,mBAAmB;AACjB,WAAO;AAAA;AAAA,SAWF,qBAAqB,KAAK,QAAQ,SAAS;AAChD,QAAI;AACJ,QAAI,MAAM,QAAQ,SAAS;AACzB,kBAAY;AACZ,aAAO,QAAQ,CAAC,GAAG,MAAM;AACvB,kBAAU,IAAI,IAAI,OAAO;AAAA;AAE3B,YAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,EAAE,kBAAkB,QAAQ;AAAA,WACtF;AACL,kBAAY;AACZ,UAAI,OAAO,WAAW,UAAU;AAC9B,mBAAW,KAAK,OAAO,KAAK,SAAS;AACnC,oBAAU,IAAI,OAAO,OAAO;AAAA;AAAA;AAGhC,YAAM,cAAc,qBAAqB,KAAK,QAAQ,SAAS,EAAE,kBAAkB,QAAQ;AAAA;AAE7F,WAAO,CAAC,KAAK;AAAA;AAAA,EAGf,eAAe,SAAS,QAAQ;AAC9B,UAAM,MAAM;AAEZ,QAAI,SAAS;AACX,iBAAW,YAAY,SAAS;AAC9B,YAAI;AACJ,YAAI,CAAC,QAAQ;AACX,gBAAM,SAAS;AAAA,eACV;AACL,gBAAM,GAAG,UAAU,SAAS;AAAA;AAE9B,YAAI,OAAO,SAAS;AAEpB,YAAI,SAAS,SAAS;AACpB,YAAE,MAAM,KAAK,KAAK,eAAe,SAAS,SAAS;AAAA;AAAA;AAAA;AAKzD,WAAO;AAAA;AAAA,EAGT,qBAAqB,UAAU,aAAa,KAAK,SAAS,UAAU;AAClE,QAAI,KAAK;AACP,UAAI,MAAM,KAAK;AACf,YAAM,KAAK,YAAY,KAAK;AAAA;AAE9B,QAAI,SAAS,KAAK;AAGlB,QAAI,KAAK,cAAc,SAAS,aAAa,KAAK,iBAAiB;AACjE,WAAK,kBAAkB,SAAS;AAChC,UAAI,CAAC,KAAK,UAAU;AAElB,YACE,SAAS,YAAY,SAAS,eAC3B,KAAK,SACL,KAAK,MAAM,0BACX,KAAK,MAAM,2BAA2B,KAAK,MAAM,uBACjD,KAAK,MAAM,cAAc,KAAK,MAAM,sBACvC;AACA,gBAAM,UAAU,SAAS,KAAK,sBAAsB,SAAS,UAAU;AACvE,mBAAS;AACT,mBAAS,IAAI,SAAS,IAAI,UAAU,SAAS,SAAS,KAAK;AACzD,mBAAO,KAAK,GAAG,KAAK,MAAM,cAAc,KAAK,MAAM,qBAAqB,QAAQ;AAAA;AAAA,eAE7E;AACL,mBAAS,SAAS,KAAK;AAAA;AAAA;AAAA;AAK7B,QAAI,KAAK,qBAAqB;AAC5B,aAAO,QAAQ,IAAI,SAAO,IAAI;AAAA;AAEhC,QAAI,KAAK,0BAA0B;AACjC,eAAS;AACT,UAAI,WAAW,QAAQ,MAAM,QAAQ,GAAG,KAAK;AAC3C,iBAAS,KAAK,wBAAwB,QAAQ,GAAG;AAAA;AAEnD,aAAO;AAAA;AAET,QAAI,KAAK,iBAAiB;AACxB,UAAI,KAAK,QAAQ,KAAK;AACpB,eAAO,KAAK,kBAAkB;AAAA;AAGhC,YAAM,WAAW,KAAK,eAAe,KAAK,QAAQ;AAElD,gBAAU,QAAQ,IAAI,aAAU;AAC9B,eAAO,EAAE,UAAU,SAAQ,CAAC,OAAO,SAAS;AAC1C,cAAI;AACJ,cAAI,KAAK,SAAS,MAAM;AACtB,kBAAM,UAAU,KAAK,YAAY;AAEjC,oBAAQ,SAAS,KAAK,OAAO,GAAG;AAEhC,mBAAO,KAAK,OAAO,UAAU;AAAA,iBACxB;AACL,oBAAQ,KAAK,QAAQ;AAAA;AAGvB,gBAAM,YAAY,MAAM,eAAe,WAAW,QAAQ,MAAM;AAChE,gBAAM,aAAa,YAAY,cAAc;AAE7C,cAAI,cAAc,CAAE,SAAQ,aAAa;AAEvC,cAAE,OAAO,MAAM,eAAe,CAAC,WAAW,QAAQ;AAChD,kBAAI,SAAS,OAAO,UAAU,OAAO;AACnC,uBAAO,UAAU;AACjB,uBAAO;AAAA;AAAA;AAAA;AAKb,iBAAO,OAAO,UAAU,eAAe,KAAK,YAAY,QACpD,KAAK,aAAa,WAAW,OAAO,SACpC;AAAA;AAAA;AAIR,aAAO,KAAK,kBAAkB;AAAA;AAEhC,QAAI,KAAK,yBAAyB;AAChC,aAAO;AAAA;AAET,QAAI,KAAK,IAAI,SAAS,sBAAsB;AAC1C,aAAO,KAAK,uBAAuB;AAAA;AAErC,QAAI,KAAK,IAAI,SAAS,sBAAsB;AAC1C,aAAO;AAAA;AAET,QAAI,KAAK,IAAI,SAAS,sBAAsB;AAE1C,eAAS;AAET,UAAI;AACJ,iBAAW,WAAW,SAAS;AAC7B,YAAI,QAAQ,eAAe,MAAM;AAE/B,yBAAe;AAAA,mBACN,QAAQ,eAAe,QAAQ;AAExC,yBAAe;AAAA,eACV;AACL,yBAAe,QAAQ;AAAA;AAGzB,eAAO,QAAQ,QAAQ;AAAA,UACrB,MAAM,QAAQ;AAAA,UACd,WAAW,QAAQ,YAAY;AAAA,UAC/B;AAAA,UACA,YAAY,QAAQ,OAAO;AAAA;AAG7B,YAAI,OAAO,QAAQ,MAAM,SAAS,cAAc;AAC9C,iBAAO,QAAQ,MAAM,eAAe,EAAE,KAAK,OAAO,KAAK,OAAO,OAAO,QAAQ,MAAM;AAAA;AAGrF,YAAI,OAAO,OAAO,QAAQ,MAAM,iBAAiB,UAAU;AACzD,iBAAO,QAAQ,MAAM,eAAe,OAAO,QAAQ,MAAM,aAAa,QAAQ,MAAM;AAAA;AAAA;AAGxF,aAAO;AAAA;AAET,QAAI,KAAK,IAAI,SAAS,yBAAyB;AAC7C,aAAO,QAAQ;AAAA;AAEjB,QAAI,KAAK,IAAI,SAAS,wBAAwB;AAC5C,aAAO;AAAA;AAET,QAAI,KAAK,IAAI,SAAS,4BAA4B;AAChD,aAAO;AAAA;AAET,QAAI,CAAC,WAAW,YAAY,WAAW,YAAY,SAAS,KAAK,QAAQ,OAAO;AAC9E,aAAO,SAAS;AAAA;AAElB,QAAI,KAAK,QAAQ,SAAS,WAAW,SAAS;AAC5C,aAAO,QAAQ,GAAG;AAAA;AAEpB,QAAI,KAAK,QAAQ,SAAS,WAAW,KAAK;AACxC,aAAO,CAAC,SAAS;AAAA;AAEnB,QAAI,KAAK,iBAAiB;AACxB,aAAO,CAAC,QAAQ;AAAA;AAElB,QAAI,KAAK,mBAAmB,KAAK,iBAAiB;AAChD,aAAO,CAAC,QAAQ,SAAS;AAAA;AAE3B,WAAO;AAAA;AAAA,QAGH,IAAI,KAAK,YAAY;AACzB,UAAM,OAAO,KAAK;AAClB,SAAK,MAAM;AACX,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,UAAU,KAAK,OAAO;AAE5C,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW,KAAK,UAAU,YAAY;AACjE,YAAM,cAAc;AACpB,YAAM,cAAc,IAAI;AACxB,YAAM,aAAa,MAAM;AACvB,YAAI,IAAI,WAAW,QAAQ;AACzB,iBAAO;AAAA;AAET,cAAM,QAAQ;AAEd,8BAAsB,gBAAgB,SAAS;AAC7C,cAAI;AACF;AAGA,oBAAQ,MAAM,qBAAqB,MAAM,aAAa,gBAAgB,SAAS,YAAY;AAC3F;AAAA,mBACO,OAAP;AACA,mBAAO;AAAA;AAAA;AAIX,YAAI,CAAC;AAAY,uBAAa;AAE9B,YAAI,EAAE,cAAc,aAAa;AAC/B,gBAAM,gBAAgB,OAAO,OAAO;AACpC,qBAAW,OAAO,OAAO,KAAK,aAAa;AACzC,0BAAc,GAAG,SAAS,kBAAkB,WAAW;AAAA;AAEzD,uBAAa;AAAA,eACR;AACL,uBAAa,WAAW,IAAI;AAAA;AAG9B,aAAK,QAAQ,KAAK,YAAY;AAE9B,eAAO;AAAA;AAGT,UAAI,KAAK,wBAAwB,OAAO;AACtC,YAAI,aAAa;AACjB,YAAI,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC3C,uBAAa,KAAK,QAAQ;AAAA,mBACjB,gBAAgB,KAAK,KAAK,MAAM;AACzC,qBAAW,KAAK,gBAAgB,KAAK,KAAK,KAAK;AAAA;AAIjD,qBAAa,WAAW,OAAO,eAAa,CAAE,cAAa,gBAAgB,cAAc;AAEzF,YAAI,CAAC,WAAW,QAAQ;AACtB,iBAAO;AAAA;AAET,cAAM,QAAQ,IAAI,WAAW,IAAI,eAC/B,IAAI,QAAQ,cAAW;AACrB,sBAAY,UAAU,QAAQ,MAAM;AACpC,sBAAY,aAAa;AAEzB,eAAK,IAAI,uBAAuB,gBAAgB,CAAC,KAAK,YAAY;AAChE,gBAAI,CAAC,KAAK;AACR,yBAAW,UAAU,SAAS;AAC5B,4BAAY,WAAW,OAAO,QAAQ,OAAO;AAAA;AAAA;AAGjD;AAAA;AAAA;AAAA;AAIR,aAAO;AAAA;AAAA;AAAA,EAIX,wBAAwB,KAAK;AAC3B,QAAI,cAAc,IAAI,MAAM;AAC5B,QAAI,oBAAoB,oBAAoB,cAAc;AAC1D,gBAAY,OAAO,GAAG;AACtB,kBAAc,YAAY,IAAI,mBAAiB;AAE7C,UAAI,cAAc,SAAS,eAAe;AAExC,uBAAe,cAAc,MAAM;AACnC,uBAAe,cAAc,MAAM;AAEnC,YAAI,cAAc;AAChB,yBAAe,aAAa;AAAA;AAG9B,YAAI,cAAc;AAChB,yBAAe,aAAa;AAAA;AAG9B,cAAM,kBAAkB;AACxB,cAAM,sBAAsB,cAAc,MAAM,iBAAiB,GAAG,MAAM;AAC1E,6BAAqB,MAAM,YAAY,oBAAoB;AAC3D,YAAI,cAAc,oBAAoB;AACtC,sBAAc,YAAY,QAAQ,UAAU,IAAI,MAAM;AACtD,6BAAqB,YAAY,IAAI,YAAU,MAAM,YAAY;AAAA;AAGnE,YAAM,sBAAsB,cAAc,MAAM,8CAA8C;AAC9F,sBAAgB,cAAc,QAAQ,UAAU;AAChD,YAAM,aAAa,cAAc,MAAM;AAEvC,UAAI,CAAC,WAAW,WAAW,SAAS,WAAW,KAAK;AAClD,mBAAW,MAAM;AAAA;AAGnB,aAAO;AAAA,QACL,gBAAgB,MAAM,YAAY,WAAW;AAAA,QAC7C,gBAAgB,WAAW;AAAA,QAC3B;AAAA,QACA;AAAA,QACA,KAAK,IAAI,QAAQ,MAAM;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAIJ,WAAO;AAAA;AAAA,EAGT,aAAa,MAAM,OAAO;AACxB,QAAI,KAAK,SAAS,MAAM;AAEtB,aAAO,KAAK,OAAO,GAAG,KAAK,QAAQ;AAAA;AAErC,WAAO,KAAK,QAAQ,YAAY,IAAI,QAAQ,YAAY;AACxD,WAAO,KAAK,OAAO;AACnB,UAAM,QAAQ,YAAY,IAAI;AAE9B,QAAI,UAAU,QAAQ,OAAO;AAC3B,aAAO,MAAM,OAAO,EAAE,UAAU,KAAK,UAAU,QAAQ;AAAA;AAEzD,WAAO;AAAA;AAAA,EAGT,YAAY,KAAK,UAAU;AAEzB,YAAQ,IAAI;AAAA,WACL;AAAA,WACA;AAAA,WACA;AAAA,WACA;AAAA,WACA,qBAAqB;AACxB,YAAI,IAAI,QAAQ,SAAS,kCAAkC;AACzD,iBAAO,IAAI,gBAAgB,0BAA0B;AAAA,YACnD,QAAQ;AAAA,YACR,OAAO;AAAA;AAAA;AAIX,YAAI,SAAS;AAGb,YAAI,QAAQ,IAAI,QAAQ,MAAM;AAC9B,YAAI,UAAU,QAAQ,MAAM,UAAU,GAAG;AACvC,mBAAS,MAAM,GAAG,MAAM;AAAA,eACnB;AAGL,kBAAQ,IAAI,QAAQ,MAAM;AAC1B,cAAI,UAAU,QAAQ,MAAM,UAAU,GAAG;AACvC,qBAAS,MAAM,GAAG,MAAM,MAAM,IAAI,qBAAmB,gBAAgB,MAAM,KAAK;AAAA;AAAA;AAIpF,cAAM,SAAS;AACf,YAAI,UAAU;AAEd,mBAAW,SAAS,QAAQ;AAC1B,iBAAO,KAAK,IAAI,gBAAgB,oBAC9B,KAAK,gCAAgC,QACrC,oBACA,OACA,KAAK,YAAY,KAAK,SAAS,QAC/B,KAAK,UACL;AAAA;AAIJ,YAAI,KAAK,OAAO;AACd,YAAE,OAAO,KAAK,MAAM,YAAY,gBAAc;AAC5C,gBAAI,EAAE,QAAQ,WAAW,QAAQ,WAAW,CAAC,CAAC,WAAW,KAAK;AAC5D,wBAAU,WAAW;AACrB,qBAAO;AAAA;AAAA;AAAA;AAKb,eAAO,IAAI,gBAAgB,sBAAsB,EAAE,SAAS,QAAQ,QAAQ,KAAK,QAAQ,OAAO;AAAA;AAAA,WAE7F;AACH,eAAO,IAAI,gBAAgB,aAAa,KAAK,EAAE,OAAO;AAAA;AAGtD,eAAO,IAAI,gBAAgB,cAAc,KAAK,EAAE,OAAO;AAAA;AAAA;AAAA,QAIvD,uBAAuB,MAAM;AAEjC,WAAO,QAAQ,IAAI,KAAK,UAAU,IAAI,OAAM,SAAQ;AAClD,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,SAAS,CAAC,CAAC,KAAK;AACrB,WAAK,iBAAiB,KAAK;AAC3B,YAAM,UAAU,MAAM,KAAK,IAAI,uBAAuB,KAAK;AAC3D,iBAAW,UAAU,SAAS;AAC5B,aAAK,OAAO,OAAO,SAAS;AAAA,UAC1B,WAAW,OAAO;AAAA,UAClB,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA;AAIX,aAAO;AAAA;AAAA;AAAA,EAIX,oBAAoB;AAClB,QAAI,KAAK,mBAAmB,KAAK,mBAAmB,KAAK,mBAAmB,KAAK,uBAAuB,KAAK,IAAI,cAAc,SAAS,yBAAyB,kBAAkB,KAAK,QAAQ,SAAS,WAAW,YAAY;AAC9N,aAAO;AAAA;AAET,WAAO;AAAA;AAAA;AAIX,OAAO,UAAU;AACjB,OAAO,QAAQ,QAAQ;AACvB,OAAO,QAAQ,UAAU;", "names": []}