const { User } = require('../models');
const { successResponse, errorResponse } = require('../utils/response');

// 获取用户信息
const getUserInfo = async (req, res, next) => {
  try {
    const user = req.user;
    
    res.json(successResponse({
      id: user.id,
      username: user.username,
      name: user.name,
      department: user.department,
      position: user.position,
      phone: user.phone,
      email: user.email,
      permissions: user.permissions
    }));
  } catch (error) {
    next(error);
  }
};

// 更新用户信息
const updateUserInfo = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { name, phone, email } = req.body;
    
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (phone !== undefined) updateData.phone = phone;
    if (email !== undefined) updateData.email = email;
    
    const [updatedRowsCount] = await User.update(updateData, {
      where: { id: userId }
    });
    
    if (updatedRowsCount === 0) {
      return res.status(404).json(errorResponse(404, '用户不存在'));
    }
    
    // 获取更新后的用户信息
    const updatedUser = await User.findByPk(userId);
    
    res.json(successResponse({
      id: updatedUser.id,
      username: updatedUser.username,
      name: updatedUser.name,
      department: updatedUser.department,
      position: updatedUser.position,
      phone: updatedUser.phone,
      email: updatedUser.email
    }, '用户信息更新成功'));
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getUserInfo,
  updateUserInfo
};
