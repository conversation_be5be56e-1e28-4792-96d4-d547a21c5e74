
<!--vehicles.wxml-->
<view class="container">
  <!-- 车辆列表 -->
  <view class="vehicle-list" wx:if="{{!isEditing}}">
    <view class="header">
      <text class="title">车辆列表</text>
      <button class="add-btn" bindtap="addVehicle">添加车辆</button>
    </view>
    
    <block wx:if="{{vehicles.length > 0}}">
      <view class="vehicle-item" wx:for="{{vehicles}}" wx:key="id">
        <view class="vehicle-info">
          <view class="plate-number">{{item.plateNumber}}</view>
          <view class="details">
            <text class="brand">{{item.brand}} {{item.model}}</text>
            <text class="type">{{item.type}}</text>
          </view>
          <view class="status-tag {{item.status === '可用' ? 'available' : item.status === '维修中' ? 'maintenance' : item.status === '已预约' ? 'reserved' : 'unavailable'}}">
            {{item.status}}
          </view>
        </view>
        <view class="actions">
          <button class="action-btn edit" bindtap="editVehicle" data-id="{{item.id}}">编辑</button>
          <button class="action-btn delete" bindtap="deleteVehicle" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </block>
    
    <view class="empty-tip" wx:else>
      <text>暂无车辆信息，请添加</text>
    </view>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form" wx:if="{{isEditing}}">
    <view class="form-header">
      <text class="title">{{currentVehicle.id ? '编辑' : '添加'}}车辆信息</text>
    </view>
    
    <form bindsubmit="saveVehicle">
      <view class="form-group">
        <view class="form-item">
          <text class="label">车牌号码</text>
          <input class="input" name="plateNumber" value="{{currentVehicle.plateNumber}}" bindinput="inputChange" data-field="plateNumber" placeholder="请输入车牌号码" />
        </view>
        
        <view class="form-item">
          <text class="label">车辆类型</text>
          <input class="input" name="type" value="{{currentVehicle.type}}" bindinput="inputChange" data-field="type" placeholder="请输入车辆类型" />
        </view>
        
        <view class="form-item">
          <text class="label">品牌</text>
          <input class="input" name="brand" value="{{currentVehicle.brand}}" bindinput="inputChange" data-field="brand" placeholder="请输入品牌" />
        </view>
        
        <view class="form-item">
          <text class="label">型号</text>
          <input class="input" name="model" value="{{currentVehicle.model}}" bindinput="inputChange" data-field="model" placeholder="请输入型号" />
        </view>
        
        <view class="form-item">
          <text class="label">状态</text>
          <picker name="status" value="{{currentVehicle.status}}" range="{{statusOptions}}" bindchange="statusChange">
            <view class="picker {{currentVehicle.status ? '' : 'placeholder'}}">
              {{currentVehicle.status || '请选择车辆状态'}}
            </view>
          </picker>
        </view>
      </view>
      
      <view class="form-actions">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="save-btn" form-type="submit">保存</button>
      </view>
    </form>
  </view>
</view>