
<view class="container">
  <view class="page-title">车辆信息</view>
  
  <!-- 状态筛选器 -->
  <view class="filter-container">
    <view 
      class="filter-item {{currentFilter === item ? 'active' : ''}}" 
      wx:for="{{statusFilters}}" 
      wx:key="*this"
      data-filter="{{item}}"
      bindtap="changeFilter"
    >
      {{item}}
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <icon type="warn" size="64"></icon>
    <text class="error-text">{{error}}</text>
    <button class="retry-button" bindtap="loadVehicles">重试</button>
  </view>

  <!-- 车辆列表 -->
  <view class="vehicle-list" wx:elif="{{!loading && !error}}">
    <view 
      class="vehicle-item"
      wx:for="{{filteredVehicles || vehicles}}" 
      wx:key="id"
      bindtap="viewVehicleDetail"
      data-id="{{item.id}}"
    >
      <view class="vehicle-header">
        <view class="plate-number">{{item.plateNumber}}</view>
        <view class="status {{item.statusClass}}">{{item.status}}</view>
      </view>
      
      <view class="vehicle-info">
        <view class="info-row">
          <view class="info-label">车型：</view>
          <view class="info-value">{{item.type}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">品牌：</view>
          <view class="info-value">{{item.brand}} {{item.model}}</view>
        </view>
        <view class="info-row">
          <view class="info-label">里程：</view>
          <view class="info-value">{{item.mileage}}公里</view>
        </view>
        <view class="info-row">
          <view class="info-label">最近保养：</view>
          <view class="info-value">{{item.lastMaintenance}}</view>
        </view>
      </view>
      
      <view class="vehicle-footer">
        <text class="view-detail">查看详情 ></text>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-list" wx:if="{{!loading && !error && (!filteredVehicles || filteredVehicles.length === 0)}}">
    <image class="empty-image" src="/images/empty-state.png" mode="aspectFit"></image>
    <text class="empty-text">{{currentFilter === '全部' ? '暂无车辆信息' : '没有符合条件的车辆'}}</text>
  </view>

  <!-- 下拉刷新提示 -->
  <view class="refresh-tip" wx:if="{{showRefreshTip}}">
    <text>下拉可以刷新</text>
  </view>
</view>