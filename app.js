
// 应用入口文件 - 小程序的全局配置和初始化
const dataManager = require('./utils/dataManager');  // 导入数据管理模块

App({
  globalData: {
    userInfo: null  // 全局用户信息，初始为null
  },
  
  onLaunch: function () {
    // 小程序启动时执行的逻辑
    console.log('App launched');  // 记录应用启动日志
    
    // 初始化本地数据
    try {
      console.log('开始初始化数据...');  // 记录初始化开始
      dataManager.initData();  // 调用数据管理器初始化数据
      console.log('数据初始化完成');  // 记录初始化完成
    } catch (e) {
      console.error('数据初始化失败:', e);  // 记录初始化失败
      // 可以在这里添加降级处理
    }
  },
});