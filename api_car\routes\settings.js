const express = require('express');
const router = express.Router();
const settingsController = require('../controllers/settingsController');
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { notificationSchemas } = require('../utils/validation');

// 获取通知设置
router.get('/notifications', authenticateToken, settingsController.getNotificationSettings);

// 更新通知设置
router.put('/notifications', 
  authenticateToken, 
  validate(notificationSchemas.updateSettings), 
  settingsController.updateNotificationSettings
);

module.exports = router;
