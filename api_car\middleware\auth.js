const jwt = require('jsonwebtoken');
const { User } = require('../models');
const { jwtSecret } = require('../config/auth');
const { errorResponse } = require('../utils/response');

// 验证JWT Token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json(errorResponse(401, '未提供认证令牌'));
    }

    const decoded = jwt.verify(token, jwtSecret);
    
    // 查找用户
    const user = await User.findByPk(decoded.userId);
    if (!user || !user.is_active) {
      return res.status(401).json(errorResponse(401, '用户不存在或已被禁用'));
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json(errorResponse(401, '无效的认证令牌'));
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json(errorResponse(401, '认证令牌已过期'));
    }
    return res.status(500).json(errorResponse(500, '认证验证失败'));
  }
};

// 检查权限
const checkPermission = (requiredPermission) => {
  return (req, res, next) => {
    const userPermissions = req.user.permissions || [];
    
    if (!userPermissions.includes(requiredPermission) && !userPermissions.includes('admin')) {
      return res.status(403).json(errorResponse(403, '权限不足'));
    }
    
    next();
  };
};

// 检查管理员权限
const requireAdmin = (req, res, next) => {
  const userPermissions = req.user.permissions || [];
  
  if (!userPermissions.includes('admin')) {
    return res.status(403).json(errorResponse(403, '需要管理员权限'));
  }
  
  next();
};

module.exports = {
  authenticateToken,
  checkPermission,
  requireAdmin
};
