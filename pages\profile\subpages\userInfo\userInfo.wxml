
<!--userInfo.wxml-->
<view class="container">
  <!-- 非编辑模式 -->
  <view class="info-display" wx:if="{{!isEditing}}">
    <view class="info-list">
      <view class="info-item">
        <text class="label">姓名</text>
        <text class="value">{{userInfo.name}}</text>
      </view>
      <view class="info-item">
        <text class="label">角色</text>
        <text class="value">{{userInfo.role}}</text>
      </view>
      <view class="info-item">
        <text class="label">部门</text>
        <text class="value">{{userInfo.department || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="label">电话</text>
        <text class="value">{{userInfo.phone || '未设置'}}</text>
      </view>
      <view class="info-item">
        <text class="label">邮箱</text>
        <text class="value">{{userInfo.email || '未设置'}}</text>
      </view>
    </view>
    
    <button class="edit-btn" bindtap="startEditing">编辑资料</button>
  </view>

  <!-- 编辑模式 -->
  <view class="edit-form" wx:if="{{isEditing}}">
    <form bindsubmit="saveEdit">
      <view class="form-group">
        <view class="form-item">
          <text class="label">姓名</text>
          <input class="input" name="name" value="{{userInfo.name}}" bindinput="inputChange" data-field="name" />
        </view>
        
        <view class="form-item">
          <text class="label">角色</text>
          <input class="input" name="role" value="{{userInfo.role}}" bindinput="inputChange" data-field="role" />
        </view>
        
        <view class="form-item">
          <text class="label">部门</text>
          <input class="input" name="department" value="{{userInfo.department}}" bindinput="inputChange" data-field="department" />
        </view>
        
        <view class="form-item">
          <text class="label">电话</text>
          <input class="input" name="phone" value="{{userInfo.phone}}" bindinput="inputChange" data-field="phone" />
        </view>
        
        <view class="form-item">
          <text class="label">邮箱</text>
          <input class="input" name="email" value="{{userInfo.email}}" bindinput="inputChange" data-field="email" />
        </view>
      </view>
      
      <view class="form-actions">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="save-btn" form-type="submit">保存</button>
      </view>
    </form>
  </view>
</view>