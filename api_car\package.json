{"name": "car-management-api", "version": "1.0.0", "description": "车辆管理系统后端API", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "keywords": ["car", "management", "api", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "sequelize": "^6.33.0", "sqlite3": "^5.1.6", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "joi": "^17.10.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}