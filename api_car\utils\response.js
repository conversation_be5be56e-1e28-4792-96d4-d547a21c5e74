// 成功响应
const successResponse = (data = null, message = 'success') => {
  return {
    code: 200,
    message,
    data
  };
};

// 错误响应
const errorResponse = (code, message, errors = null) => {
  const response = {
    code,
    message
  };
  
  if (errors) {
    response.errors = errors;
  }
  
  return response;
};

// 分页响应
const paginatedResponse = (items, total, page, pageSize, message = 'success') => {
  return {
    code: 200,
    message,
    data: {
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      items
    }
  };
};

module.exports = {
  successResponse,
  errorResponse,
  paginatedResponse
};
