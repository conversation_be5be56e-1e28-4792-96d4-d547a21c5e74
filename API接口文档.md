# 车辆管理系统 API 接口文档

## 概述

本文档详细描述了车辆管理系统的API接口规范，用于指导后端API的开发。前端应用通过这些API与后端服务进行数据交互，实现用户信息管理、车辆管理、审批流程等功能。

## 基础信息

### 基础URL

```
http://127.0.0.1/appvue
```

### 请求格式

- 所有请求和响应均使用JSON格式
- 请求头需包含 `Content-Type: application/json`

### 认证方式

API使用基于Token的认证机制：

- 客户端通过登录接口获取Token
- 所有需要认证的请求都应在Header中包含 `Authorization: Bearer {token}`

## 通用响应格式

所有API响应都遵循以下格式：

```json
{
  "code": 200,           // 状态码，200表示成功，其他值表示错误
  "message": "success",  // 状态描述
  "data": {              // 响应数据，根据不同API有不同结构
    // 具体数据
  }
}
```

## 错误处理

当API请求失败时，会返回对应的错误码和错误信息：

```json
{
  "code": 400,                   // 错误码
  "message": "参数错误",          // 错误描述
  "errors": [                    // 详细错误信息（可选）
    {
      "field": "username",
      "message": "用户名不能为空"
    }
  ]
}
```

### 常见错误码

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权（未登录或Token无效） |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API端点详细说明

### 用户相关接口

#### 获取用户信息

获取当前登录用户的详细信息。

- **URL**: `/user/info`
- **方法**: `GET`
- **需要认证**: 是

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "name": "张三",
    "department": "技术部",
    "position": "工程师",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "permissions": ["car_apply", "car_view"]
  }
}
```

#### 更新用户信息

更新当前登录用户的个人信息。

- **URL**: `/user/info`
- **方法**: `PUT`
- **需要认证**: 是

**请求参数**:

```json
{
  "name": "张三",
  "phone": "13800138000",
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": {
    "id": 1,
    "username": "zhangsan",
    "name": "张三",
    "department": "技术部",
    "position": "工程师",
    "phone": "13800138000",
    "email": "<EMAIL>"
  }
}
```

### 审批人相关接口

#### 获取审批人列表

获取当前用户可用的审批人列表。

- **URL**: `/approvers`
- **方法**: `GET`
- **需要认证**: 是

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "李主管",
      "role": "部门主管",
      "department": "技术部",
      "phone": "13900139000"
    },
    {
      "id": 2,
      "name": "王经理",
      "role": "部门经理",
      "department": "行政部",
      "phone": "13700137000"
    }
  ]
}
```

#### 添加审批人

添加新的审批人。

- **URL**: `/approvers`
- **方法**: `POST`
- **需要认证**: 是
- **权限要求**: 管理员权限

**请求参数**:

```json
{
  "name": "赵总监",
  "role": "总监",
  "department": "研发部",
  "phone": "13600136000",
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "审批人添加成功",
  "data": {
    "id": 3,
    "name": "赵总监",
    "role": "总监",
    "department": "研发部",
    "phone": "13600136000",
    "email": "<EMAIL>"
  }
}
```

#### 更新审批人信息

更新指定审批人的信息。

- **URL**: `/approvers/{id}`
- **方法**: `PUT`
- **需要认证**: 是
- **权限要求**: 管理员权限

**URL参数**:
- `id`: 审批人ID

**请求参数**:

```json
{
  "name": "赵总监",
  "role": "高级总监",
  "department": "研发部",
  "phone": "13600136000",
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "审批人信息更新成功",
  "data": {
    "id": 3,
    "name": "赵总监",
    "role": "高级总监",
    "department": "研发部",
    "phone": "13600136000",
    "email": "<EMAIL>"
  }
}
```

#### 删除审批人

删除指定的审批人。

- **URL**: `/approvers/{id}`
- **方法**: `DELETE`
- **需要认证**: 是
- **权限要求**: 管理员权限

**URL参数**:
- `id`: 审批人ID

**响应示例**:

```json
{
  "code": 200,
  "message": "审批人删除成功",
  "data": null
}
```

### 车辆相关接口

#### 获取车辆列表

获取系统中所有车辆的列表。

- **URL**: `/vehicles`
- **方法**: `GET`
- **需要认证**: 是

**查询参数**:
- `status`: (可选) 按状态筛选，可选值：可用、使用中、维修中
- `type`: (可选) 按车型筛选
- `page`: (可选) 页码，默认1
- `pageSize`: (可选) 每页数量，默认10

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "page": 1,
    "pageSize": 10,
    "items": [
      {
        "id": 1,
        "plateNumber": "粤A12345",
        "type": "小型轿车",
        "brand": "丰田",
        "model": "凯美瑞",
        "status": "可用",
        "mileage": 15000,
        "lastMaintenance": "2023-09-15"
      },
      {
        "id": 2,
        "plateNumber": "粤B67890",
        "type": "SUV",
        "brand": "本田",
        "model": "CR-V",
        "status": "使用中",
        "mileage": 25000,
        "lastMaintenance": "2023-08-20"
      }
      // 更多车辆...
    ]
  }
}
```

#### 获取单个车辆信息

获取指定车辆的详细信息。

- **URL**: `/vehicles/{id}`
- **方法**: `GET`
- **需要认证**: 是

**URL参数**:
- `id`: 车辆ID

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "plateNumber": "粤A12345",
    "type": "小型轿车",
    "brand": "丰田",
    "model": "凯美瑞",
    "status": "可用",
    "mileage": 15000,
    "lastMaintenance": "2023-09-15",
    "purchaseDate": "2020-05-10",
    "insuranceExpiry": "2024-05-10",
    "annualInspectionDate": "2023-12-15",
    "maintenanceRecords": [
      {
        "date": "2023-09-15",
        "mileage": 15000,
        "type": "常规保养",
        "description": "更换机油、机滤、空滤"
      },
      {
        "date": "2023-03-20",
        "mileage": 10000,
        "type": "常规保养",
        "description": "更换机油、机滤"
      }
    ]
  }
}
```

#### 添加车辆

添加新车辆到系统。

- **URL**: `/vehicles`
- **方法**: `POST`
- **需要认证**: 是
- **权限要求**: 管理员权限

**请求参数**:

```json
{
  "plateNumber": "粤C13579",
  "type": "商务车",
  "brand": "大众",
  "model": "途安",
  "status": "可用",
  "mileage": 0,
  "purchaseDate": "2023-10-01",
  "insuranceExpiry": "2024-10-01"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "车辆添加成功",
  "data": {
    "id": 3,
    "plateNumber": "粤C13579",
    "type": "商务车",
    "brand": "大众",
    "model": "途安",
    "status": "可用",
    "mileage": 0,
    "purchaseDate": "2023-10-01",
    "insuranceExpiry": "2024-10-01",
    "lastMaintenance": null
  }
}
```

#### 更新车辆信息

更新指定车辆的信息。

- **URL**: `/vehicles/{id}`
- **方法**: `PUT`
- **需要认证**: 是
- **权限要求**: 管理员权限

**URL参数**:
- `id`: 车辆ID

**请求参数**:

```json
{
  "status": "维修中",
  "mileage": 16000,
  "lastMaintenance": "2023-10-20"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "车辆信息更新成功",
  "data": {
    "id": 1,
    "plateNumber": "粤A12345",
    "type": "小型轿车",
    "brand": "丰田",
    "model": "凯美瑞",
    "status": "维修中",
    "mileage": 16000,
    "lastMaintenance": "2023-10-20",
    "purchaseDate": "2020-05-10",
    "insuranceExpiry": "2024-05-10"
  }
}
```

#### 删除车辆

从系统中删除指定车辆。

- **URL**: `/vehicles/{id}`
- **方法**: `DELETE`
- **需要认证**: 是
- **权限要求**: 管理员权限

**URL参数**:
- `id`: 车辆ID

**响应示例**:

```json
{
  "code": 200,
  "message": "车辆删除成功",
  "data": null
}
```

### 用车历史相关接口

#### 获取用车历史列表

获取用车记录历史列表。

- **URL**: `/car-usage/history`
- **方法**: `GET`
- **需要认证**: 是

**查询参数**:
- `userId`: (可选) 按用户ID筛选
- `vehicleId`: (可选) 按车辆ID筛选
- `status`: (可选) 按状态筛选，可选值：待审批、已批准、已拒绝、使用中、已完成
- `startDate`: (可选) 开始日期，格式：YYYY-MM-DD
- `endDate`: (可选) 结束日期，格式：YYYY-MM-DD
- `page`: (可选) 页码，默认1
- `pageSize`: (可选) 每页数量，默认10

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 35,
    "page": 1,
    "pageSize": 10,
    "items": [
      {
        "id": 1,
        "userId": 1,
        "userName": "张三",
        "vehicleId": 2,
        "plateNumber": "粤B67890",
        "purpose": "客户拜访",
        "startTime": "2023-10-15 09:00:00",
        "endTime": "2023-10-15 18:00:00",
        "status": "已完成",
        "mileage": 120,
        "approverId": 1,
        "approverName": "李主管",
        "approveTime": "2023-10-14 14:30:00",
        "comments": "按时归还"
      },
      {
        "id": 2,
        "userId": 1,
        "userName": "张三",
        "vehicleId": 1,
        "plateNumber": "粤A12345",
        "purpose": "项目调研",
        "startTime": "2023-10-20 08:00:00",
        "endTime": "2023-10-20 17:00:00",
        "status": "待审批",
        "mileage": null,
        "approverId": null,
        "approverName": null,
        "approveTime": null,
        "comments": null
      }
      // 更多记录...
    ]
  }
}
```

#### 添加用车记录

添加新的用车申请记录。

- **URL**: `/car-usage/record`
- **方法**: `POST`
- **需要认证**: 是

**请求参数**:

```json
{
  "vehicleId": 1,
  "purpose": "客户会议",
  "startTime": "2023-11-01 09:00:00",
  "endTime": "2023-11-01 18:00:00",
  "destination": "广州市天河区",
  "passengers": 3,
  "approverId": 1,
  "remarks": "需要提前准备车辆"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "用车申请提交成功",
  "data": {
    "id": 3,
    "userId": 1,
    "userName": "张三",
    "vehicleId": 1,
    "plateNumber": "粤A12345",
    "purpose": "客户会议",
    "startTime": "2023-11-01 09:00:00",
    "endTime": "2023-11-01 18:00:00",
    "status": "待审批",
    "destination": "广州市天河区",
    "passengers": 3,
    "approverId": 1,
    "approverName": "李主管",
    "submitTime": "2023-10-25 10:30:00"
  }
}
```

#### 更新用车记录

更新指定的用车记录，包括审批、完成用车等操作。

- **URL**: `/car-usage/record/{id}`
- **方法**: `PUT`
- **需要认证**: 是
- **权限要求**: 根据操作类型不同，可能需要审批人权限或管理员权限

**URL参数**:
- `id`: 用车记录ID

**请求参数**:

```json
{
  "status": "已批准",
  "comments": "同意申请，请按时归还"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "用车记录更新成功",
  "data": {
    "id": 2,
    "userId": 1,
    "userName": "张三",
    "vehicleId": 1,
    "plateNumber": "粤A12345",
    "purpose": "项目调研",
    "startTime": "2023-10-20 08:00:00",
    "endTime": "2023-10-20 17:00:00",
    "status": "已批准",
    "approverId": 1,
    "approverName": "李主管",
    "approveTime": "2023-10-18 11:20:00",
    "comments": "同意申请，请按时归还"
  }
}
```

### 通知设置相关接口

#### 获取通知设置

获取当前用户的通知设置。

- **URL**: `/settings/notifications`
- **方法**: `GET`
- **需要认证**: 是

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "approvalNotice": true,
    "usageReminder": true,
    "maintenanceAlert": false,
    "systemNotification": true,
    "emailNotification": false,
    "smsNotification": true
  }
}
```

#### 更新通知设置

更新当前用户的通知设置。

- **URL**: `/settings/notifications`
- **方法**: `PUT`
- **需要认证**: 是

**请求参数**:

```json
{
  "approvalNotice": true,
  "usageReminder": true,
  "maintenanceAlert": true,
  "systemNotification": true,
  "emailNotification": true,
  "smsNotification": false
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "通知设置更新成功",
  "data": {
    "approvalNotice": true,
    "usageReminder": true,
    "maintenanceAlert": true,
    "systemNotification": true,
    "emailNotification": true,
    "smsNotification": false
  }
}
```

## 数据模型

### 用户模型

```json
{
  "id": "用户ID",
  "username": "用户名",
  "name": "姓名",
  "department": "部门",
  "position": "职位",
  "phone": "电话号码",
  "email": "电子邮箱",
  "permissions": ["权限列表"]
}
```

### 审批人模型

```json
{
  "id": "审批人ID",
  "name": "姓名",
  "role": "角色",
  "department": "部门",
  "phone": "电话号码",
  "email": "电子邮箱"
}
```

### 车辆模型

```json
{
  "id": "车辆ID",
  "plateNumber": "车牌号",
  "type": "车型",
  "brand": "品牌",
  "model": "型号",
  "status": "状态",
  "mileage": "里程数",
  "lastMaintenance": "最后保养日期",
  "purchaseDate": "购买日期",
  "insuranceExpiry": "保险到期日",
  "annualInspectionDate": "年检日期",
  "maintenanceRecords": ["保养记录列表"]
}
```

### 用车记录模型

```json
{
  "id": "记录ID",
  "userId": "用户ID",
  "userName": "用户姓名",
  "vehicleId": "车辆ID",
  "plateNumber": "车牌号",
  "purpose": "用车目的",
  "startTime": "开始时间",
  "endTime": "结束时间",
  "status": "状态",
  "mileage": "行驶里程",
  "destination": "目的地",
  "passengers": "乘客人数",
  "approverId": "审批人ID",
  "approverName": "审批人姓名",
  "approveTime": "审批时间",
  "submitTime": "提交时间",
  "comments": "备注"
}
```

### 通知设置模型

```json
{
  "approvalNotice": "审批通知",
  "usageReminder": "用车提醒",
  "maintenanceAlert": "维护提醒",
  "systemNotification": "系统通知",
  "emailNotification": "邮件通知",
  "smsNotification": "短信通知"
}
```

## 开发建议

1. 所有API应实现适当的错误处理和日志记录
2. 敏感操作应记录审计日志
3. 实现请求限流以防止滥用
4. 所有API应进行适当的权限验证
5. 考虑实现数据缓存机制以提高性能
6. 为移动端优化响应数据大小
7. 实现健康检查端点以监控API状态

## 更新历史

| 版本 | 日期 | 描述 |
|------|------|------|
| 1.0 | 2023-10-25 | 初始版本 |