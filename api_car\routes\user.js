const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { userSchemas } = require('../utils/validation');

// 获取用户信息
router.get('/info', authenticateToken, userController.getUserInfo);

// 更新用户信息
router.put('/info', 
  authenticateToken, 
  validate(userSchemas.updateUser), 
  userController.updateUserInfo
);

module.exports = router;
