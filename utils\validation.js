
// validation.js
const validate = {
  // 验证车牌号
  plateNumber: (value) => {
    const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/;
    return reg.test(value);
  },

  // 验证日期格式 (YYYY-MM-DD)
  date: (value) => {
    const reg = /^\d{4}-\d{2}-\d{2}$/;
    return reg.test(value);
  },

  // 验证手机号
  phone: (value) => {
    const reg = /^1[3-9]\d{9}$/;
    return reg.test(value);
  },

  // 验证邮箱
  email: (value) => {
    const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return reg.test(value);
  },

  // 验证必填字段
  required: (value) => {
    return value !== null && value !== undefined && value !== '';
  },

  // 验证文本长度
  length: (value, min, max) => {
    if (min && value.length < min) return false;
    if (max && value.length > max) return false;
    return true;
  },

  // 验证数字范围
  numberRange: (value, min, max) => {
    const num = Number(value);
    if (isNaN(num)) return false;
    if (min !== undefined && num < min) return false;
    if (max !== undefined && num > max) return false;
    return true;
  },

  // 验证用车申请
  carUsage: (data) => {
    return validate.required(data.date) && 
           validate.date(data.date) &&
           validate.required(data.reason) &&
           validate.length(data.reason, 5, 200) &&
           validate.required(data.vehicleId);
  },

  // 验证审批人信息
  approver: (data) => {
    return validate.required(data.name) &&
           validate.length(data.name, 2, 20) &&
           validate.required(data.role) &&
           validate.required(data.department);
  },

  // 验证车辆信息
  vehicle: (data) => {
    return validate.required(data.plateNumber) &&
           validate.plateNumber(data.plateNumber) &&
           validate.required(data.type) &&
           validate.required(data.brand);
  },

  // 获取验证错误信息
  getErrorMessage: (field, rule, ...args) => {
    const messages = {
      plateNumber: '请输入有效的车牌号码',
      date: '请输入有效的日期格式(YYYY-MM-DD)',
      phone: '请输入有效的手机号码',
      email: '请输入有效的邮箱地址',
      required: '该字段为必填项',
      length: `长度应在${args[0] || ''}-${args[1] || ''}之间`,
      numberRange: `数值应在${args[0] || ''}-${args[1] || ''}之间`,
      carUsage: '请填写完整的用车申请信息',
      approver: '请填写完整的审批人信息',
      vehicle: '请填写完整的车辆信息'
    };
    return messages[rule] || `字段${field}验证失败`;
  }
};

module.exports = validate;