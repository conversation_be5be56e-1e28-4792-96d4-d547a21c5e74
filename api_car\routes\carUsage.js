const express = require('express');
const router = express.Router();
const carUsageController = require('../controllers/carUsageController');
const { authenticateToken } = require('../middleware/auth');
const { validate, validateQuery, validateParams } = require('../middleware/validation');
const { carUsageSchemas, commonSchemas } = require('../utils/validation');

// 获取用车历史列表
router.get('/history', 
  authenticateToken, 
  validateQuery(carUsageSchemas.historyQuery),
  carUsageController.getCarUsageHistory
);

// 添加用车记录
router.post('/record', 
  authenticateToken, 
  validate(carUsageSchemas.createRecord), 
  carUsageController.createCarUsageRecord
);

// 更新用车记录
router.put('/record/:id', 
  authenticateToken, 
  validateParams(commonSchemas.idParam),
  validate(carUsageSchemas.updateRecord), 
  carUsageController.updateCarUsageRecord
);

module.exports = router;
