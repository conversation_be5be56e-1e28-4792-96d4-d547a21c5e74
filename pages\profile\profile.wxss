
/* profile.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding-bottom: 30rpx;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

/* 登录按钮样式 */
.login-btn {
  padding: 20rpx 60rpx;
  background: #1296db;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
  margin: 20rpx 0;
}

.login-btn::after {
  border: none;
}

.user-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.name {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.role {
  font-size: 28rpx;
  color: #666;
}

/* 菜单列表 */
.menu-list {
  width: 100%;
}

.menu-group {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 0 20rpx 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
}

.menu-arrow {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.5;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}