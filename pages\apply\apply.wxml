
<view class="container">
  <view class="page-title">用车申请</view>
  
  <view class="form-container">
    <view class="form-item">
      <view class="form-label">用车目的</view>
      <input class="form-input" placeholder="请输入用车目的" data-field="purpose" bindinput="onInput" value="{{formData.purpose}}"/>
    </view>
    
    <view class="form-item">
      <view class="form-label">开始时间</view>
      <picker mode="date" value="{{formData.startTime}}" start="2023-01-01" end="2030-12-31" bindchange="onInput" data-field="startTime">
        <view class="picker-view">{{formData.startTime || '请选择开始时间'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="form-label">结束时间</view>
      <picker mode="date" value="{{formData.endTime}}" start="2023-01-01" end="2030-12-31" bindchange="onInput" data-field="endTime">
        <view class="picker-view">{{formData.endTime || '请选择结束时间'}}</view>
      </picker>
    </view>
    
    <view class="form-item">
      <view class="form-label">目的地</view>
      <input class="form-input" placeholder="请输入目的地" data-field="destination" bindinput="onInput" value="{{formData.destination}}"/>
    </view>
    
    <view class="form-item">
      <view class="form-label">乘车人数</view>
      <slider min="1" max="10" value="{{formData.passengers}}" show-value data-field="passengers" bindchange="onInput"/>
    </view>
  </view>
  
  <button class="submit-btn" bindtap="submitApplication">提交申请</button>
</view>