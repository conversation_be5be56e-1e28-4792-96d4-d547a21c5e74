module.exports = (sequelize, DataTypes) => {
  const Vehicle = sequelize.define('Vehicle', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    plate_number: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        len: [1, 20]
      }
    },
    type: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 50]
      }
    },
    brand: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 50]
      }
    },
    model: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 50]
      }
    },
    status: {
      type: DataTypes.ENUM('可用', '使用中', '维修中'),
      allowNull: false,
      defaultValue: '可用'
    },
    mileage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    last_maintenance: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    purchase_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    insurance_expiry: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    annual_inspection_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    maintenance_records: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue('maintenance_records');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('maintenance_records', JSON.stringify(value || []));
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'vehicles'
  });

  return Vehicle;
};
