const Joi = require('joi');
const { errorResponse } = require('../utils/response');

// 通用验证中间件
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        code: 400,
        message: '请求参数验证失败',
        errors
      });
    }
    
    next();
  };
};

// 验证查询参数
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.query);
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        code: 400,
        message: '查询参数验证失败',
        errors
      });
    }
    
    next();
  };
};

// 验证路径参数
const validateParams = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.params);
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      return res.status(400).json({
        code: 400,
        message: '路径参数验证失败',
        errors
      });
    }
    
    next();
  };
};

module.exports = {
  validate,
  validateQuery,
  validateParams
};
