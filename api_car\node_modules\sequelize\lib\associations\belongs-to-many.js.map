{"version": 3, "sources": ["../../src/associations/belongs-to-many.js"], "sourcesContent": ["'use strict';\n\nconst Utils = require('./../utils');\nconst Helpers = require('./helpers');\nconst _ = require('lodash');\nconst Association = require('./base');\nconst BelongsTo = require('./belongs-to');\nconst HasMany = require('./has-many');\nconst HasOne = require('./has-one');\nconst AssociationError = require('../errors').AssociationError;\nconst EmptyResultError = require('../errors').EmptyResultError;\nconst Op = require('../operators');\n\n/**\n * Many-to-many association with a join table.\n *\n * When the join table has additional attributes, these can be passed in the options object:\n *\n * ```js\n * UserProject = sequelize.define('user_project', {\n *   role: Sequelize.STRING\n * });\n * User.belongsToMany(Project, { through: UserProject });\n * Project.belongsToMany(User, { through: UserProject });\n * // through is required!\n *\n * user.addProject(project, { through: { role: 'manager' }});\n * ```\n *\n * All methods allow you to pass either a persisted instance, its primary key, or a mixture:\n *\n * ```js\n * const project = await Project.create({ id: 11 });\n * await user.addProjects([project, 12]);\n * ```\n *\n * If you want to set several target instances, but with different attributes you have to set the attributes on the instance, using a property with the name of the through model:\n *\n * ```js\n * p1.UserProjects = {\n *   started: true\n * }\n * user.setProjects([p1, p2], { through: { started: false }}) // The default value is false, but p1 overrides that.\n * ```\n *\n * Similarly, when fetching through a join table with custom attributes, these attributes will be available as an object with the name of the through model.\n * ```js\n * const projects = await user.getProjects();\n * const p1 = projects[0];\n * p1.UserProjects.started // Is this project started yet?\n * ```\n *\n * In the API reference below, add the name of the association to the method, e.g. for `User.belongsToMany(Project)` the getter will be `user.getProjects()`.\n *\n * @see {@link Model.belongsToMany}\n */\nclass BelongsToMany extends Association {\n  constructor(source, target, options) {\n    super(source, target, options);\n\n    if (this.options.through === undefined || this.options.through === true || this.options.through === null) {\n      throw new AssociationError(`${source.name}.belongsToMany(${target.name}) requires through option, pass either a string or a model`);\n    }\n\n    if (!this.options.through.model) {\n      this.options.through = {\n        model: options.through\n      };\n    }\n\n    this.associationType = 'BelongsToMany';\n    this.targetAssociation = null;\n    this.sequelize = source.sequelize;\n    this.through = { ...this.options.through };\n    this.isMultiAssociation = true;\n    this.doubleLinked = false;\n\n    if (!this.as && this.isSelfAssociation) {\n      throw new AssociationError('\\'as\\' must be defined for many-to-many self-associations');\n    }\n\n    if (this.as) {\n      this.isAliased = true;\n\n      if (_.isPlainObject(this.as)) {\n        this.options.name = this.as;\n        this.as = this.as.plural;\n      } else {\n        this.options.name = {\n          plural: this.as,\n          singular: Utils.singularize(this.as)\n        };\n      }\n    } else {\n      this.as = this.target.options.name.plural;\n      this.options.name = this.target.options.name;\n    }\n\n    this.combinedTableName = Utils.combineTableNames(\n      this.source.tableName,\n      this.isSelfAssociation ? this.as || this.target.tableName : this.target.tableName\n    );\n\n    /*\n    * If self association, this is the target association - Unless we find a pairing association\n    */\n    if (this.isSelfAssociation) {\n      this.targetAssociation = this;\n    }\n\n    /*\n    * Find paired association (if exists)\n    */\n    _.each(this.target.associations, association => {\n      if (association.associationType !== 'BelongsToMany') return;\n      if (association.target !== this.source) return;\n\n      if (this.options.through.model === association.options.through.model) {\n        this.paired = association;\n        association.paired = this;\n      }\n    });\n\n    /*\n    * Default/generated source/target keys\n    */\n    this.sourceKey = this.options.sourceKey || this.source.primaryKeyAttribute;\n    this.sourceKeyField = this.source.rawAttributes[this.sourceKey].field || this.sourceKey;\n\n    if (this.options.targetKey) {\n      this.targetKey = this.options.targetKey;\n      this.targetKeyField = this.target.rawAttributes[this.targetKey].field || this.targetKey;\n    } else {\n      this.targetKeyDefault = true;\n      this.targetKey = this.target.primaryKeyAttribute;\n      this.targetKeyField = this.target.rawAttributes[this.targetKey].field || this.targetKey;\n    }\n\n    this._createForeignAndOtherKeys();\n\n    if (typeof this.through.model === 'string') {\n      if (!this.sequelize.isDefined(this.through.model)) {\n        this.through.model = this.sequelize.define(this.through.model, {}, Object.assign(this.options, {\n          tableName: this.through.model,\n          indexes: [], //we don't want indexes here (as referenced in #2416)\n          paranoid: this.through.paranoid ? this.through.paranoid : false, // Default to non-paranoid join (referenced in #11991)\n          validate: {} // Don't propagate model-level validations\n        }));\n      } else {\n        this.through.model = this.sequelize.model(this.through.model);\n      }\n    }\n\n    Object.assign(this.options, _.pick(this.through.model.options, [\n      'timestamps', 'createdAt', 'updatedAt', 'deletedAt', 'paranoid'\n    ]));\n\n    if (this.paired) {\n      let needInjectPaired = false;\n\n      if (this.targetKeyDefault) {\n        this.targetKey = this.paired.sourceKey;\n        this.targetKeyField = this.paired.sourceKeyField;\n        this._createForeignAndOtherKeys();\n      }\n      if (this.paired.targetKeyDefault) {\n        // in this case paired.otherKey depends on paired.targetKey,\n        // so cleanup previously wrong generated otherKey\n        if (this.paired.targetKey !== this.sourceKey) {\n          delete this.through.model.rawAttributes[this.paired.otherKey];\n          this.paired.targetKey = this.sourceKey;\n          this.paired.targetKeyField = this.sourceKeyField;\n          this.paired._createForeignAndOtherKeys();\n          needInjectPaired = true;\n        }\n      }\n\n      if (this.otherKeyDefault) {\n        this.otherKey = this.paired.foreignKey;\n      }\n      if (this.paired.otherKeyDefault) {\n        // If paired otherKey was inferred we should make sure to clean it up\n        // before adding a new one that matches the foreignKey\n        if (this.paired.otherKey !== this.foreignKey) {\n          delete this.through.model.rawAttributes[this.paired.otherKey];\n          this.paired.otherKey = this.foreignKey;\n          needInjectPaired = true;\n        }\n      }\n\n      if (needInjectPaired) {\n        this.paired._injectAttributes();\n      }\n    }\n\n    if (this.through) {\n      this.throughModel = this.through.model;\n    }\n\n    this.options.tableName = this.combinedName = this.through.model === Object(this.through.model) ? this.through.model.tableName : this.through.model;\n\n    this.associationAccessor = this.as;\n\n    // Get singular and plural names, trying to uppercase the first letter, unless the model forbids it\n    const plural = _.upperFirst(this.options.name.plural);\n    const singular = _.upperFirst(this.options.name.singular);\n\n    this.accessors = {\n      get: `get${plural}`,\n      set: `set${plural}`,\n      addMultiple: `add${plural}`,\n      add: `add${singular}`,\n      create: `create${singular}`,\n      remove: `remove${singular}`,\n      removeMultiple: `remove${plural}`,\n      hasSingle: `has${singular}`,\n      hasAll: `has${plural}`,\n      count: `count${plural}`\n    };\n  }\n\n  _createForeignAndOtherKeys() {\n    /*\n    * Default/generated foreign/other keys\n    */\n    if (_.isObject(this.options.foreignKey)) {\n      this.foreignKeyAttribute = this.options.foreignKey;\n      this.foreignKey = this.foreignKeyAttribute.name || this.foreignKeyAttribute.fieldName;\n    } else {\n      this.foreignKeyAttribute = {};\n      this.foreignKey = this.options.foreignKey || Utils.camelize(\n        [\n          this.source.options.name.singular,\n          this.sourceKey\n        ].join('_')\n      );\n    }\n\n    if (_.isObject(this.options.otherKey)) {\n      this.otherKeyAttribute = this.options.otherKey;\n      this.otherKey = this.otherKeyAttribute.name || this.otherKeyAttribute.fieldName;\n    } else {\n      if (!this.options.otherKey) {\n        this.otherKeyDefault = true;\n      }\n\n      this.otherKeyAttribute = {};\n      this.otherKey = this.options.otherKey || Utils.camelize(\n        [\n          this.isSelfAssociation ? Utils.singularize(this.as) : this.target.options.name.singular,\n          this.targetKey\n        ].join('_')\n      );\n    }\n  }\n\n  // the id is in the target table\n  // or in an extra table which connects two tables\n  _injectAttributes() {\n    this.identifier = this.foreignKey;\n    this.foreignIdentifier = this.otherKey;\n\n    // remove any PKs previously defined by sequelize\n    // but ignore any keys that are part of this association (#5865)\n    _.each(this.through.model.rawAttributes, (attribute, attributeName) => {\n      if (attribute.primaryKey === true && attribute._autoGenerated === true) {\n        if ([this.foreignKey, this.otherKey].includes(attributeName)) {\n          // this key is still needed as it's part of the association\n          // so just set primaryKey to false\n          attribute.primaryKey = false;\n        }\n        else {\n          delete this.through.model.rawAttributes[attributeName];\n        }\n        this.primaryKeyDeleted = true;\n      }\n    });\n\n    const sourceKey = this.source.rawAttributes[this.sourceKey];\n    const sourceKeyType = sourceKey.type;\n    const sourceKeyField = this.sourceKeyField;\n    const targetKey = this.target.rawAttributes[this.targetKey];\n    const targetKeyType = targetKey.type;\n    const targetKeyField = this.targetKeyField;\n    const sourceAttribute = { type: sourceKeyType, ...this.foreignKeyAttribute };\n    const targetAttribute = { type: targetKeyType, ...this.otherKeyAttribute };\n\n    if (this.primaryKeyDeleted === true) {\n      targetAttribute.primaryKey = sourceAttribute.primaryKey = true;\n    } else if (this.through.unique !== false) {\n      let uniqueKey;\n      if (typeof this.options.uniqueKey === 'string' && this.options.uniqueKey !== '') {\n        uniqueKey = this.options.uniqueKey;\n      } else {\n        uniqueKey = [this.through.model.tableName, this.foreignKey, this.otherKey, 'unique'].join('_');\n      }\n      targetAttribute.unique = sourceAttribute.unique = uniqueKey;\n    }\n\n    if (!this.through.model.rawAttributes[this.foreignKey]) {\n      this.through.model.rawAttributes[this.foreignKey] = {\n        _autoGenerated: true\n      };\n    }\n\n    if (!this.through.model.rawAttributes[this.otherKey]) {\n      this.through.model.rawAttributes[this.otherKey] = {\n        _autoGenerated: true\n      };\n    }\n\n    if (this.options.constraints !== false) {\n      sourceAttribute.references = {\n        model: this.source.getTableName(),\n        key: sourceKeyField\n      };\n      // For the source attribute the passed option is the priority\n      sourceAttribute.onDelete = this.options.onDelete || this.through.model.rawAttributes[this.foreignKey].onDelete;\n      sourceAttribute.onUpdate = this.options.onUpdate || this.through.model.rawAttributes[this.foreignKey].onUpdate;\n\n      if (!sourceAttribute.onDelete) sourceAttribute.onDelete = 'CASCADE';\n      if (!sourceAttribute.onUpdate) sourceAttribute.onUpdate = 'CASCADE';\n\n      targetAttribute.references = {\n        model: this.target.getTableName(),\n        key: targetKeyField\n      };\n      // But the for target attribute the previously defined option is the priority (since it could've been set by another belongsToMany call)\n      targetAttribute.onDelete = this.through.model.rawAttributes[this.otherKey].onDelete || this.options.onDelete;\n      targetAttribute.onUpdate = this.through.model.rawAttributes[this.otherKey].onUpdate || this.options.onUpdate;\n\n      if (!targetAttribute.onDelete) targetAttribute.onDelete = 'CASCADE';\n      if (!targetAttribute.onUpdate) targetAttribute.onUpdate = 'CASCADE';\n    }\n\n    Object.assign(this.through.model.rawAttributes[this.foreignKey], sourceAttribute);\n    Object.assign(this.through.model.rawAttributes[this.otherKey], targetAttribute);\n\n    this.through.model.refreshAttributes();\n\n    this.identifierField = this.through.model.rawAttributes[this.foreignKey].field || this.foreignKey;\n    this.foreignIdentifierField = this.through.model.rawAttributes[this.otherKey].field || this.otherKey;\n\n    // For Db2 server, a reference column of a FOREIGN KEY must be unique\n    // else, server throws SQL0573N error. Hence, setting it here explicitly\n    // for non primary columns.\n    if (this.options.sequelize.options.dialect === 'db2' &&\n        this.source.rawAttributes[this.sourceKey].primaryKey !== true) {\n      this.source.rawAttributes[this.sourceKey].unique = true;\n    }\n\n    if (this.paired && !this.paired.foreignIdentifierField) {\n      this.paired.foreignIdentifierField = this.through.model.rawAttributes[this.paired.otherKey].field || this.paired.otherKey;\n    }\n\n    this.toSource = new BelongsTo(this.through.model, this.source, {\n      foreignKey: this.foreignKey\n    });\n    this.manyFromSource = new HasMany(this.source, this.through.model, {\n      foreignKey: this.foreignKey\n    });\n    this.oneFromSource = new HasOne(this.source, this.through.model, {\n      foreignKey: this.foreignKey,\n      sourceKey: this.sourceKey,\n      as: this.through.model.name\n    });\n\n    this.toTarget = new BelongsTo(this.through.model, this.target, {\n      foreignKey: this.otherKey\n    });\n    this.manyFromTarget = new HasMany(this.target, this.through.model, {\n      foreignKey: this.otherKey\n    });\n    this.oneFromTarget = new HasOne(this.target, this.through.model, {\n      foreignKey: this.otherKey,\n      sourceKey: this.targetKey,\n      as: this.through.model.name\n    });\n\n    if (this.paired && this.paired.otherKeyDefault) {\n      this.paired.toTarget = new BelongsTo(this.paired.through.model, this.paired.target, {\n        foreignKey: this.paired.otherKey\n      });\n\n      this.paired.oneFromTarget = new HasOne(this.paired.target, this.paired.through.model, {\n        foreignKey: this.paired.otherKey,\n        sourceKey: this.paired.targetKey,\n        as: this.paired.through.model.name\n      });\n    }\n\n    Helpers.checkNamingCollision(this);\n\n    return this;\n  }\n\n  mixin(obj) {\n    const methods = ['get', 'count', 'hasSingle', 'hasAll', 'set', 'add', 'addMultiple', 'remove', 'removeMultiple', 'create'];\n    const aliases = {\n      hasSingle: 'has',\n      hasAll: 'has',\n      addMultiple: 'add',\n      removeMultiple: 'remove'\n    };\n\n    Helpers.mixinMethods(this, obj, methods, aliases);\n  }\n\n  /**\n   * Get everything currently associated with this, using an optional where clause.\n   *\n   * @see\n   * {@link Model} for a full explanation of options\n   *\n   * @param {Model} instance instance\n   * @param {object} [options] find options\n   * @param {object} [options.where] An optional where clause to limit the associated models\n   * @param {string|boolean} [options.scope] Apply a scope on the related model, or remove its default scope by passing false\n   * @param {string} [options.schema] Apply a schema on the related model\n   * @param {object} [options.through.where] An optional where clause applied to through model (join table)\n   * @param {boolean} [options.through.paranoid=true] If true, only non-deleted records will be returned from the join table. If false, both deleted and non-deleted records will be returned. Only applies if through model is paranoid\n   *\n   * @returns {Promise<Array<Model>>}\n   */\n  async get(instance, options) {\n    options = Utils.cloneDeep(options) || {};\n\n    const through = this.through;\n    let scopeWhere;\n    let throughWhere;\n\n    if (this.scope) {\n      scopeWhere = { ...this.scope };\n    }\n\n    options.where = {\n      [Op.and]: [\n        scopeWhere,\n        options.where\n      ]\n    };\n\n    if (Object(through.model) === through.model) {\n      throughWhere = {};\n      throughWhere[this.foreignKey] = instance.get(this.sourceKey);\n\n      if (through.scope) {\n        Object.assign(throughWhere, through.scope);\n      }\n\n      //If a user pass a where on the options through options, make an \"and\" with the current throughWhere\n      if (options.through && options.through.where) {\n        throughWhere = {\n          [Op.and]: [throughWhere, options.through.where]\n        };\n      }\n\n      options.include = options.include || [];\n      options.include.push({\n        association: this.oneFromTarget,\n        attributes: options.joinTableAttributes,\n        required: true,\n        paranoid: _.get(options.through, 'paranoid', true),\n        where: throughWhere\n      });\n    }\n\n    let model = this.target;\n    if (Object.prototype.hasOwnProperty.call(options, 'scope')) {\n      if (!options.scope) {\n        model = model.unscoped();\n      } else {\n        model = model.scope(options.scope);\n      }\n    }\n\n    if (Object.prototype.hasOwnProperty.call(options, 'schema')) {\n      model = model.schema(options.schema, options.schemaDelimiter);\n    }\n\n    return model.findAll(options);\n  }\n\n  /**\n   * Count everything currently associated with this, using an optional where clause.\n   *\n   * @param {Model} instance instance\n   * @param {object} [options] find options\n   * @param {object} [options.where] An optional where clause to limit the associated models\n   * @param {string|boolean} [options.scope] Apply a scope on the related model, or remove its default scope by passing false\n   *\n   * @returns {Promise<number>}\n   */\n  async count(instance, options) {\n    const sequelize = this.target.sequelize;\n\n    options = Utils.cloneDeep(options);\n    options.attributes = [\n      [sequelize.fn('COUNT', sequelize.col([this.target.name, this.targetKeyField].join('.'))), 'count']\n    ];\n    options.joinTableAttributes = [];\n    options.raw = true;\n    options.plain = true;\n\n    const result = await this.get(instance, options);\n\n    return parseInt(result.count, 10);\n  }\n\n  /**\n   * Check if one or more instance(s) are associated with this. If a list of instances is passed, the function returns true if _all_ instances are associated\n   *\n   * @param {Model} sourceInstance source instance to check for an association with\n   * @param {Model|Model[]|string[]|string|number[]|number} [instances] Can be an array of instances or their primary keys\n   * @param {object} [options] Options passed to getAssociations\n   *\n   * @returns {Promise<boolean>}\n   */\n  async has(sourceInstance, instances, options) {\n    if (!Array.isArray(instances)) {\n      instances = [instances];\n    }\n\n    options = {\n      raw: true,\n      ...options,\n      scope: false,\n      attributes: [this.targetKey],\n      joinTableAttributes: []\n    };\n\n    const instancePrimaryKeys = instances.map(instance => {\n      if (instance instanceof this.target) {\n        return instance.where();\n      }\n      return {\n        [this.targetKey]: instance\n      };\n    });\n\n    options.where = {\n      [Op.and]: [\n        { [Op.or]: instancePrimaryKeys },\n        options.where\n      ]\n    };\n\n    const associatedObjects = await this.get(sourceInstance, options);\n\n    return _.differenceWith(instancePrimaryKeys, associatedObjects,\n      (a, b) => _.isEqual(a[this.targetKey], b[this.targetKey])).length === 0;\n  }\n\n  /**\n   * Set the associated models by passing an array of instances or their primary keys.\n   * Everything that it not in the passed array will be un-associated.\n   *\n   * @param {Model} sourceInstance source instance to associate new instances with\n   * @param {Model|Model[]|string[]|string|number[]|number} [newAssociatedObjects] A single instance or primary key, or a mixed array of persisted instances or primary keys\n   * @param {object} [options] Options passed to `through.findAll`, `bulkCreate`, `update` and `destroy`\n   * @param {object} [options.validate] Run validation for the join model\n   * @param {object} [options.through] Additional attributes for the join table.\n   *\n   * @returns {Promise}\n   */\n  async set(sourceInstance, newAssociatedObjects, options) {\n    options = options || {};\n\n    const sourceKey = this.sourceKey;\n    const targetKey = this.targetKey;\n    const identifier = this.identifier;\n    const foreignIdentifier = this.foreignIdentifier;\n\n    if (newAssociatedObjects === null) {\n      newAssociatedObjects = [];\n    } else {\n      newAssociatedObjects = this.toInstanceArray(newAssociatedObjects);\n    }\n    const where = {\n      [identifier]: sourceInstance.get(sourceKey),\n      ...this.through.scope\n    };\n\n    const updateAssociations = currentRows => {\n      const obsoleteAssociations = [];\n      const promises = [];\n      const defaultAttributes = options.through || {};\n\n      const unassociatedObjects = newAssociatedObjects.filter(obj =>\n        !currentRows.some(currentRow => currentRow[foreignIdentifier] === obj.get(targetKey))\n      );\n\n      for (const currentRow of currentRows) {\n        const newObj = newAssociatedObjects.find(obj => currentRow[foreignIdentifier] === obj.get(targetKey));\n\n        if (!newObj) {\n          obsoleteAssociations.push(currentRow);\n        } else {\n          let throughAttributes = newObj[this.through.model.name];\n          // Quick-fix for subtle bug when using existing objects that might have the through model attached (not as an attribute object)\n          if (throughAttributes instanceof this.through.model) {\n            throughAttributes = {};\n          }\n\n          const attributes = { ...defaultAttributes, ...throughAttributes };\n\n          if (Object.keys(attributes).length) {\n            promises.push(\n              this.through.model.update(attributes, Object.assign(options, {\n                where: {\n                  [identifier]: sourceInstance.get(sourceKey),\n                  [foreignIdentifier]: newObj.get(targetKey)\n                }\n              }\n              ))\n            );\n          }\n        }\n      }\n\n      if (obsoleteAssociations.length > 0) {\n        promises.push(\n          this.through.model.destroy({\n            ...options,\n            where: {\n              [identifier]: sourceInstance.get(sourceKey),\n              [foreignIdentifier]: obsoleteAssociations.map(obsoleteAssociation => obsoleteAssociation[foreignIdentifier]),\n              ...this.through.scope\n            }\n          })\n        );\n      }\n\n      if (unassociatedObjects.length > 0) {\n        const bulk = unassociatedObjects.map(unassociatedObject => {\n          return {\n            ...defaultAttributes,\n            ...unassociatedObject[this.through.model.name],\n            [identifier]: sourceInstance.get(sourceKey),\n            [foreignIdentifier]: unassociatedObject.get(targetKey),\n            ...this.through.scope\n          };\n        });\n\n        promises.push(this.through.model.bulkCreate(bulk, { validate: true, ...options }));\n      }\n\n      return Promise.all(promises);\n    };\n\n    try {\n      const currentRows = await this.through.model.findAll({ ...options, where, raw: true });\n      return await updateAssociations(currentRows);\n    } catch (error) {\n      if (error instanceof EmptyResultError) return updateAssociations([]);\n      throw error;\n    }\n  }\n\n  /**\n   * Associate one or several rows with source instance. It will not un-associate any already associated instance\n   * that may be missing from `newInstances`.\n   *\n   * @param {Model} sourceInstance source instance to associate new instances with\n   * @param {Model|Model[]|string[]|string|number[]|number} [newInstances] A single instance or primary key, or a mixed array of persisted instances or primary keys\n   * @param {object} [options] Options passed to `through.findAll`, `bulkCreate` and `update`\n   * @param {object} [options.validate] Run validation for the join model.\n   * @param {object} [options.through] Additional attributes for the join table.\n   *\n   * @returns {Promise}\n   */\n  async add(sourceInstance, newInstances, options) {\n    // If newInstances is null or undefined, no-op\n    if (!newInstances) return Promise.resolve();\n\n    options = { ...options };\n\n    const association = this;\n    const sourceKey = association.sourceKey;\n    const targetKey = association.targetKey;\n    const identifier = association.identifier;\n    const foreignIdentifier = association.foreignIdentifier;\n    const defaultAttributes = options.through || {};\n\n    newInstances = association.toInstanceArray(newInstances);\n\n    const where = {\n      [identifier]: sourceInstance.get(sourceKey),\n      [foreignIdentifier]: newInstances.map(newInstance => newInstance.get(targetKey)),\n      ...association.through.scope\n    };\n\n    const updateAssociations = currentRows => {\n      const promises = [];\n      const unassociatedObjects = [];\n      const changedAssociations = [];\n      for (const obj of newInstances) {\n        const existingAssociation = currentRows && currentRows.find(current => current[foreignIdentifier] === obj.get(targetKey));\n\n        if (!existingAssociation) {\n          unassociatedObjects.push(obj);\n        } else {\n          const throughAttributes = obj[association.through.model.name];\n          const attributes = { ...defaultAttributes, ...throughAttributes };\n\n          if (Object.keys(attributes).some(attribute => attributes[attribute] !== existingAssociation[attribute])) {\n            changedAssociations.push(obj);\n          }\n        }\n      }\n\n      if (unassociatedObjects.length > 0) {\n        const bulk = unassociatedObjects.map(unassociatedObject => {\n          const throughAttributes = unassociatedObject[association.through.model.name];\n          const attributes = { ...defaultAttributes, ...throughAttributes };\n\n          attributes[identifier] = sourceInstance.get(sourceKey);\n          attributes[foreignIdentifier] = unassociatedObject.get(targetKey);\n\n          Object.assign(attributes, association.through.scope);\n\n          return attributes;\n        });\n\n        promises.push(association.through.model.bulkCreate(bulk, { validate: true, ...options }));\n      }\n\n      for (const assoc of changedAssociations) {\n        let throughAttributes = assoc[association.through.model.name];\n        const attributes = { ...defaultAttributes, ...throughAttributes };\n        // Quick-fix for subtle bug when using existing objects that might have the through model attached (not as an attribute object)\n        if (throughAttributes instanceof association.through.model) {\n          throughAttributes = {};\n        }\n\n        promises.push(association.through.model.update(attributes, Object.assign(options, { where: {\n          [identifier]: sourceInstance.get(sourceKey),\n          [foreignIdentifier]: assoc.get(targetKey)\n        } })));\n      }\n\n      return Promise.all(promises);\n    };\n\n    try {\n      const currentRows = await association.through.model.findAll({ ...options, where, raw: true });\n      const [associations] = await updateAssociations(currentRows);\n      return associations;\n    } catch (error) {\n      if (error instanceof EmptyResultError) return updateAssociations();\n      throw error;\n    }\n  }\n\n  /**\n   * Un-associate one or more instance(s).\n   *\n   * @param {Model} sourceInstance instance to un associate instances with\n   * @param {Model|Model[]|string|string[]|number|number[]} [oldAssociatedObjects] Can be an Instance or its primary key, or a mixed array of instances and primary keys\n   * @param {object} [options] Options passed to `through.destroy`\n   *\n   * @returns {Promise}\n   */\n  remove(sourceInstance, oldAssociatedObjects, options) {\n    const association = this;\n\n    options = options || {};\n\n    oldAssociatedObjects = association.toInstanceArray(oldAssociatedObjects);\n\n    const where = {\n      [association.identifier]: sourceInstance.get(association.sourceKey),\n      [association.foreignIdentifier]: oldAssociatedObjects.map(newInstance => newInstance.get(association.targetKey))\n    };\n\n    return association.through.model.destroy({ ...options, where });\n  }\n\n  /**\n   * Create a new instance of the associated model and associate it with this.\n   *\n   * @param {Model} sourceInstance source instance\n   * @param {object} [values] values for target model\n   * @param {object} [options] Options passed to create and add\n   * @param {object} [options.through] Additional attributes for the join table\n   *\n   * @returns {Promise}\n   */\n  async create(sourceInstance, values, options) {\n    const association = this;\n\n    options = options || {};\n    values = values || {};\n\n    if (Array.isArray(options)) {\n      options = {\n        fields: options\n      };\n    }\n\n    if (association.scope) {\n      Object.assign(values, association.scope);\n      if (options.fields) {\n        options.fields = options.fields.concat(Object.keys(association.scope));\n      }\n    }\n\n    // Create the related model instance\n    const newAssociatedObject = await association.target.create(values, options);\n\n    await sourceInstance[association.accessors.add](newAssociatedObject, _.omit(options, ['fields']));\n    return newAssociatedObject;\n  }\n\n  verifyAssociationAlias(alias) {\n    if (typeof alias === 'string') {\n      return this.as === alias;\n    }\n\n    if (alias && alias.plural) {\n      return this.as === alias.plural;\n    }\n\n    return !this.isAliased;\n  }\n}\n\nmodule.exports = BelongsToMany;\nmodule.exports.BelongsToMany = BelongsToMany;\nmodule.exports.default = BelongsToMany;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,QAAQ,QAAQ;AACtB,MAAM,UAAU,QAAQ;AACxB,MAAM,IAAI,QAAQ;AAClB,MAAM,cAAc,QAAQ;AAC5B,MAAM,YAAY,QAAQ;AAC1B,MAAM,UAAU,QAAQ;AACxB,MAAM,SAAS,QAAQ;AACvB,MAAM,mBAAmB,QAAQ,aAAa;AAC9C,MAAM,mBAAmB,QAAQ,aAAa;AAC9C,MAAM,KAAK,QAAQ;AA6CnB,4BAA4B,YAAY;AAAA,EACtC,YAAY,QAAQ,QAAQ,SAAS;AACnC,UAAM,QAAQ,QAAQ;AAEtB,QAAI,KAAK,QAAQ,YAAY,UAAa,KAAK,QAAQ,YAAY,QAAQ,KAAK,QAAQ,YAAY,MAAM;AACxG,YAAM,IAAI,iBAAiB,GAAG,OAAO,sBAAsB,OAAO;AAAA;AAGpE,QAAI,CAAC,KAAK,QAAQ,QAAQ,OAAO;AAC/B,WAAK,QAAQ,UAAU;AAAA,QACrB,OAAO,QAAQ;AAAA;AAAA;AAInB,SAAK,kBAAkB;AACvB,SAAK,oBAAoB;AACzB,SAAK,YAAY,OAAO;AACxB,SAAK,UAAU,mBAAK,KAAK,QAAQ;AACjC,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AAEpB,QAAI,CAAC,KAAK,MAAM,KAAK,mBAAmB;AACtC,YAAM,IAAI,iBAAiB;AAAA;AAG7B,QAAI,KAAK,IAAI;AACX,WAAK,YAAY;AAEjB,UAAI,EAAE,cAAc,KAAK,KAAK;AAC5B,aAAK,QAAQ,OAAO,KAAK;AACzB,aAAK,KAAK,KAAK,GAAG;AAAA,aACb;AACL,aAAK,QAAQ,OAAO;AAAA,UAClB,QAAQ,KAAK;AAAA,UACb,UAAU,MAAM,YAAY,KAAK;AAAA;AAAA;AAAA,WAGhC;AACL,WAAK,KAAK,KAAK,OAAO,QAAQ,KAAK;AACnC,WAAK,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA;AAG1C,SAAK,oBAAoB,MAAM,kBAC7B,KAAK,OAAO,WACZ,KAAK,oBAAoB,KAAK,MAAM,KAAK,OAAO,YAAY,KAAK,OAAO;AAM1E,QAAI,KAAK,mBAAmB;AAC1B,WAAK,oBAAoB;AAAA;AAM3B,MAAE,KAAK,KAAK,OAAO,cAAc,iBAAe;AAC9C,UAAI,YAAY,oBAAoB;AAAiB;AACrD,UAAI,YAAY,WAAW,KAAK;AAAQ;AAExC,UAAI,KAAK,QAAQ,QAAQ,UAAU,YAAY,QAAQ,QAAQ,OAAO;AACpE,aAAK,SAAS;AACd,oBAAY,SAAS;AAAA;AAAA;AAOzB,SAAK,YAAY,KAAK,QAAQ,aAAa,KAAK,OAAO;AACvD,SAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAE9E,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,YAAY,KAAK,QAAQ;AAC9B,WAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAAA,WACzE;AACL,WAAK,mBAAmB;AACxB,WAAK,YAAY,KAAK,OAAO;AAC7B,WAAK,iBAAiB,KAAK,OAAO,cAAc,KAAK,WAAW,SAAS,KAAK;AAAA;AAGhF,SAAK;AAEL,QAAI,OAAO,KAAK,QAAQ,UAAU,UAAU;AAC1C,UAAI,CAAC,KAAK,UAAU,UAAU,KAAK,QAAQ,QAAQ;AACjD,aAAK,QAAQ,QAAQ,KAAK,UAAU,OAAO,KAAK,QAAQ,OAAO,IAAI,OAAO,OAAO,KAAK,SAAS;AAAA,UAC7F,WAAW,KAAK,QAAQ;AAAA,UACxB,SAAS;AAAA,UACT,UAAU,KAAK,QAAQ,WAAW,KAAK,QAAQ,WAAW;AAAA,UAC1D,UAAU;AAAA;AAAA,aAEP;AACL,aAAK,QAAQ,QAAQ,KAAK,UAAU,MAAM,KAAK,QAAQ;AAAA;AAAA;AAI3D,WAAO,OAAO,KAAK,SAAS,EAAE,KAAK,KAAK,QAAQ,MAAM,SAAS;AAAA,MAC7D;AAAA,MAAc;AAAA,MAAa;AAAA,MAAa;AAAA,MAAa;AAAA;AAGvD,QAAI,KAAK,QAAQ;AACf,UAAI,mBAAmB;AAEvB,UAAI,KAAK,kBAAkB;AACzB,aAAK,YAAY,KAAK,OAAO;AAC7B,aAAK,iBAAiB,KAAK,OAAO;AAClC,aAAK;AAAA;AAEP,UAAI,KAAK,OAAO,kBAAkB;AAGhC,YAAI,KAAK,OAAO,cAAc,KAAK,WAAW;AAC5C,iBAAO,KAAK,QAAQ,MAAM,cAAc,KAAK,OAAO;AACpD,eAAK,OAAO,YAAY,KAAK;AAC7B,eAAK,OAAO,iBAAiB,KAAK;AAClC,eAAK,OAAO;AACZ,6BAAmB;AAAA;AAAA;AAIvB,UAAI,KAAK,iBAAiB;AACxB,aAAK,WAAW,KAAK,OAAO;AAAA;AAE9B,UAAI,KAAK,OAAO,iBAAiB;AAG/B,YAAI,KAAK,OAAO,aAAa,KAAK,YAAY;AAC5C,iBAAO,KAAK,QAAQ,MAAM,cAAc,KAAK,OAAO;AACpD,eAAK,OAAO,WAAW,KAAK;AAC5B,6BAAmB;AAAA;AAAA;AAIvB,UAAI,kBAAkB;AACpB,aAAK,OAAO;AAAA;AAAA;AAIhB,QAAI,KAAK,SAAS;AAChB,WAAK,eAAe,KAAK,QAAQ;AAAA;AAGnC,SAAK,QAAQ,YAAY,KAAK,eAAe,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,MAAM,YAAY,KAAK,QAAQ;AAE7I,SAAK,sBAAsB,KAAK;AAGhC,UAAM,SAAS,EAAE,WAAW,KAAK,QAAQ,KAAK;AAC9C,UAAM,WAAW,EAAE,WAAW,KAAK,QAAQ,KAAK;AAEhD,SAAK,YAAY;AAAA,MACf,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,MACX,aAAa,MAAM;AAAA,MACnB,KAAK,MAAM;AAAA,MACX,QAAQ,SAAS;AAAA,MACjB,QAAQ,SAAS;AAAA,MACjB,gBAAgB,SAAS;AAAA,MACzB,WAAW,MAAM;AAAA,MACjB,QAAQ,MAAM;AAAA,MACd,OAAO,QAAQ;AAAA;AAAA;AAAA,EAInB,6BAA6B;AAI3B,QAAI,EAAE,SAAS,KAAK,QAAQ,aAAa;AACvC,WAAK,sBAAsB,KAAK,QAAQ;AACxC,WAAK,aAAa,KAAK,oBAAoB,QAAQ,KAAK,oBAAoB;AAAA,WACvE;AACL,WAAK,sBAAsB;AAC3B,WAAK,aAAa,KAAK,QAAQ,cAAc,MAAM,SACjD;AAAA,QACE,KAAK,OAAO,QAAQ,KAAK;AAAA,QACzB,KAAK;AAAA,QACL,KAAK;AAAA;AAIX,QAAI,EAAE,SAAS,KAAK,QAAQ,WAAW;AACrC,WAAK,oBAAoB,KAAK,QAAQ;AACtC,WAAK,WAAW,KAAK,kBAAkB,QAAQ,KAAK,kBAAkB;AAAA,WACjE;AACL,UAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,aAAK,kBAAkB;AAAA;AAGzB,WAAK,oBAAoB;AACzB,WAAK,WAAW,KAAK,QAAQ,YAAY,MAAM,SAC7C;AAAA,QACE,KAAK,oBAAoB,MAAM,YAAY,KAAK,MAAM,KAAK,OAAO,QAAQ,KAAK;AAAA,QAC/E,KAAK;AAAA,QACL,KAAK;AAAA;AAAA;AAAA,EAOb,oBAAoB;AAClB,SAAK,aAAa,KAAK;AACvB,SAAK,oBAAoB,KAAK;AAI9B,MAAE,KAAK,KAAK,QAAQ,MAAM,eAAe,CAAC,WAAW,kBAAkB;AACrE,UAAI,UAAU,eAAe,QAAQ,UAAU,mBAAmB,MAAM;AACtE,YAAI,CAAC,KAAK,YAAY,KAAK,UAAU,SAAS,gBAAgB;AAG5D,oBAAU,aAAa;AAAA,eAEpB;AACH,iBAAO,KAAK,QAAQ,MAAM,cAAc;AAAA;AAE1C,aAAK,oBAAoB;AAAA;AAAA;AAI7B,UAAM,YAAY,KAAK,OAAO,cAAc,KAAK;AACjD,UAAM,gBAAgB,UAAU;AAChC,UAAM,iBAAiB,KAAK;AAC5B,UAAM,YAAY,KAAK,OAAO,cAAc,KAAK;AACjD,UAAM,gBAAgB,UAAU;AAChC,UAAM,iBAAiB,KAAK;AAC5B,UAAM,kBAAkB,iBAAE,MAAM,iBAAkB,KAAK;AACvD,UAAM,kBAAkB,iBAAE,MAAM,iBAAkB,KAAK;AAEvD,QAAI,KAAK,sBAAsB,MAAM;AACnC,sBAAgB,aAAa,gBAAgB,aAAa;AAAA,eACjD,KAAK,QAAQ,WAAW,OAAO;AACxC,UAAI;AACJ,UAAI,OAAO,KAAK,QAAQ,cAAc,YAAY,KAAK,QAAQ,cAAc,IAAI;AAC/E,oBAAY,KAAK,QAAQ;AAAA,aACpB;AACL,oBAAY,CAAC,KAAK,QAAQ,MAAM,WAAW,KAAK,YAAY,KAAK,UAAU,UAAU,KAAK;AAAA;AAE5F,sBAAgB,SAAS,gBAAgB,SAAS;AAAA;AAGpD,QAAI,CAAC,KAAK,QAAQ,MAAM,cAAc,KAAK,aAAa;AACtD,WAAK,QAAQ,MAAM,cAAc,KAAK,cAAc;AAAA,QAClD,gBAAgB;AAAA;AAAA;AAIpB,QAAI,CAAC,KAAK,QAAQ,MAAM,cAAc,KAAK,WAAW;AACpD,WAAK,QAAQ,MAAM,cAAc,KAAK,YAAY;AAAA,QAChD,gBAAgB;AAAA;AAAA;AAIpB,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,sBAAgB,aAAa;AAAA,QAC3B,OAAO,KAAK,OAAO;AAAA,QACnB,KAAK;AAAA;AAGP,sBAAgB,WAAW,KAAK,QAAQ,YAAY,KAAK,QAAQ,MAAM,cAAc,KAAK,YAAY;AACtG,sBAAgB,WAAW,KAAK,QAAQ,YAAY,KAAK,QAAQ,MAAM,cAAc,KAAK,YAAY;AAEtG,UAAI,CAAC,gBAAgB;AAAU,wBAAgB,WAAW;AAC1D,UAAI,CAAC,gBAAgB;AAAU,wBAAgB,WAAW;AAE1D,sBAAgB,aAAa;AAAA,QAC3B,OAAO,KAAK,OAAO;AAAA,QACnB,KAAK;AAAA;AAGP,sBAAgB,WAAW,KAAK,QAAQ,MAAM,cAAc,KAAK,UAAU,YAAY,KAAK,QAAQ;AACpG,sBAAgB,WAAW,KAAK,QAAQ,MAAM,cAAc,KAAK,UAAU,YAAY,KAAK,QAAQ;AAEpG,UAAI,CAAC,gBAAgB;AAAU,wBAAgB,WAAW;AAC1D,UAAI,CAAC,gBAAgB;AAAU,wBAAgB,WAAW;AAAA;AAG5D,WAAO,OAAO,KAAK,QAAQ,MAAM,cAAc,KAAK,aAAa;AACjE,WAAO,OAAO,KAAK,QAAQ,MAAM,cAAc,KAAK,WAAW;AAE/D,SAAK,QAAQ,MAAM;AAEnB,SAAK,kBAAkB,KAAK,QAAQ,MAAM,cAAc,KAAK,YAAY,SAAS,KAAK;AACvF,SAAK,yBAAyB,KAAK,QAAQ,MAAM,cAAc,KAAK,UAAU,SAAS,KAAK;AAK5F,QAAI,KAAK,QAAQ,UAAU,QAAQ,YAAY,SAC3C,KAAK,OAAO,cAAc,KAAK,WAAW,eAAe,MAAM;AACjE,WAAK,OAAO,cAAc,KAAK,WAAW,SAAS;AAAA;AAGrD,QAAI,KAAK,UAAU,CAAC,KAAK,OAAO,wBAAwB;AACtD,WAAK,OAAO,yBAAyB,KAAK,QAAQ,MAAM,cAAc,KAAK,OAAO,UAAU,SAAS,KAAK,OAAO;AAAA;AAGnH,SAAK,WAAW,IAAI,UAAU,KAAK,QAAQ,OAAO,KAAK,QAAQ;AAAA,MAC7D,YAAY,KAAK;AAAA;AAEnB,SAAK,iBAAiB,IAAI,QAAQ,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MACjE,YAAY,KAAK;AAAA;AAEnB,SAAK,gBAAgB,IAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MAC/D,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,IAAI,KAAK,QAAQ,MAAM;AAAA;AAGzB,SAAK,WAAW,IAAI,UAAU,KAAK,QAAQ,OAAO,KAAK,QAAQ;AAAA,MAC7D,YAAY,KAAK;AAAA;AAEnB,SAAK,iBAAiB,IAAI,QAAQ,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MACjE,YAAY,KAAK;AAAA;AAEnB,SAAK,gBAAgB,IAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAAA,MAC/D,YAAY,KAAK;AAAA,MACjB,WAAW,KAAK;AAAA,MAChB,IAAI,KAAK,QAAQ,MAAM;AAAA;AAGzB,QAAI,KAAK,UAAU,KAAK,OAAO,iBAAiB;AAC9C,WAAK,OAAO,WAAW,IAAI,UAAU,KAAK,OAAO,QAAQ,OAAO,KAAK,OAAO,QAAQ;AAAA,QAClF,YAAY,KAAK,OAAO;AAAA;AAG1B,WAAK,OAAO,gBAAgB,IAAI,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,QAAQ,OAAO;AAAA,QACpF,YAAY,KAAK,OAAO;AAAA,QACxB,WAAW,KAAK,OAAO;AAAA,QACvB,IAAI,KAAK,OAAO,QAAQ,MAAM;AAAA;AAAA;AAIlC,YAAQ,qBAAqB;AAE7B,WAAO;AAAA;AAAA,EAGT,MAAM,KAAK;AACT,UAAM,UAAU,CAAC,OAAO,SAAS,aAAa,UAAU,OAAO,OAAO,eAAe,UAAU,kBAAkB;AACjH,UAAM,UAAU;AAAA,MACd,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,gBAAgB;AAAA;AAGlB,YAAQ,aAAa,MAAM,KAAK,SAAS;AAAA;AAAA,QAmBrC,IAAI,UAAU,SAAS;AAC3B,cAAU,MAAM,UAAU,YAAY;AAEtC,UAAM,UAAU,KAAK;AACrB,QAAI;AACJ,QAAI;AAEJ,QAAI,KAAK,OAAO;AACd,mBAAa,mBAAK,KAAK;AAAA;AAGzB,YAAQ,QAAQ;AAAA,OACb,GAAG,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA;AAAA;AAIZ,QAAI,OAAO,QAAQ,WAAW,QAAQ,OAAO;AAC3C,qBAAe;AACf,mBAAa,KAAK,cAAc,SAAS,IAAI,KAAK;AAElD,UAAI,QAAQ,OAAO;AACjB,eAAO,OAAO,cAAc,QAAQ;AAAA;AAItC,UAAI,QAAQ,WAAW,QAAQ,QAAQ,OAAO;AAC5C,uBAAe;AAAA,WACZ,GAAG,MAAM,CAAC,cAAc,QAAQ,QAAQ;AAAA;AAAA;AAI7C,cAAQ,UAAU,QAAQ,WAAW;AACrC,cAAQ,QAAQ,KAAK;AAAA,QACnB,aAAa,KAAK;AAAA,QAClB,YAAY,QAAQ;AAAA,QACpB,UAAU;AAAA,QACV,UAAU,EAAE,IAAI,QAAQ,SAAS,YAAY;AAAA,QAC7C,OAAO;AAAA;AAAA;AAIX,QAAI,QAAQ,KAAK;AACjB,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,UAAU;AAC1D,UAAI,CAAC,QAAQ,OAAO;AAClB,gBAAQ,MAAM;AAAA,aACT;AACL,gBAAQ,MAAM,MAAM,QAAQ;AAAA;AAAA;AAIhC,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,WAAW;AAC3D,cAAQ,MAAM,OAAO,QAAQ,QAAQ,QAAQ;AAAA;AAG/C,WAAO,MAAM,QAAQ;AAAA;AAAA,QAajB,MAAM,UAAU,SAAS;AAC7B,UAAM,YAAY,KAAK,OAAO;AAE9B,cAAU,MAAM,UAAU;AAC1B,YAAQ,aAAa;AAAA,MACnB,CAAC,UAAU,GAAG,SAAS,UAAU,IAAI,CAAC,KAAK,OAAO,MAAM,KAAK,gBAAgB,KAAK,QAAQ;AAAA;AAE5F,YAAQ,sBAAsB;AAC9B,YAAQ,MAAM;AACd,YAAQ,QAAQ;AAEhB,UAAM,SAAS,MAAM,KAAK,IAAI,UAAU;AAExC,WAAO,SAAS,OAAO,OAAO;AAAA;AAAA,QAY1B,IAAI,gBAAgB,WAAW,SAAS;AAC5C,QAAI,CAAC,MAAM,QAAQ,YAAY;AAC7B,kBAAY,CAAC;AAAA;AAGf,cAAU;AAAA,MACR,KAAK;AAAA,OACF,UAFK;AAAA,MAGR,OAAO;AAAA,MACP,YAAY,CAAC,KAAK;AAAA,MAClB,qBAAqB;AAAA;AAGvB,UAAM,sBAAsB,UAAU,IAAI,cAAY;AACpD,UAAI,oBAAoB,KAAK,QAAQ;AACnC,eAAO,SAAS;AAAA;AAElB,aAAO;AAAA,SACJ,KAAK,YAAY;AAAA;AAAA;AAItB,YAAQ,QAAQ;AAAA,OACb,GAAG,MAAM;AAAA,QACR,GAAG,GAAG,KAAK;AAAA,QACX,QAAQ;AAAA;AAAA;AAIZ,UAAM,oBAAoB,MAAM,KAAK,IAAI,gBAAgB;AAEzD,WAAO,EAAE,eAAe,qBAAqB,mBAC3C,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,YAAY,EAAE,KAAK,aAAa,WAAW;AAAA;AAAA,QAepE,IAAI,gBAAgB,sBAAsB,SAAS;AACvD,cAAU,WAAW;AAErB,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AACvB,UAAM,aAAa,KAAK;AACxB,UAAM,oBAAoB,KAAK;AAE/B,QAAI,yBAAyB,MAAM;AACjC,6BAAuB;AAAA,WAClB;AACL,6BAAuB,KAAK,gBAAgB;AAAA;AAE9C,UAAM,QAAQ;AAAA,OACX,aAAa,eAAe,IAAI;AAAA,OAC9B,KAAK,QAAQ;AAGlB,UAAM,qBAAqB,iBAAe;AACxC,YAAM,uBAAuB;AAC7B,YAAM,WAAW;AACjB,YAAM,oBAAoB,QAAQ,WAAW;AAE7C,YAAM,sBAAsB,qBAAqB,OAAO,SACtD,CAAC,YAAY,KAAK,gBAAc,WAAW,uBAAuB,IAAI,IAAI;AAG5E,iBAAW,cAAc,aAAa;AACpC,cAAM,SAAS,qBAAqB,KAAK,SAAO,WAAW,uBAAuB,IAAI,IAAI;AAE1F,YAAI,CAAC,QAAQ;AACX,+BAAqB,KAAK;AAAA,eACrB;AACL,cAAI,oBAAoB,OAAO,KAAK,QAAQ,MAAM;AAElD,cAAI,6BAA6B,KAAK,QAAQ,OAAO;AACnD,gCAAoB;AAAA;AAGtB,gBAAM,aAAa,kCAAK,oBAAsB;AAE9C,cAAI,OAAO,KAAK,YAAY,QAAQ;AAClC,qBAAS,KACP,KAAK,QAAQ,MAAM,OAAO,YAAY,OAAO,OAAO,SAAS;AAAA,cAC3D,OAAO;AAAA,iBACJ,aAAa,eAAe,IAAI;AAAA,iBAChC,oBAAoB,OAAO,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAS5C,UAAI,qBAAqB,SAAS,GAAG;AACnC,iBAAS,KACP,KAAK,QAAQ,MAAM,QAAQ,iCACtB,UADsB;AAAA,UAEzB,OAAO;AAAA,aACJ,aAAa,eAAe,IAAI;AAAA,aAChC,oBAAoB,qBAAqB,IAAI,yBAAuB,oBAAoB;AAAA,aACtF,KAAK,QAAQ;AAAA;AAAA;AAMxB,UAAI,oBAAoB,SAAS,GAAG;AAClC,cAAM,OAAO,oBAAoB,IAAI,wBAAsB;AACzD,iBAAO,+DACF,oBACA,mBAAmB,KAAK,QAAQ,MAAM,QAFpC;AAAA,aAGJ,aAAa,eAAe,IAAI;AAAA,aAChC,oBAAoB,mBAAmB,IAAI;AAAA,cACzC,KAAK,QAAQ;AAAA;AAIpB,iBAAS,KAAK,KAAK,QAAQ,MAAM,WAAW,MAAM,iBAAE,UAAU,QAAS;AAAA;AAGzE,aAAO,QAAQ,IAAI;AAAA;AAGrB,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,QAAQ,MAAM,QAAQ,iCAAK,UAAL,EAAc,OAAO,KAAK;AAC/E,aAAO,MAAM,mBAAmB;AAAA,aACzB,OAAP;AACA,UAAI,iBAAiB;AAAkB,eAAO,mBAAmB;AACjE,YAAM;AAAA;AAAA;AAAA,QAgBJ,IAAI,gBAAgB,cAAc,SAAS;AAE/C,QAAI,CAAC;AAAc,aAAO,QAAQ;AAElC,cAAU,mBAAK;AAEf,UAAM,cAAc;AACpB,UAAM,YAAY,YAAY;AAC9B,UAAM,YAAY,YAAY;AAC9B,UAAM,aAAa,YAAY;AAC/B,UAAM,oBAAoB,YAAY;AACtC,UAAM,oBAAoB,QAAQ,WAAW;AAE7C,mBAAe,YAAY,gBAAgB;AAE3C,UAAM,QAAQ;AAAA,OACX,aAAa,eAAe,IAAI;AAAA,OAChC,oBAAoB,aAAa,IAAI,iBAAe,YAAY,IAAI;AAAA,OAClE,YAAY,QAAQ;AAGzB,UAAM,qBAAqB,iBAAe;AACxC,YAAM,WAAW;AACjB,YAAM,sBAAsB;AAC5B,YAAM,sBAAsB;AAC5B,iBAAW,OAAO,cAAc;AAC9B,cAAM,sBAAsB,eAAe,YAAY,KAAK,aAAW,QAAQ,uBAAuB,IAAI,IAAI;AAE9G,YAAI,CAAC,qBAAqB;AACxB,8BAAoB,KAAK;AAAA,eACpB;AACL,gBAAM,oBAAoB,IAAI,YAAY,QAAQ,MAAM;AACxD,gBAAM,aAAa,kCAAK,oBAAsB;AAE9C,cAAI,OAAO,KAAK,YAAY,KAAK,eAAa,WAAW,eAAe,oBAAoB,aAAa;AACvG,gCAAoB,KAAK;AAAA;AAAA;AAAA;AAK/B,UAAI,oBAAoB,SAAS,GAAG;AAClC,cAAM,OAAO,oBAAoB,IAAI,wBAAsB;AACzD,gBAAM,oBAAoB,mBAAmB,YAAY,QAAQ,MAAM;AACvE,gBAAM,aAAa,kCAAK,oBAAsB;AAE9C,qBAAW,cAAc,eAAe,IAAI;AAC5C,qBAAW,qBAAqB,mBAAmB,IAAI;AAEvD,iBAAO,OAAO,YAAY,YAAY,QAAQ;AAE9C,iBAAO;AAAA;AAGT,iBAAS,KAAK,YAAY,QAAQ,MAAM,WAAW,MAAM,iBAAE,UAAU,QAAS;AAAA;AAGhF,iBAAW,SAAS,qBAAqB;AACvC,YAAI,oBAAoB,MAAM,YAAY,QAAQ,MAAM;AACxD,cAAM,aAAa,kCAAK,oBAAsB;AAE9C,YAAI,6BAA6B,YAAY,QAAQ,OAAO;AAC1D,8BAAoB;AAAA;AAGtB,iBAAS,KAAK,YAAY,QAAQ,MAAM,OAAO,YAAY,OAAO,OAAO,SAAS,EAAE,OAAO;AAAA,WACxF,aAAa,eAAe,IAAI;AAAA,WAChC,oBAAoB,MAAM,IAAI;AAAA;AAAA;AAInC,aAAO,QAAQ,IAAI;AAAA;AAGrB,QAAI;AACF,YAAM,cAAc,MAAM,YAAY,QAAQ,MAAM,QAAQ,iCAAK,UAAL,EAAc,OAAO,KAAK;AACtF,YAAM,CAAC,gBAAgB,MAAM,mBAAmB;AAChD,aAAO;AAAA,aACA,OAAP;AACA,UAAI,iBAAiB;AAAkB,eAAO;AAC9C,YAAM;AAAA;AAAA;AAAA,EAaV,OAAO,gBAAgB,sBAAsB,SAAS;AACpD,UAAM,cAAc;AAEpB,cAAU,WAAW;AAErB,2BAAuB,YAAY,gBAAgB;AAEnD,UAAM,QAAQ;AAAA,OACX,YAAY,aAAa,eAAe,IAAI,YAAY;AAAA,OACxD,YAAY,oBAAoB,qBAAqB,IAAI,iBAAe,YAAY,IAAI,YAAY;AAAA;AAGvG,WAAO,YAAY,QAAQ,MAAM,QAAQ,iCAAK,UAAL,EAAc;AAAA;AAAA,QAanD,OAAO,gBAAgB,QAAQ,SAAS;AAC5C,UAAM,cAAc;AAEpB,cAAU,WAAW;AACrB,aAAS,UAAU;AAEnB,QAAI,MAAM,QAAQ,UAAU;AAC1B,gBAAU;AAAA,QACR,QAAQ;AAAA;AAAA;AAIZ,QAAI,YAAY,OAAO;AACrB,aAAO,OAAO,QAAQ,YAAY;AAClC,UAAI,QAAQ,QAAQ;AAClB,gBAAQ,SAAS,QAAQ,OAAO,OAAO,OAAO,KAAK,YAAY;AAAA;AAAA;AAKnE,UAAM,sBAAsB,MAAM,YAAY,OAAO,OAAO,QAAQ;AAEpE,UAAM,eAAe,YAAY,UAAU,KAAK,qBAAqB,EAAE,KAAK,SAAS,CAAC;AACtF,WAAO;AAAA;AAAA,EAGT,uBAAuB,OAAO;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,KAAK,OAAO;AAAA;AAGrB,QAAI,SAAS,MAAM,QAAQ;AACzB,aAAO,KAAK,OAAO,MAAM;AAAA;AAG3B,WAAO,CAAC,KAAK;AAAA;AAAA;AAIjB,OAAO,UAAU;AACjB,OAAO,QAAQ,gBAAgB;AAC/B,OAAO,QAAQ,UAAU;", "names": []}