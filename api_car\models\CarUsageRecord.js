module.exports = (sequelize, DataTypes) => {
  const CarUsageRecord = sequelize.define('CarUsageRecord', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    vehicle_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicles',
        key: 'id'
      }
    },
    purpose: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 255]
      }
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: false
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: false,
      validate: {
        isAfterStartTime(value) {
          if (value <= this.start_time) {
            throw new Error('结束时间必须晚于开始时间');
          }
        }
      }
    },
    status: {
      type: DataTypes.ENUM('待审批', '已批准', '已拒绝', '使用中', '已完成'),
      allowNull: false,
      defaultValue: '待审批'
    },
    mileage: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      }
    },
    destination: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    passengers: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 20
      }
    },
    approver_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'approvers',
        key: 'id'
      }
    },
    approve_time: {
      type: DataTypes.DATE,
      allowNull: true
    },
    submit_time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    comments: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'car_usage_records'
  });

  return CarUsageRecord;
};
