
// 车辆管理页面
Page({
  data: {
    vehicles: [],                // 车辆列表数据
    showAddDialog: false,        // 是否显示添加/编辑对话框
    currentVehicle: null,        // 当前正在编辑的车辆对象
    formData: {                  // 表单数据
      plateNumber: '',           // 车牌号
      type: '',                  // 车型（如：小型轿车、SUV等）
      brand: '',                 // 品牌（如：丰田、本田等）
      model: '',                 // 型号（如：凯美瑞、CR-V等）
      status: 'available'        // 状态（available:可用，inuse:使用中）
    }
  },

  onLoad: function(options) {
    // 页面加载时获取车辆列表
    this.loadVehicles();
  },

  // 加载车辆列表数据
  loadVehicles: function() {
    // TODO: 替换为实际API调用
    // 示例数据
    const demoData = [
      {
        id: 1,                     // 车辆ID
        plateNumber: "粤A12345",   // 车牌号
        type: "小型轿车",          // 车型
        brand: "丰田",             // 品牌
        model: "凯美瑞",           // 型号
        status: "available"        // 状态（可用）
      },
      {
        id: 2,                     // 车辆ID
        plateNumber: "粤B67890",   // 车牌号
        type: "SUV",               // 车型
        brand: "本田",             // 品牌
        model: "CR-V",             // 型号
        status: "inuse"            // 状态（使用中）
      }
    ];
    // 更新页面数据
    this.setData({ vehicles: demoData });
  },

  // 打开添加车辆对话框
  openAddDialog: function() {
    // 重置表单数据并显示对话框
    this.setData({
      showAddDialog: true,         // 显示对话框
      currentVehicle: null,        // 清空当前编辑的车辆
      formData: {                  // 重置表单数据
        plateNumber: '',           // 车牌号
        type: '',                  // 车型
        brand: '',                 // 品牌
        model: '',                 // 型号
        status: 'available'        // 状态（默认可用）
      }
    });
  },

  // 打开编辑车辆对话框
  openEditDialog: function(e) {
    const { vehicle } = e.currentTarget.dataset;  // 获取要编辑的车辆数据
    // 设置表单数据并显示对话框
    this.setData({
      showAddDialog: true,         // 显示对话框
      currentVehicle: vehicle,     // 设置当前编辑的车辆
      formData: { ...vehicle }     // 复制车辆数据到表单
    });
  },

  // 处理表单输入
  onFormInput: function(e) {
    const { field } = e.currentTarget.dataset;  // 获取输入字段名
    const { value } = e.detail;                 // 获取输入的值
    // 更新表单数据
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交表单（添加/编辑车辆）
  submitForm: function() {
    const { currentVehicle, formData } = this.data;  // 获取表单数据
    // TODO: 替换为实际API调用
    if (currentVehicle) {
      // 编辑现有车辆
      wx.showToast({
        title: '车辆信息已更新',
        icon: 'success'
      });
    } else {
      // 添加新车辆
      wx.showToast({
        title: '车辆添加成功',
        icon: 'success'
      });
    }
    this.setData({ showAddDialog: false });  // 关闭对话框
    this.loadVehicles();  // 重新加载车辆列表
  },

  // 删除车辆
  deleteVehicle: function(e) {
    const { id } = e.currentTarget.dataset;  // 获取要删除的车辆ID
    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这辆车吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认删除
          // TODO: 替换为实际API调用
          wx.showToast({
            title: '车辆已删除',
            icon: 'success'
          });
          this.loadVehicles();  // 重新加载车辆列表
        }
      }
    });
  },

  // 关闭对话框
  closeDialog: function() {
    this.setData({ showAddDialog: false });  // 隐藏对话框
  }
});