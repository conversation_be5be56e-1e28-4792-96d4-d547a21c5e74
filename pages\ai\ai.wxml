<!--ai.wxml-->
<view class="container">
  <view class="chat-window">
    <view class="message-list">
      <block wx:for="{{questions}}" wx:key="question">
        <view class="message" bindtap="switchQuestion" data-index="{{index}}">
          <text>{{item.question}}</text>
        </view>
      </block>
    </view>
    <view class="answer-pane">
      <text>{{questions[currentQuestionIndex].answer}}</text>
    </view>
  </view>
</view>
