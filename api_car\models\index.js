const { Sequelize } = require('sequelize');
const config = require('../config/database');

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 创建Sequelize实例
const sequelize = new Sequelize({
  dialect: dbConfig.dialect,
  storage: dbConfig.storage,
  logging: dbConfig.logging,
  define: dbConfig.define
});

// 导入模型
const User = require('./User')(sequelize, Sequelize.DataTypes);
const Approver = require('./Approver')(sequelize, Sequelize.DataTypes);
const Vehicle = require('./Vehicle')(sequelize, Sequelize.DataTypes);
const CarUsageRecord = require('./CarUsageRecord')(sequelize, Sequelize.DataTypes);
const NotificationSettings = require('./NotificationSettings')(sequelize, Sequelize.DataTypes);

// 定义关联关系
// 用户和通知设置 1:1
User.hasOne(NotificationSettings, { foreignKey: 'user_id', as: 'notificationSettings' });
NotificationSettings.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// 用车记录和用户 N:1
CarUsageRecord.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
User.hasMany(CarUsageRecord, { foreignKey: 'user_id', as: 'carUsageRecords' });

// 用车记录和车辆 N:1
CarUsageRecord.belongsTo(Vehicle, { foreignKey: 'vehicle_id', as: 'vehicle' });
Vehicle.hasMany(CarUsageRecord, { foreignKey: 'vehicle_id', as: 'carUsageRecords' });

// 用车记录和审批人 N:1
CarUsageRecord.belongsTo(Approver, { foreignKey: 'approver_id', as: 'approver' });
Approver.hasMany(CarUsageRecord, { foreignKey: 'approver_id', as: 'carUsageRecords' });

const db = {
  sequelize,
  Sequelize,
  User,
  Approver,
  Vehicle,
  CarUsageRecord,
  NotificationSettings
};

module.exports = db;
