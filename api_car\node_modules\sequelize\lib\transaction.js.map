{"version": 3, "sources": ["../src/transaction.js"], "sourcesContent": ["'use strict';\n\n/**\n * The transaction object is used to identify a running transaction.\n * It is created by calling `Sequelize.transaction()`.\n * To run a query under a transaction, you should pass the transaction in the options object.\n *\n * @class Transaction\n * @see {@link Sequelize.transaction}\n */\nclass Transaction {\n  /**\n   * Creates a new transaction instance\n   *\n   * @param {Sequelize} sequelize A configured sequelize Instance\n   * @param {object} options An object with options\n   * @param {string} [options.type] Sets the type of the transaction. Sqlite only\n   * @param {string} [options.isolationLevel] Sets the isolation level of the transaction.\n   * @param {string} [options.deferrable] Sets the constraints to be deferred or immediately checked. PostgreSQL only\n   * @param {boolean} [options.readOnly] Whether this transaction will only be used to read data. Used to determine whether sequelize is allowed to use a read replication server.\n   */\n  constructor(sequelize, options) {\n    this.sequelize = sequelize;\n    this.savepoints = [];\n    this._afterCommitHooks = [];\n\n    // get dialect specific transaction options\n    const generateTransactionId = this.sequelize.dialect.queryGenerator.generateTransactionId;\n\n    this.options = {\n      type: sequelize.options.transactionType,\n      isolationLevel: sequelize.options.isolationLevel,\n      readOnly: false,\n      ...options\n    };\n\n    this.parent = this.options.transaction;\n\n    if (this.parent) {\n      this.id = this.parent.id;\n      this.parent.savepoints.push(this);\n      this.name = `${this.id}-sp-${this.parent.savepoints.length}`;\n    } else {\n      this.id = this.name = generateTransactionId();\n    }\n\n    delete this.options.transaction;\n  }\n\n  /**\n   * Commit the transaction\n   *\n   * @returns {Promise}\n   */\n  async commit() {\n    if (this.finished) {\n      throw new Error(`Transaction cannot be committed because it has been finished with state: ${this.finished}`);\n    }\n\n    try {\n      await this.sequelize.getQueryInterface().commitTransaction(this, this.options);\n      this.cleanup();\n    } catch (e) {\n      console.warn(`Committing transaction ${this.id} failed with error ${JSON.stringify(e.message)}. We are killing its connection as it is now in an undetermined state.`);\n      await this.forceCleanup();\n\n      throw e;\n    } finally {\n      this.finished = 'commit';\n      for (const hook of this._afterCommitHooks) {\n        await hook.apply(this, [this]);\n      }\n    }\n  }\n\n  /**\n   * Rollback (abort) the transaction\n   *\n   * @returns {Promise}\n   */\n  async rollback() {\n    if (this.finished) {\n      throw new Error(`Transaction cannot be rolled back because it has been finished with state: ${this.finished}`);\n    }\n\n    if (!this.connection) {\n      throw new Error('Transaction cannot be rolled back because it never started');\n    }\n\n    try {\n      await this\n        .sequelize\n        .getQueryInterface()\n        .rollbackTransaction(this, this.options);\n\n      this.cleanup();\n    } catch (e) {\n      console.warn(`Rolling back transaction ${this.id} failed with error ${JSON.stringify(e.message)}. We are killing its connection as it is now in an undetermined state.`);\n      await this.forceCleanup();\n\n      throw e;\n    }\n  }\n\n  /**\n   * Called to acquire a connection to use and set the correct options on the connection.\n   * We should ensure all of the environment that's set up is cleaned up in `cleanup()` below.\n   *\n   * @param {boolean} useCLS Defaults to true: Use CLS (Continuation Local Storage) with Sequelize. With CLS, all queries within the transaction callback will automatically receive the transaction object.\n   * @returns {Promise}\n   */\n  async prepareEnvironment(useCLS = true) {\n    let connectionPromise;\n\n    if (this.parent) {\n      connectionPromise = Promise.resolve(this.parent.connection);\n    } else {\n      const acquireOptions = { uuid: this.id };\n      if (this.options.readOnly) {\n        acquireOptions.type = 'SELECT';\n      }\n      connectionPromise = this.sequelize.connectionManager.getConnection(acquireOptions);\n    }\n\n    let result;\n    const connection = await connectionPromise;\n    this.connection = connection;\n    this.connection.uuid = this.id;\n\n    try {\n      await this.begin();\n      result = await this.setDeferrable();\n    } catch (setupErr) {\n      try {\n        result = await this.rollback();\n      } finally {\n        throw setupErr; // eslint-disable-line no-unsafe-finally\n      }\n    }\n\n    // TODO (@ephys) [>=7.0.0]: move this inside of sequelize.transaction, remove parameter.\n    if (useCLS && this.sequelize.constructor._cls) {\n      this.sequelize.constructor._cls.set('transaction', this);\n    }\n\n    return result;\n  }\n\n  async setDeferrable() {\n    if (this.options.deferrable) {\n      return await this\n        .sequelize\n        .getQueryInterface()\n        .deferConstraints(this, this.options);\n    }\n  }\n\n  async begin() {\n    const queryInterface = this.sequelize.getQueryInterface();\n\n    if ( this.sequelize.dialect.supports.settingIsolationLevelDuringTransaction ) {\n      await queryInterface.startTransaction(this, this.options);\n      return queryInterface.setIsolationLevel(this, this.options.isolationLevel, this.options);\n    }\n\n    await queryInterface.setIsolationLevel(this, this.options.isolationLevel, this.options);\n\n    return queryInterface.startTransaction(this, this.options);\n  }\n\n  cleanup() {\n    // Don't release the connection if there's a parent transaction or\n    // if we've already cleaned up\n    if (this.parent || this.connection.uuid === undefined) {\n      return;\n    }\n\n    this._clearCls();\n    this.sequelize.connectionManager.releaseConnection(this.connection);\n    this.connection.uuid = undefined;\n  }\n\n  /**\n   * Kills the connection this transaction uses.\n   * Used as a last resort, for instance because COMMIT or ROLLBACK resulted in an error\n   * and the transaction is left in a broken state,\n   * and releasing the connection to the pool would be dangerous.\n   */\n  async forceCleanup() {\n    // Don't release the connection if there's a parent transaction or\n    // if we've already cleaned up\n    if (this.parent || this.connection.uuid === undefined) {\n      return;\n    }\n\n    this._clearCls();\n    await this.sequelize.connectionManager.destroyConnection(this.connection);\n    this.connection.uuid = undefined;\n  }\n\n  _clearCls() {\n    const cls = this.sequelize.constructor._cls;\n\n    if (cls) {\n      if (cls.get('transaction') === this) {\n        cls.set('transaction', null);\n      }\n    }\n  }\n\n  /**\n   * A hook that is run after a transaction is committed\n   *\n   * @param {Function} fn   A callback function that is called with the committed transaction\n   * @name afterCommit\n   * @memberof Sequelize.Transaction\n   */\n  afterCommit(fn) {\n    if (!fn || typeof fn !== 'function') {\n      throw new Error('\"fn\" must be a function');\n    }\n    this._afterCommitHooks.push(fn);\n  }\n\n  /**\n   * Types can be set per-transaction by passing `options.type` to `sequelize.transaction`.\n   * Default to `DEFERRED` but you can override the default type by passing `options.transactionType` in `new Sequelize`.\n   * Sqlite only.\n   *\n   * Pass in the desired level as the first argument:\n   *\n   * @example\n   * try {\n   *   await sequelize.transaction({ type: Sequelize.Transaction.TYPES.EXCLUSIVE }, transaction => {\n   *      // your transactions\n   *   });\n   *   // transaction has been committed. Do something after the commit if required.\n   * } catch(err) {\n   *   // do something with the err.\n   * }\n   *\n   * @property DEFERRED\n   * @property IMMEDIATE\n   * @property EXCLUSIVE\n   */\n  static get TYPES() {\n    return {\n      DEFERRED: 'DEFERRED',\n      IMMEDIATE: 'IMMEDIATE',\n      EXCLUSIVE: 'EXCLUSIVE'\n    };\n  }\n\n  /**\n   * Isolation levels can be set per-transaction by passing `options.isolationLevel` to `sequelize.transaction`.\n   * Sequelize uses the default isolation level of the database, you can override this by passing `options.isolationLevel` in Sequelize constructor options.\n   *\n   * Pass in the desired level as the first argument:\n   *\n   * @example\n   * try {\n   *   const result = await sequelize.transaction({isolationLevel: Sequelize.Transaction.ISOLATION_LEVELS.SERIALIZABLE}, transaction => {\n   *     // your transactions\n   *   });\n   *   // transaction has been committed. Do something after the commit if required.\n   * } catch(err) {\n   *   // do something with the err.\n   * }\n   *\n   * @property READ_UNCOMMITTED\n   * @property READ_COMMITTED\n   * @property REPEATABLE_READ\n   * @property SERIALIZABLE\n   */\n  static get ISOLATION_LEVELS() {\n    return {\n      READ_UNCOMMITTED: 'READ UNCOMMITTED',\n      READ_COMMITTED: 'READ COMMITTED',\n      REPEATABLE_READ: 'REPEATABLE READ',\n      SERIALIZABLE: 'SERIALIZABLE'\n    };\n  }\n\n\n  /**\n   * Possible options for row locking. Used in conjunction with `find` calls:\n   *\n   * @example\n   * // t1 is a transaction\n   * Model.findAll({\n   *   where: ...,\n   *   transaction: t1,\n   *   lock: t1.LOCK...\n   * });\n   *\n   * @example <caption>Postgres also supports specific locks while eager loading by using OF:</caption>\n   * UserModel.findAll({\n   *   where: ...,\n   *   include: [TaskModel, ...],\n   *   transaction: t1,\n   *   lock: {\n   *     level: t1.LOCK...,\n   *     of: UserModel\n   *   }\n   * });\n   *\n   * # UserModel will be locked but TaskModel won't!\n   *\n   * @example <caption>You can also skip locked rows:</caption>\n   * // t1 is a transaction\n   * Model.findAll({\n   *   where: ...,\n   *   transaction: t1,\n   *   lock: true,\n   *   skipLocked: true\n   * });\n   * # The query will now return any rows that aren't locked by another transaction\n   *\n   * @returns {object}\n   * @property UPDATE\n   * @property SHARE\n   * @property KEY_SHARE Postgres 9.3+ only\n   * @property NO_KEY_UPDATE Postgres 9.3+ only\n   */\n  static get LOCK() {\n    return {\n      UPDATE: 'UPDATE',\n      SHARE: 'SHARE',\n      KEY_SHARE: 'KEY SHARE',\n      NO_KEY_UPDATE: 'NO KEY UPDATE'\n    };\n  }\n\n  /**\n   * Please see {@link Transaction.LOCK}\n   */\n  get LOCK() {\n    return Transaction.LOCK;\n  }\n}\n\nmodule.exports = Transaction;\nmodule.exports.Transaction = Transaction;\nmodule.exports.default = Transaction;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAUA,kBAAkB;AAAA,EAWhB,YAAY,WAAW,SAAS;AAC9B,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,oBAAoB;AAGzB,UAAM,wBAAwB,KAAK,UAAU,QAAQ,eAAe;AAEpE,SAAK,UAAU;AAAA,MACb,MAAM,UAAU,QAAQ;AAAA,MACxB,gBAAgB,UAAU,QAAQ;AAAA,MAClC,UAAU;AAAA,OACP;AAGL,SAAK,SAAS,KAAK,QAAQ;AAE3B,QAAI,KAAK,QAAQ;AACf,WAAK,KAAK,KAAK,OAAO;AACtB,WAAK,OAAO,WAAW,KAAK;AAC5B,WAAK,OAAO,GAAG,KAAK,SAAS,KAAK,OAAO,WAAW;AAAA,WAC/C;AACL,WAAK,KAAK,KAAK,OAAO;AAAA;AAGxB,WAAO,KAAK,QAAQ;AAAA;AAAA,QAQhB,SAAS;AACb,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,4EAA4E,KAAK;AAAA;AAGnG,QAAI;AACF,YAAM,KAAK,UAAU,oBAAoB,kBAAkB,MAAM,KAAK;AACtE,WAAK;AAAA,aACE,GAAP;AACA,cAAQ,KAAK,0BAA0B,KAAK,wBAAwB,KAAK,UAAU,EAAE;AACrF,YAAM,KAAK;AAEX,YAAM;AAAA,cACN;AACA,WAAK,WAAW;AAChB,iBAAW,QAAQ,KAAK,mBAAmB;AACzC,cAAM,KAAK,MAAM,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,QAUxB,WAAW;AACf,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,8EAA8E,KAAK;AAAA;AAGrG,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,IAAI,MAAM;AAAA;AAGlB,QAAI;AACF,YAAM,KACH,UACA,oBACA,oBAAoB,MAAM,KAAK;AAElC,WAAK;AAAA,aACE,GAAP;AACA,cAAQ,KAAK,4BAA4B,KAAK,wBAAwB,KAAK,UAAU,EAAE;AACvF,YAAM,KAAK;AAEX,YAAM;AAAA;AAAA;AAAA,QAWJ,mBAAmB,SAAS,MAAM;AACtC,QAAI;AAEJ,QAAI,KAAK,QAAQ;AACf,0BAAoB,QAAQ,QAAQ,KAAK,OAAO;AAAA,WAC3C;AACL,YAAM,iBAAiB,EAAE,MAAM,KAAK;AACpC,UAAI,KAAK,QAAQ,UAAU;AACzB,uBAAe,OAAO;AAAA;AAExB,0BAAoB,KAAK,UAAU,kBAAkB,cAAc;AAAA;AAGrE,QAAI;AACJ,UAAM,aAAa,MAAM;AACzB,SAAK,aAAa;AAClB,SAAK,WAAW,OAAO,KAAK;AAE5B,QAAI;AACF,YAAM,KAAK;AACX,eAAS,MAAM,KAAK;AAAA,aACb,UAAP;AACA,UAAI;AACF,iBAAS,MAAM,KAAK;AAAA,gBACpB;AACA,cAAM;AAAA;AAAA;AAKV,QAAI,UAAU,KAAK,UAAU,YAAY,MAAM;AAC7C,WAAK,UAAU,YAAY,KAAK,IAAI,eAAe;AAAA;AAGrD,WAAO;AAAA;AAAA,QAGH,gBAAgB;AACpB,QAAI,KAAK,QAAQ,YAAY;AAC3B,aAAO,MAAM,KACV,UACA,oBACA,iBAAiB,MAAM,KAAK;AAAA;AAAA;AAAA,QAI7B,QAAQ;AACZ,UAAM,iBAAiB,KAAK,UAAU;AAEtC,QAAK,KAAK,UAAU,QAAQ,SAAS,wCAAyC;AAC5E,YAAM,eAAe,iBAAiB,MAAM,KAAK;AACjD,aAAO,eAAe,kBAAkB,MAAM,KAAK,QAAQ,gBAAgB,KAAK;AAAA;AAGlF,UAAM,eAAe,kBAAkB,MAAM,KAAK,QAAQ,gBAAgB,KAAK;AAE/E,WAAO,eAAe,iBAAiB,MAAM,KAAK;AAAA;AAAA,EAGpD,UAAU;AAGR,QAAI,KAAK,UAAU,KAAK,WAAW,SAAS,QAAW;AACrD;AAAA;AAGF,SAAK;AACL,SAAK,UAAU,kBAAkB,kBAAkB,KAAK;AACxD,SAAK,WAAW,OAAO;AAAA;AAAA,QASnB,eAAe;AAGnB,QAAI,KAAK,UAAU,KAAK,WAAW,SAAS,QAAW;AACrD;AAAA;AAGF,SAAK;AACL,UAAM,KAAK,UAAU,kBAAkB,kBAAkB,KAAK;AAC9D,SAAK,WAAW,OAAO;AAAA;AAAA,EAGzB,YAAY;AACV,UAAM,MAAM,KAAK,UAAU,YAAY;AAEvC,QAAI,KAAK;AACP,UAAI,IAAI,IAAI,mBAAmB,MAAM;AACnC,YAAI,IAAI,eAAe;AAAA;AAAA;AAAA;AAAA,EAY7B,YAAY,IAAI;AACd,QAAI,CAAC,MAAM,OAAO,OAAO,YAAY;AACnC,YAAM,IAAI,MAAM;AAAA;AAElB,SAAK,kBAAkB,KAAK;AAAA;AAAA,aAwBnB,QAAQ;AACjB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW;AAAA,MACX,WAAW;AAAA;AAAA;AAAA,aAyBJ,mBAAmB;AAC5B,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,cAAc;AAAA;AAAA;AAAA,aA6CP,OAAO;AAChB,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA;AAAA;AAAA,MAOf,OAAO;AACT,WAAO,YAAY;AAAA;AAAA;AAIvB,OAAO,UAAU;AACjB,OAAO,QAAQ,cAAc;AAC7B,OAAO,QAAQ,UAAU;", "names": []}