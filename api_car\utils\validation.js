const Joi = require('joi');

// 用户相关验证规则
const userSchemas = {
  updateUser: Joi.object({
    name: Joi.string().min(1).max(100),
    phone: Joi.string().pattern(/^[0-9+\-\s()]*$/).allow(''),
    email: Joi.string().email().allow('')
  })
};

// 审批人相关验证规则
const approverSchemas = {
  createApprover: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    role: Joi.string().min(1).max(100).required(),
    department: Joi.string().max(100).allow(''),
    phone: Joi.string().pattern(/^[0-9+\-\s()]*$/).allow(''),
    email: Joi.string().email().allow('')
  }),
  updateApprover: Joi.object({
    name: Joi.string().min(1).max(100),
    role: Joi.string().min(1).max(100),
    department: Joi.string().max(100).allow(''),
    phone: Joi.string().pattern(/^[0-9+\-\s()]*$/).allow(''),
    email: Joi.string().email().allow('')
  })
};

// 车辆相关验证规则
const vehicleSchemas = {
  createVehicle: Joi.object({
    plateNumber: Joi.string().min(1).max(20).required(),
    type: Joi.string().min(1).max(50).required(),
    brand: Joi.string().min(1).max(50).required(),
    model: Joi.string().min(1).max(50).required(),
    status: Joi.string().valid('可用', '使用中', '维修中').default('可用'),
    mileage: Joi.number().integer().min(0).default(0),
    purchaseDate: Joi.date().iso(),
    insuranceExpiry: Joi.date().iso()
  }),
  updateVehicle: Joi.object({
    status: Joi.string().valid('可用', '使用中', '维修中'),
    mileage: Joi.number().integer().min(0),
    lastMaintenance: Joi.date().iso()
  }),
  vehicleQuery: Joi.object({
    status: Joi.string().valid('可用', '使用中', '维修中'),
    type: Joi.string(),
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(10)
  })
};

// 用车记录相关验证规则
const carUsageSchemas = {
  createRecord: Joi.object({
    vehicleId: Joi.number().integer().required(),
    purpose: Joi.string().min(1).max(255).required(),
    startTime: Joi.date().iso().required(),
    endTime: Joi.date().iso().greater(Joi.ref('startTime')).required(),
    destination: Joi.string().max(255).allow(''),
    passengers: Joi.number().integer().min(1).max(20),
    approverId: Joi.number().integer().required(),
    remarks: Joi.string().allow('')
  }),
  updateRecord: Joi.object({
    status: Joi.string().valid('待审批', '已批准', '已拒绝', '使用中', '已完成'),
    comments: Joi.string().allow(''),
    mileage: Joi.number().integer().min(0)
  }),
  historyQuery: Joi.object({
    userId: Joi.number().integer(),
    vehicleId: Joi.number().integer(),
    status: Joi.string().valid('待审批', '已批准', '已拒绝', '使用中', '已完成'),
    startDate: Joi.date().iso(),
    endDate: Joi.date().iso(),
    page: Joi.number().integer().min(1).default(1),
    pageSize: Joi.number().integer().min(1).max(100).default(10)
  })
};

// 通知设置验证规则
const notificationSchemas = {
  updateSettings: Joi.object({
    approvalNotice: Joi.boolean(),
    usageReminder: Joi.boolean(),
    maintenanceAlert: Joi.boolean(),
    systemNotification: Joi.boolean(),
    emailNotification: Joi.boolean(),
    smsNotification: Joi.boolean()
  })
};

// 通用参数验证
const commonSchemas = {
  idParam: Joi.object({
    id: Joi.number().integer().required()
  })
};

module.exports = {
  userSchemas,
  approverSchemas,
  vehicleSchemas,
  carUsageSchemas,
  notificationSchemas,
  commonSchemas
};
