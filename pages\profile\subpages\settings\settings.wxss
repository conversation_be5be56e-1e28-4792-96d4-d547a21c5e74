
/* settings.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 卡片样式 */
.settings-card, .tips-card {
  background: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header, .tips-header {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 设置列表 */
.settings-list {
  padding: 0 20rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  padding-right: 20rpx;
}

.setting-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.setting-desc {
  font-size: 26rpx;
  color: #999;
}

/* 提示卡片 */
.tips-content {
  padding: 20rpx;
}

.tips-content text {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 10rpx;
}