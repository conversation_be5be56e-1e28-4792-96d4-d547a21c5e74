
Page({
  data: {
    rating: 5,
    comment: '',
    canSubmit: false
  },

  onLoad: function(options) {
    // 页面加载时执行
  },

  // 评分变化
  onRatingChange: function(e) {
    this.setData({
      rating: e.detail.value,
      canSubmit: true
    });
  },

  // 评论输入
  onCommentInput: function(e) {
    this.setData({
      comment: e.detail.value,
      canSubmit: e.detail.value.trim().length > 0
    });
  },

  // 提交评价
  submitFeedback: function() {
    if (!this.data.canSubmit) return;
    
    const { rating, comment } = this.data;
    // TODO: 实现提交逻辑
    wx.showToast({
      title: '评价已提交',
      icon: 'success'
    });
    
    // 清空表单
    this.setData({
      rating: 5,
      comment: '',
      canSubmit: false
    });
  }
});