
/* dataManage.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.backup-btn {
  background-color: #1aad19;
  color: #fff;
  font-size: 28rpx;
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
}

.backup-list {
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.backup-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.backup-item:last-child {
  border-bottom: none;
}

.backup-info {
  flex: 1;
  padding-right: 20rpx;
}

.backup-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.backup-date {
  font-size: 24rpx;
  color: #999;
}

.backup-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin: 0;
  min-width: 100rpx;
}

.action-btn.restore {
  background-color: #1890ff;
  color: #fff;
}

.action-btn.delete {
  background-color: #ff4d4f;
  color: #fff;
}

.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}