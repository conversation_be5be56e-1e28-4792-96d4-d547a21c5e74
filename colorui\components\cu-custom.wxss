
/* colorui/components/cu-custom.wxss */
.cu-custom {
  display: block;
  position: relative;
}

.cu-custom .cu-bar .content {
  width: calc(100% - 440rpx);
}

.cu-bar {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}

.cu-bar .action {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  max-width: 100%;
}

.cu-bar .action:first-child {
  margin-left: 30rpx;
  font-size: 30rpx;
}

.cu-bar .action text.cuIcon-back {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.cu-bar .content {
  position: absolute;
  text-align: center;
  width: calc(100% - 340rpx);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  cursor: none;
  pointer-events: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.cu-bar.fixed, .nav.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.fixed {
  padding-top: 0px;
}

.cu-bar.bg-white {
  background-color: #ffffff;
  color: #333333;
}

.cu-bar.bg-gradual-blue {
  background-image: linear-gradient(45deg, #0081ff, #1cbbb4);
  color: #ffffff;
}

.cu-bar.none-bg {
  background-color: transparent;
}

.cu-bar.bg-img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.cu-bar.text-white {
  color: #ffffff;
}

.cu-bar .border-custom {
  position: relative;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 1000rpx;
  height: 30px;
}

.cu-bar .border-custom::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 1rpx solid #ffffff;
  opacity: 0.5;
}

.cu-bar .border-custom text {
  display: block;
  flex: 1;
  margin: auto !important;
  text-align: center;
  font-size: 34rpx;
}