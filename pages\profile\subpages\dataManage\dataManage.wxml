
<!--dataManage.wxml-->
<view class="container">
  <view class="header">
    <text class="title">数据备份与恢复</text>
    <button class="backup-btn" bindtap="createBackup">创建备份</button>
  </view>

  <view class="backup-list">
    <block wx:if="{{backupFiles.length > 0}}">
      <view class="backup-item" wx:for="{{backupFiles}}" wx:key="name">
        <view class="backup-info">
          <text class="backup-name">{{item.name}}</text>
          <text class="backup-date">{{item.date}}</text>
        </view>
        <view class="backup-actions">
          <button class="action-btn restore" bindtap="restoreData" 
                  data-path="{{item.path}}" data-name="{{item.name}}">恢复</button>
          <button class="action-btn delete" bindtap="deleteBackup" 
                  data-path="{{item.path}}" data-name="{{item.name}}">删除</button>
        </view>
      </view>
    </block>
    
    <view class="empty-tip" wx:else>
      <text>暂无备份文件</text>
    </view>
  </view>
</view>