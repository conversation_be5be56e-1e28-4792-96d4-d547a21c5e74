{"version": 3, "sources": ["../../src/associations/base.js"], "sourcesContent": ["'use strict';\n\nconst { AssociationError } = require('./../errors');\n\n/**\n * Creating associations in sequelize is done by calling one of the belongsTo / hasOne / hasMany / belongsToMany functions on a model (the source), and providing another model as the first argument to the function (the target).\n *\n * * hasOne - adds a foreign key to the target and singular association mixins to the source.\n * * belongsTo - add a foreign key and singular association mixins to the source.\n * * hasMany - adds a foreign key to target and plural association mixins to the source.\n * * belongsToMany - creates an N:M association with a join table and adds plural association mixins to the source. The junction table is created with sourceId and targetId.\n *\n * Creating an association will add a foreign key constraint to the attributes. All associations use `CASCADE` on update and `SET NULL` on delete, except for n:m, which also uses `CASCADE` on delete.\n *\n * When creating associations, you can provide an alias, via the `as` option. This is useful if the same model is associated twice, or you want your association to be called something other than the name of the target model.\n *\n * As an example, consider the case where users have many pictures, one of which is their profile picture. All pictures have a `userId`, but in addition the user model also has a `profilePictureId`, to be able to easily load the user's profile picture.\n *\n * ```js\n * User.hasMany(Picture)\n * User.belongsTo(Picture, { as: 'ProfilePicture', constraints: false })\n *\n * user.getPictures() // gets you all pictures\n * user.getProfilePicture() // gets you only the profile picture\n *\n * User.findAll({\n *   where: ...,\n *   include: [\n *     { model: Picture }, // load all pictures\n *     { model: Picture, as: 'ProfilePicture' }, // load the profile picture.\n *     // Notice that the spelling must be the exact same as the one in the association\n *   ]\n * })\n * ```\n * To get full control over the foreign key column added by sequelize, you can use the `foreignKey` option. It can either be a string, that specifies the name, or and object type definition,\n * equivalent to those passed to `sequelize.define`.\n *\n * ```js\n * User.hasMany(Picture, { foreignKey: 'uid' })\n * ```\n *\n * The foreign key column in Picture will now be called `uid` instead of the default `userId`.\n *\n * ```js\n * User.hasMany(Picture, {\n *   foreignKey: {\n *     name: 'uid',\n *     allowNull: false\n *   }\n * })\n * ```\n *\n * This specifies that the `uid` column cannot be null. In most cases this will already be covered by the foreign key constraints, which sequelize creates automatically, but can be useful in case where the foreign keys are disabled, e.g. due to circular references (see `constraints: false` below).\n *\n * When fetching associated models, you can limit your query to only load some models. These queries are written in the same way as queries to `find`/`findAll`. To only get pictures in JPG, you can do:\n *\n * ```js\n * user.getPictures({\n *   where: {\n *     format: 'jpg'\n *   }\n * })\n * ```\n *\n * There are several ways to update and add new associations. Continuing with our example of users and pictures:\n * ```js\n * user.addPicture(p) // Add a single picture\n * user.setPictures([p1, p2]) // Associate user with ONLY these two picture, all other associations will be deleted\n * user.addPictures([p1, p2]) // Associate user with these two pictures, but don't touch any current associations\n * ```\n *\n * You don't have to pass in a complete object to the association functions, if your associated model has a single primary key:\n *\n * ```js\n * user.addPicture(req.query.pid) // Here pid is just an integer, representing the primary key of the picture\n * ```\n *\n * In the example above we have specified that a user belongs to his profile picture. Conceptually, this might not make sense, but since we want to add the foreign key to the user model this is the way to do it.\n *\n * Note how we also specified `constraints: false` for profile picture. This is because we add a foreign key from user to picture (profilePictureId), and from picture to user (userId). If we were to add foreign keys to both, it would create a cyclic dependency, and sequelize would not know which table to create first, since user depends on picture, and picture depends on user. These kinds of problems are detected by sequelize before the models are synced to the database, and you will get an error along the lines of `Error: Cyclic dependency found. 'users' is dependent of itself`. If you encounter this, you should either disable some constraints, or rethink your associations completely.\n */\nclass Association {\n  constructor(source, target, options = {}) {\n    /**\n     * @type {Model}\n     */\n    this.source = source;\n\n    /**\n     * @type {Model}\n     */\n    this.target = target;\n\n    this.options = options;\n    this.scope = options.scope;\n    this.isSelfAssociation = this.source === this.target;\n    this.as = options.as;\n\n    /**\n     * The type of the association. One of `HasMany`, `BelongsTo`, `HasOne`, `BelongsToMany`\n     *\n     * @type {string}\n     */\n    this.associationType = '';\n\n    if (source.hasAlias(options.as)) {\n      throw new AssociationError(`You have used the alias ${options.as} in two separate associations. ` +\n      'Aliased associations must have unique aliases.'\n      );\n    }\n  }\n\n  /**\n   * Normalize input\n   *\n   * @param {Array|string} input it may be array or single obj, instance or primary key\n   *\n   * @private\n   * @returns {Array} built objects\n   */\n  toInstanceArray(input) {\n    if (!Array.isArray(input)) {\n      input = [input];\n    }\n\n    return input.map(element => {\n      if (element instanceof this.target) return element;\n\n      const tmpInstance = {};\n      tmpInstance[this.target.primaryKeyAttribute] = element;\n\n      return this.target.build(tmpInstance, { isNewRecord: false });\n    });\n  }\n\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return this.as;\n  }\n}\n\nmodule.exports = Association;\n"], "mappings": ";AAEA,MAAM,EAAE,qBAAqB,QAAQ;AA+ErC,kBAAkB;AAAA,EAChB,YAAY,QAAQ,QAAQ,UAAU,IAAI;AAIxC,SAAK,SAAS;AAKd,SAAK,SAAS;AAEd,SAAK,UAAU;AACf,SAAK,QAAQ,QAAQ;AACrB,SAAK,oBAAoB,KAAK,WAAW,KAAK;AAC9C,SAAK,KAAK,QAAQ;AAOlB,SAAK,kBAAkB;AAEvB,QAAI,OAAO,SAAS,QAAQ,KAAK;AAC/B,YAAM,IAAI,iBAAiB,2BAA2B,QAAQ;AAAA;AAAA;AAAA,EAclE,gBAAgB,OAAO;AACrB,QAAI,CAAC,MAAM,QAAQ,QAAQ;AACzB,cAAQ,CAAC;AAAA;AAGX,WAAO,MAAM,IAAI,aAAW;AAC1B,UAAI,mBAAmB,KAAK;AAAQ,eAAO;AAE3C,YAAM,cAAc;AACpB,kBAAY,KAAK,OAAO,uBAAuB;AAE/C,aAAO,KAAK,OAAO,MAAM,aAAa,EAAE,aAAa;AAAA;AAAA;AAAA,GAIxD,OAAO,IAAI,iCAAiC;AAC3C,WAAO,KAAK;AAAA;AAAA;AAIhB,OAAO,UAAU;", "names": []}