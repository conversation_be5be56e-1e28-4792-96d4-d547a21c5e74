
.container {
  padding: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 30rpx 0;
  color: #333;
}

.approval-list {
  padding: 0 20rpx;
}

.approval-item {
  background: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.approval-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.applicant {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.department {
  font-size: 28rpx;
  color: #666;
}

.approval-content {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.approval-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  min-width: 160rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin: 0;
  padding: 0 30rpx;
}

.approve {
  background-color: #1296db;
  color: #fff;
}

.reject {
  background-color: #f5f5f5;
  color: #666;
}

.approval-status {
  text-align: right;
  padding-top: 10rpx;
}

.status {
  font-size: 28rpx;
}

.status.approved {
  color: #07c160;
}

.status.rejected {
  color: #ff4d4f;
}

.empty-list {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}