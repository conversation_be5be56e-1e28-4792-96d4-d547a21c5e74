
/* carHistory.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 0;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background: #fff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 15rpx 0;
}

.tab-item.active {
  color: #1aad19;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1aad19;
  border-radius: 2rpx;
}

/* 记录列表 */
.record-list {
  padding: 20rpx;
}

.record-item {
  background: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.date {
  font-size: 28rpx;
  color: #333;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status.pending {
  background-color: #ffd591;
  color: #d46b08;
}

.status.completed {
  background-color: #b7eb8f;
  color: #389e0d;
}

.status.rejected {
  background-color: #ffa39e;
  color: #cf1322;
}

.record-body {
  padding-top: 15rpx;
}

.record-info {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.record-info:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  width: 140rpx;
}

.value {
  flex: 1;
  color: #333;
}

/* 空记录提示 */
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}