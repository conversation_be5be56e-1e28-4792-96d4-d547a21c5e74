
// settings.js
const dataManager = require('../../../../utils/dataManager');

Page({
  data: {
    notificationSettings: {
      approvalNotice: true,
      usageReminder: true
    }
  },

  onLoad: function() {
    this.loadSettings();
  },

  // 加载通知设置
  loadSettings: function() {
    const settings = dataManager.getData('notificationSettings');
    if (settings) {
      this.setData({
        notificationSettings: settings
      });
    }
  },

  // 切换通知设置
  toggleSetting: function(e) {
    const setting = e.currentTarget.dataset.setting;
    const newValue = !this.data.notificationSettings[setting];
    
    // 更新本地数据
    const newSettings = {
      ...this.data.notificationSettings,
      [setting]: newValue
    };
    
    dataManager.updateData('notificationSettings', newSettings);
    
    this.setData({
      [`notificationSettings.${setting}`]: newValue
    });
    
    wx.showToast({
      title: newValue ? '已开启' : '已关闭',
      icon: 'success'
    });
  }
})