
Page({
  data: {
    formData: {
      purpose: '',
      startTime: '',
      endTime: '',
      destination: '',
      passengers: 1
    }
  },

  onLoad: function(options) {
    // 页面加载时执行
  },

  // 表单输入处理
  onInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交申请
  submitApplication: function() {
    const { formData } = this.data;
    // TODO: 实现提交逻辑
    wx.showToast({
      title: '申请已提交',
      icon: 'success'
    });
  }
});