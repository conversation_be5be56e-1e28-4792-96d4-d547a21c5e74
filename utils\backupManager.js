
// backupManager.js
const dataManager = require('./dataManager');

// 导出数据
const exportData = () => {
  const data = dataManager.readData();
  return JSON.stringify(data, null, 2);
};

// 导入数据
const importData = (jsonData) => {
  try {
    const data = JSON.parse(jsonData);
    if (data && typeof data === 'object') {
      return dataManager.writeData(data);
    }
    return false;
  } catch (e) {
    console.error('导入数据失败:', e);
    return false;
  }
};

// 备份数据到本地文件
const backupToFile = () => {
  const data = exportData();
  const filePath = `${wx.env.USER_DATA_PATH}/backup_${Date.now()}.json`;
  
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().writeFile({
      filePath: filePath,
      data: data,
      encoding: 'utf8',
      success: () => resolve(filePath),
      fail: (err) => reject(err)
    });
  });
};

// 从文件恢复数据
const restoreFromFile = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath: filePath,
      encoding: 'utf8',
      success: (res) => {
        if (importData(res.data)) {
          resolve(true);
        } else {
          reject(new Error('数据格式不正确'));
        }
      },
      fail: (err) => reject(err)
    });
  });
};

// 获取所有备份文件
const getBackupFiles = () => {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readdir({
      dirPath: wx.env.USER_DATA_PATH,
      success: (res) => {
        const backupFiles = res.files
          .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
          .map(file => {
            const timestamp = file.replace('backup_', '').replace('.json', '');
            const date = new Date(parseInt(timestamp));
            return {
              name: file,
              path: `${wx.env.USER_DATA_PATH}/${file}`,
              date: date.toLocaleString()
            };
          })
          .sort((a, b) => b.name.localeCompare(a.name)); // 按时间倒序排列
        
        resolve(backupFiles);
      },
      fail: (err) => reject(err)
    });
  });
};

// 删除备份文件
const deleteBackupFile = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().unlink({
      filePath: filePath,
      success: () => resolve(true),
      fail: (err) => reject(err)
    });
  });
};

module.exports = {
  exportData,
  importData,
  backupToFile,
  restoreFromFile,
  getBackupFiles,
  deleteBackupFile
};