
// 个人中心页面
const dataManager = require('../../utils/dataManager');
const authManager = require('../../utils/authManager');

Page({
  data: {
    userInfo: null,            // 微信用户信息
    hasUserInfo: false,        // 是否已获取用户信息
    canIUseGetUserProfile: false, // 是否可以使用getUserProfile
    permissions: {
      canManageApprovers: true, // 是否可以管理审批人
      canManageVehicles: true,  // 是否可以管理车辆
      canApprove: true,         // 是否有审批权限
      canViewAllRecords: true,  // 是否可以查看所有记录
      canManageUsers: true      // 是否可以管理用户
    },
    isAdmin: true               // 是否是管理员
  },

  onLoad: function(options) {
    // 检查是否可以使用getUserProfile API
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }
    
    // 页面加载时刷新数据
    this.refreshData();
  },

  onShow: function() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  // 获取微信用户信息
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善用户资料', // 声明获取用户个人信息后的用途
      success: (res) => {
        console.log('获取用户信息成功');
        
        // 只保存必要的用户信息，不包含头像
        const userInfo = {
          nickName: res.userInfo.nickName,
          gender: res.userInfo.gender,
          language: res.userInfo.language,
          role: '管理员' // 设置为管理员角色
        };
        
        // 保存到本地存储
        dataManager.saveData('userInfo', userInfo);
        
        // 更新页面数据
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });
        
        // 刷新权限
        this.refreshPermissions();

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  refreshData: function() {
    // 从本地存储获取用户信息
    const storedUserInfo = dataManager.getData('userInfo');
    
    if (storedUserInfo) {
      // 如果有存储的用户信息，使用它
      this.setData({
        userInfo: storedUserInfo,
        hasUserInfo: true
      });
    }
    
    // 刷新权限设置
    this.refreshPermissions();
  },
  
  // 刷新权限设置
  refreshPermissions: function() {
    // 设置管理员所有权限
    const adminPermissions = {
      canManageApprovers: true, // 允许管理审批人
      canManageVehicles: true,  // 允许管理车辆
      canApprove: true,         // 允许审批
      canViewAllRecords: true,  // 允许查看所有记录
      canManageUsers: true      // 允许管理用户
    };

    // 更新本地存储中的权限信息
    wx.setStorage({
      key: 'userPermissions',
      data: adminPermissions
    });

    // 更新页面数据
    this.setData({
      permissions: adminPermissions, // 设置权限
      isAdmin: true                  // 设置为管理员
    }, () => {
      // 数据设置完成后的回调
      console.log('【权限设置完成】');
      console.log('用户信息:', this.data.userInfo);
      console.log('权限状态:', this.data.permissions);
    });
  },

  // 导航到二级页面
  navigateTo: function(e) {
    const page = e.currentTarget.dataset.page;
    const pages = {
      'userInfo': '../profile/subpages/userInfo/userInfo',
      'carHistory': '../profile/subpages/carHistory/carHistory',
      'settings': '../profile/subpages/settings/settings',
      'approvers': '../profile/subpages/approvers/approvers',
      'vehicles': '../profile/subpages/vehicles/vehicles',
      'approvalCenter': '../profile/subpages/approvalCenter/approvalCenter'
    };
    
    // 权限检查
    const permissionMap = {
      'approvers': 'canManageApprovers',
      'vehicles': 'canManageVehicles',
      'approvalCenter': 'canApprove'
    };

    // 需要权限的页面检查
    if (permissionMap[page] && !this.data.permissions[permissionMap[page]]) {
      wx.showToast({
        title: '无权限访问',
        icon: 'none'
      });
      return;
    }

    // 导航到对应页面
    wx.navigateTo({
      url: pages[page]
    });
  }
});