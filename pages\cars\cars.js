
const dataManager = require('../../utils/dataManager');

Page({
  data: {
    vehicles: [],
    statusFilters: ["全部", "可用", "使用中", "维修中"],
    currentFilter: "全部",
    loading: true,  // 添加加载状态
    error: null     // 添加错误状态
  },

  onLoad: async function(options) {
    // 页面加载时获取车辆数据
    await this.loadVehicles();
  },

  onPullDownRefresh: async function() {
    // 下拉刷新时重新加载数据
    await this.loadVehicles();
    wx.stopPullDownRefresh();
  },

  // 加载车辆数据
  loadVehicles: async function() {
    this.setData({ loading: true, error: null });
    
    try {
      // 检查网络状态
      const isOnline = await dataManager.checkNetworkStatus();
      if (!isOnline) {
        throw new Error('网络连接不可用');
      }

      // 获取车辆数据
      const vehicles = await dataManager.getData('vehicles');
      
      // 为每个车辆添加状态样式类
      const processedVehicles = vehicles.map(vehicle => ({
        ...vehicle,
        statusClass: this.getStatusClass(vehicle.status)
      }));

      this.setData({
        vehicles: processedVehicles,
        loading: false
      });
    } catch (error) {
      console.error('加载车辆数据失败:', error);
      this.setData({
        error: error.message || '加载失败，请稍后重试',
        loading: false
      });
      
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none'
      });
    }
  },

  // 获取状态对应的样式类
  getStatusClass: function(status) {
    const statusMap = {
      '可用': 'available',
      '使用中': 'inuse',
      '维修中': 'maintenance'
    };
    return statusMap[status] || 'unknown';
  },

  // 切换状态筛选
  changeFilter: function(e) {
    const { filter } = e.currentTarget.dataset;
    this.setData({
      currentFilter: filter
    });
  },

  // 查看车辆详情
  viewVehicleDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    // TODO: 跳转到车辆详情页
    wx.showToast({
      title: '查看车辆详情: ' + id,
      icon: 'none'
    });
  }
});