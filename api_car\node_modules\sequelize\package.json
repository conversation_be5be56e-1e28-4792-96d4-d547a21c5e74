{"name": "sequelize", "description": "Sequelize is a promise-based Node.js ORM tool for Postgres, MySQL, MariaDB, SQLite, Microsoft SQL Server, Amazon Redshift and Snowflake’s Data Cloud. It features solid transaction support, relations, eager and lazy loading, read replication and more.", "version": "6.37.7", "funding": [{"type": "opencollective", "url": "https://opencollective.com/sequelize"}], "repository": {"type": "git", "url": "https://github.com/sequelize/sequelize.git"}, "bugs": {"url": "https://github.com/sequelize/sequelize/issues"}, "homepage": "https://sequelize.org/", "main": "./lib/index.js", "types": "./types/index.d.ts", "type": "commonjs", "exports": {".": {"types": "./types/index.d.ts", "import": "./lib/index.mjs", "require": "./lib/index.js"}, "./lib/*": {"types": "./types/*.d.ts", "default": "./lib/*.js"}, "./lib/errors": {"types": "./types/errors/index.d.ts", "default": "./lib/errors/index.js"}, "./package.json": "./package.json", "./types/*": {"types": "./types/*.d.ts"}}, "engines": {"node": ">=10.0.0"}, "files": ["lib", "types", "index.js"], "license": "MIT", "dependencies": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.6", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.1", "retry-as-promised": "^7.0.4", "semver": "^7.5.4", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}, "devDependencies": {"@commitlint/cli": "^15.0.0", "@commitlint/config-angular": "^15.0.0", "@octokit/rest": "^18.12.0", "@octokit/types": "^6.34.0", "@types/chai": "^4.3.0", "@types/lodash": "4.14.197", "@types/mocha": "^9.0.0", "@types/node": "^16.11.17", "@types/sinon": "^10.0.6", "@typescript-eslint/eslint-plugin": "^5.8.1", "@typescript-eslint/parser": "^5.8.1", "acorn": "^8.7.0", "chai": "^4.3.7", "chai-as-promised": "^7.1.1", "chai-datetime": "^1.8.0", "cheerio": "^1.0.0-rc.10", "cls-hooked": "^4.2.2", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "delay": "^5.0.0", "esbuild": "0.14.3", "esdoc": "^1.1.0", "esdoc-ecmascript-proposal-plugin": "^1.0.0", "esdoc-inject-style-plugin": "^1.0.0", "esdoc-standard-plugin": "^1.0.0", "eslint": "^8.5.0", "eslint-plugin-jsdoc": "^37.4.0", "eslint-plugin-mocha": "^9.0.0", "expect-type": "^0.12.0", "fast-glob": "^3.2.7", "fs-jetpack": "^4.3.0", "husky": "^7.0.4", "ibm_db": "^2.8.1", "js-combinatorics": "^0.6.1", "lcov-result-merger": "^3.1.0", "lint-staged": "^12.1.4", "mariadb": "^2.5.5", "markdownlint-cli": "^0.30.0", "mocha": "^7.2.0", "module-alias": "^2.2.2", "mysql2": "^2.3.3", "node-hook": "^1.0.0", "nyc": "^15.1.0", "oracledb": "^5.5.0", "p-map": "^4.0.0", "p-props": "^4.0.0", "p-settle": "^4.1.1", "p-timeout": "^4.0.0", "pg": "^8.7.1", "pg-hstore": "^2.3.4", "rimraf": "^3.0.2", "semantic-release": "^18.0.1", "semantic-release-fail-on-major-bump": "^1.0.0", "sinon": "^12.0.1", "sinon-chai": "^3.7.0", "snowflake-sdk": "^1.6.6", "source-map-support": "^0.5.21", "sqlite3": "^5.1.6", "tedious": "8.3.0", "typescript": "^4.5.4"}, "peerDependenciesMeta": {"pg": {"optional": true}, "pg-hstore": {"optional": true}, "mysql2": {"optional": true}, "ibm_db": {"optional": true}, "snowflake-sdk": {"optional": true}, "mariadb": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}, "oracledb": {"optional": true}}, "keywords": ["mysql", "ma<PERSON>b", "sqlite", "postgresql", "postgres", "pg", "mssql", "db2", "ibm_db", "sql", "oracledb", "sqlserver", "snowflake", "orm", "nodejs", "object relational mapper", "database", "db"], "commitlint": {"extends": ["@commitlint/config-angular"], "rules": {"type-enum": [2, "always", ["build", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test", "meta"]]}}, "lint-staged": {"*!(d).[tj]s": "eslint"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "semantic-release-fail-on-major-bump", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github"], "branches": ["v6", {"name": "v6-beta", "prerelease": "beta"}]}, "publishConfig": {"tag": "latest"}, "scripts": {"----------------------------------------- static analysis -----------------------------------------": "", "lint": "eslint src test --quiet --fix", "lint-docs": "markdownlint docs", "test-typings": "tsc --noEmit --emitDeclarationOnly false && tsc -b test/tsconfig.json", "----------------------------------------- documentation -------------------------------------------": "", "docs": "sh docs.sh", "----------------------------------------- tests ---------------------------------------------------": "", "mocha": "mocha -r ./test/registerEsbuild", "test-unit": "yarn mocha \"test/unit/**/*.test.[tj]s\"", "test-integration": "yarn mocha \"test/integration/**/*.test.[tj]s\"", "teaser": "node test/teaser.js", "test": "npm run prepare && npm run test-typings && npm run teaser && npm run test-unit && npm run test-integration", "----------------------------------------- coverage ------------------------------------------------": "", "cover": "rimraf coverage && npm run teaser && npm run cover-integration && npm run cover-unit && npm run merge-coverage", "cover-integration": "cross-env COVERAGE=true nyc --reporter=lcovonly yarn mocha \"test/integration/**/*.test.[tj]s\" && node -e \"require('fs').renameSync('coverage/lcov.info', 'coverage/integration.info')\"", "cover-unit": "cross-env COVERAGE=true nyc --reporter=lcovonly yarn mocha \"test/unit/**/*.test.[tj]s\" && node -e \"require('fs').renameSync('coverage/lcov.info', 'coverage/unit.info')\"", "merge-coverage": "lcov-result-merger \"coverage/*.info\" \"coverage/lcov.info\"", "----------------------------------------- local test dbs ------------------------------------------": "", "start-mariadb": "bash dev/mariadb/10.3/start.sh", "start-mysql": "bash dev/mysql/5.7/start.sh", "start-mysql-8": "bash dev/mysql/8.0/start.sh", "start-postgres": "bash dev/postgres/10/start.sh", "start-mssql": "bash dev/mssql/2019/start.sh", "start-db2": "bash dev/db2/11.5/start.sh", "start-oracle-oldest": "bash dev/oracle/18-slim/start.sh", "start-oracle-latest": "bash dev/oracle/23-slim/start.sh", "stop-mariadb": "bash dev/mariadb/10.3/stop.sh", "stop-mysql": "bash dev/mysql/5.7/stop.sh", "stop-mysql-8": "bash dev/mysql/8.0/stop.sh", "stop-postgres": "bash dev/postgres/10/stop.sh", "stop-mssql": "bash dev/mssql/2019/stop.sh", "stop-db2": "bash dev/db2/11.5/stop.sh", "stop-oracle-oldest": "bash dev/oracle/18-slim/stop.sh", "stop-oracle-latest": "bash dev/oracle/23-slim/stop.sh", "restart-mariadb": "npm run start-ma<PERSON><PERSON>", "restart-mysql": "npm run start-mysql", "restart-postgres": "npm run start-postgres", "restart-mssql": "npm run start-mssql", "restart-db2": "npm run start-db2", "restart-oracle-oldest": "npm run start-oracle-oldest", "restart-oracle-latest": "npm run start-oracle-latest", "----------------------------------------- local tests ---------------------------------------------": "", "test-unit-mariadb": "cross-env DIALECT=mariadb npm run test-unit", "test-unit-mysql": "cross-env DIALECT=mysql npm run test-unit", "test-unit-postgres": "cross-env DIALECT=postgres npm run test-unit", "test-unit-postgres-native": "cross-env DIALECT=postgres-native npm run test-unit", "test-unit-sqlite": "cross-env DIALECT=sqlite npm run test-unit", "test-unit-mssql": "cross-env DIALECT=mssql npm run test-unit", "test-unit-db2": "cross-env DIALECT=db2 npm run test-unit", "test-unit-snowflake": "cross-env DIALECT=snowflake npm run test-unit", "test-unit-oracle": "cross-env DIALECT=oracle npm run test-unit", "test-unit-all": "npm run test-unit-mariadb && npm run test-unit-mysql && npm run test-unit-postgres && npm run test-unit-postgres-native && npm run test-unit-mssql && npm run test-unit-sqlite && npm run test-unit-snowflake && npm run test-unit-db2 && npm run test-unit-oracle", "test-integration-mariadb": "cross-env DIALECT=mariadb npm run test-integration", "test-integration-mysql": "cross-env DIALECT=mysql npm run test-integration", "test-integration-postgres": "cross-env DIALECT=postgres npm run test-integration", "test-integration-postgres-native": "cross-env DIALECT=postgres-native npm run test-integration", "test-integration-sqlite": "cross-env DIALECT=sqlite npm run test-integration", "test-integration-mssql": "cross-env DIALECT=mssql npm run test-integration", "test-integration-db2": "cross-env DIALECT=db2 npm run test-integration", "test-integration-snowflake": "cross-env DIALECT=snowflake npm run test-integration", "test-integration-oracle": "cross-env LD_LIBRARY_PATH=\"$PWD/.oracle/instantclient/\" DIALECT=oracle UV_THREADPOOL_SIZE=128 npm run test-integration", "test-mariadb": "cross-env DIALECT=mariadb npm test", "test-mysql": "cross-env DIALECT=mysql npm test", "test-sqlite": "cross-env DIALECT=sqlite npm test", "test-postgres": "cross-env DIALECT=postgres npm test", "test-postgres-native": "cross-env DIALECT=postgres-native npm test", "test-mssql": "cross-env DIALECT=mssql npm test", "test-db2": "cross-env DIALECT=db2 npm test", "test-oracle": "cross-env LD_LIBRARY_PATH=\"$PWD/.oracle/instantclient/\" DIALECT=oracle UV_THREADPOOL_SIZE=128 npm test", "----------------------------------------- development ---------------------------------------------": "", "sscce": "node sscce.js", "sscce-mariadb": "cross-env DIALECT=mariadb node sscce.js", "sscce-mysql": "cross-env DIALECT=mysql node sscce.js", "sscce-postgres": "cross-env DIALECT=postgres node sscce.js", "sscce-postgres-native": "cross-env DIALECT=postgres-native node sscce.js", "sscce-sqlite": "cross-env DIALECT=sqlite node sscce.js", "sscce-mssql": "cross-env DIALECT=mssql node sscce.js", "sscce-db2": "cross-env DIALECT=db2 node sscce.js", "sscce-oracle": "cross-env LD_LIBRARY_PATH=\"$PWD/.oracle/instantclient/\" DIALECT=oracle UV_THREADPOOL_SIZE=128 node sscce.js", "prepare": "npm run build && husky install", "build": "node ./build.js", "---------------------------------------------------------------------------------------------------": ""}, "support": true}