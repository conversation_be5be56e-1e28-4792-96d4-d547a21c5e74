const { errorResponse } = require('../utils/response');

const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Sequelize 验证错误
  if (err.name === 'SequelizeValidationError') {
    const errors = err.errors.map(error => ({
      field: error.path,
      message: error.message
    }));
    return res.status(400).json({
      code: 400,
      message: '数据验证失败',
      errors
    });
  }

  // Sequelize 唯一约束错误
  if (err.name === 'SequelizeUniqueConstraintError') {
    const field = err.errors[0].path;
    return res.status(400).json(errorResponse(400, `${field} 已存在`));
  }

  // Sequelize 外键约束错误
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    return res.status(400).json(errorResponse(400, '关联数据不存在'));
  }

  // JSON 解析错误
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json(errorResponse(400, '请求数据格式错误'));
  }

  // 默认服务器错误
  res.status(500).json(errorResponse(500, '服务器内部错误'));
};

module.exports = errorHandler;
