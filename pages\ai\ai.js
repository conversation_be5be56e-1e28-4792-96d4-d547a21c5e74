// ai.js
const dataManager = require('../../utils/dataManager');

Page({
  data: {
    questions: [],
    currentQuestionIndex: 0
  },

  onLoad: function() {
    // 从本地数据获取问题
    const questions = dataManager.getData('questions');
    if (questions && questions.length > 0) {
      this.setData({
        questions: questions
      });
    } else {
      // 如果没有数据，使用默认问题
      const defaultQuestions = [
        {
          id: 1,
          question: "如何提交用车申请？",
          answer: "点击首页的'用车申请'功能，填写相关信息并提交即可。"
        },
        {
          id: 2,
          question: "用车审批流程是怎样的？",
          answer: "根据您的权限等级，用车申请可能需要经过不同级别的审批。"
        },
        {
          id: 3,
          question: "如何查看可用车辆？",
          answer: "在首页点击'车辆信息查看'功能，您可以看到所有可用车辆的信息。"
        }
      ];
      
      // 保存默认问题到本地数据
      dataManager.updateData('questions', defaultQuestions);
      
      this.setData({
        questions: defaultQuestions
      });
    }
  },

  switchQuestion: function(e) {
    this.setData({
      currentQuestionIndex: e.currentTarget.dataset.index
    });
  },

  // 添加新问题
  addQuestion: function(question) {
    dataManager.addRecord('questions', question);
    // 重新加载数据
    this.setData({
      questions: dataManager.getData('questions')
    });
  },

  // 更新问题
  updateQuestion: function(id, newData) {
    dataManager.updateRecord('questions', id, newData);
    // 重新加载数据
    this.setData({
      questions: dataManager.getData('questions')
    });
  },

  // 删除问题
  deleteQuestion: function(id) {
    dataManager.deleteRecord('questions', id);
    // 重新加载数据
    this.setData({
      questions: dataManager.getData('questions')
    });
  }
})