
/* ColorUI主样式文件 */
/* 基础样式 */
.text-xs { font-size: 20rpx; }
.text-sm { font-size: 24rpx; }
.text-df { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-xxl { font-size: 44rpx; }
.text-sl { font-size: 80rpx; }
.text-xsl { font-size: 120rpx; }

/* 文字颜色 */
.text-white { color: #ffffff; }
.text-black { color: #333333; }
.text-gray { color: #aaaaaa; }
.text-red { color: #e54d42; }
.text-orange { color: #f37b1d; }
.text-yellow { color: #fbbd08; }
.text-olive { color: #8dc63f; }
.text-green { color: #39b54a; }
.text-cyan { color: #1cbbb4; }
.text-blue { color: #0081ff; }
.text-purple { color: #6739b6; }
.text-mauve { color: #9c26b0; }
.text-pink { color: #e03997; }
.text-brown { color: #a5673f; }
.text-grey { color: #8799a3; }

/* 背景颜色 */
.bg-white { background-color: #ffffff; }
.bg-black { background-color: #333333; }
.bg-gray { background-color: #f0f0f0; }
.bg-red { background-color: #e54d42; }
.bg-orange { background-color: #f37b1d; }
.bg-yellow { background-color: #fbbd08; }
.bg-olive { background-color: #8dc63f; }
.bg-green { background-color: #39b54a; }
.bg-cyan { background-color: #1cbbb4; }
.bg-blue { background-color: #0081ff; }
.bg-purple { background-color: #6739b6; }
.bg-mauve { background-color: #9c26b0; }
.bg-pink { background-color: #e03997; }
.bg-brown { background-color: #a5673f; }
.bg-grey { background-color: #8799a3; }

/* 渐变背景 */
.bg-gradual-red { background-image: linear-gradient(45deg, #f43f3b, #ec008c); color: #ffffff; }
.bg-gradual-orange { background-image: linear-gradient(45deg, #ff9700, #ed1c24); color: #ffffff; }
.bg-gradual-green { background-image: linear-gradient(45deg, #39b54a, #8dc63f); color: #ffffff; }
.bg-gradual-blue { background-image: linear-gradient(45deg, #0081ff, #1cbbb4); color: #ffffff; }
.bg-gradual-purple { background-image: linear-gradient(45deg, #9000ff, #5e00ff); color: #ffffff; }
.bg-gradual-pink { background-image: linear-gradient(45deg, #ec008c, #6739b6); color: #ffffff; }

/* 阴影 */
.shadow { box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0rpx 40rpx 100rpx 0rpx rgba(0, 0, 0, 0.07); }
.shadow-warp { position: relative; box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1); }

/* 边距 */
.margin-xs { margin: 10rpx; }
.margin-sm { margin: 20rpx; }
.margin { margin: 30rpx; }
.margin-lg { margin: 40rpx; }
.margin-xl { margin: 50rpx; }

.margin-top-xs { margin-top: 10rpx; }
.margin-top-sm { margin-top: 20rpx; }
.margin-top { margin-top: 30rpx; }
.margin-top-lg { margin-top: 40rpx; }
.margin-top-xl { margin-top: 50rpx; }

.margin-right-xs { margin-right: 10rpx; }
.margin-right-sm { margin-right: 20rpx; }
.margin-right { margin-right: 30rpx; }
.margin-right-lg { margin-right: 40rpx; }
.margin-right-xl { margin-right: 50rpx; }

.margin-bottom-xs { margin-bottom: 10rpx; }
.margin-bottom-sm { margin-bottom: 20rpx; }
.margin-bottom { margin-bottom: 30rpx; }
.margin-bottom-lg { margin-bottom: 40rpx; }
.margin-bottom-xl { margin-bottom: 50rpx; }

.margin-left-xs { margin-left: 10rpx; }
.margin-left-sm { margin-left: 20rpx; }
.margin-left { margin-left: 30rpx; }
.margin-left-lg { margin-left: 40rpx; }
.margin-left-xl { margin-left: 50rpx; }

/* 内边距 */
.padding-xs { padding: 10rpx; }
.padding-sm { padding: 20rpx; }
.padding { padding: 30rpx; }
.padding-lg { padding: 40rpx; }
.padding-xl { padding: 50rpx; }

.padding-top-xs { padding-top: 10rpx; }
.padding-top-sm { padding-top: 20rpx; }
.padding-top { padding-top: 30rpx; }
.padding-top-lg { padding-top: 40rpx; }
.padding-top-xl { padding-top: 50rpx; }

.padding-right-xs { padding-right: 10rpx; }
.padding-right-sm { padding-right: 20rpx; }
.padding-right { padding-right: 30rpx; }
.padding-right-lg { padding-right: 40rpx; }
.padding-right-xl { padding-right: 50rpx; }

.padding-bottom-xs { padding-bottom: 10rpx; }
.padding-bottom-sm { padding-bottom: 20rpx; }
.padding-bottom { padding-bottom: 30rpx; }
.padding-bottom-lg { padding-bottom: 40rpx; }
.padding-bottom-xl { padding-bottom: 50rpx; }

.padding-left-xs { padding-left: 10rpx; }
.padding-left-sm { padding-left: 20rpx; }
.padding-left { padding-left: 30rpx; }
.padding-left-lg { padding-left: 40rpx; }
.padding-left-xl { padding-left: 50rpx; }

/* 布局 */
.flex { display: flex; }
.flex-sub { flex: 1; }
.flex-twice { flex: 2; }
.flex-treble { flex: 3; }
.flex-direction { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }
.align-center { align-items: center; }
.align-stretch { align-items: stretch; }
.self-start { align-self: flex-start; }
.self-center { align-self: center; }
.self-end { align-self: flex-end; }
.self-stretch { align-self: stretch; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* 卡片 */
.cu-card {
  display: block;
  overflow: hidden;
}

.cu-card>.cu-item {
  display: block;
  background-color: #ffffff;
  overflow: hidden;
  border-radius: 10rpx;
  margin: 30rpx;
}

.cu-card.case>.cu-item {
  margin: 0 30rpx 30rpx;
}

/* 列表 */
.cu-list.menu {
  display: block;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 10rpx;
}

.cu-list.menu>.cu-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
  padding: 0 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.cu-list.menu>.cu-item:last-child {
  border-bottom: none;
}

.cu-list.menu>.cu-item .content {
  display: flex;
  align-items: center;
  flex: 1;
  color: #555;
  font-size: 28rpx;
  line-height: 1.6em;
}

.cu-list.menu-avatar>.cu-item {
  padding-left: 140rpx;
}

.cu-list.menu-avatar>.cu-item .cu-avatar {
  position: absolute;
  left: 30rpx;
}

.cu-list.grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10rpx;
  background-color: #ffffff;
}

.cu-list.grid>.cu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  box-sizing: border-box;
  width: 33.33%;
  text-align: center;
}

.cu-list.grid>.cu-item [class*="cuIcon-"] {
  font-size: 48rpx;
  position: relative;
  margin-bottom: 10rpx;
}

/* 头像 */
.cu-avatar {
  font-variant: small-caps;
  margin: 0;
  padding: 0;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background-color: #ccc;
  color: #ffffff;
  white-space: nowrap;
  position: relative;
  width: 64rpx;
  height: 64rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 1.5em;
}

.cu-avatar.sm {
  width: 48rpx;
  height: 48rpx;
  font-size: 1em;
}

.cu-avatar.lg {
  width: 96rpx;
  height: 96rpx;
  font-size: 2em;
}

.cu-avatar.xl {
  width: 128rpx;
  height: 128rpx;
  font-size: 2.5em;
}

.cu-avatar.round {
  border-radius: 5000rpx;
}

/* 按钮 */
.cu-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
  background-color: #f0f0f0;
  color: #555;
}

.cu-btn::after {
  display: none;
}

.cu-btn:not([class*="bg-"]) {
  background-color: #f0f0f0;
}

.cu-btn[class*="bg-"] {
  color: #ffffff;
}

.cu-btn.sm {
  padding: 0 20rpx;
  font-size: 20rpx;
  height: 48rpx;
}

.cu-btn.lg {
  padding: 0 40rpx;
  font-size: 32rpx;
  height: 80rpx;
}

.cu-btn.block {
  display: flex;
  width: 100%;
}

.cu-btn.round {
  border-radius: 5000rpx;
}

/* 导航栏 */
.cu-bar {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}

.cu-bar .action {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  max-width: 100%;
}

.cu-bar .action:first-child {
  margin-left: 30rpx;
  font-size: 30rpx;
}

.cu-bar .action:last-child {
  margin-right: 30rpx;
}

.cu-bar .content {
  position: absolute;
  text-align: center;
  width: calc(100% - 340rpx);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  cursor: none;
  pointer-events: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.cu-bar.solid-bottom::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

/* 其他通用样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-bold { font-weight: bold; }
.text-cut { text-overflow: ellipsis; white-space: nowrap; overflow: hidden; }
.round { border-radius: 5000rpx; }
.radius { border-radius: 6rpx; }