
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info">
    <button wx:if="{{!hasUserInfo && canIUseGetUserProfile}}" class="login-btn" bindtap="getUserProfile">
      点击登录
    </button>
    <view wx:else class="user-detail">
      <text class="name">{{userInfo.nickName || '未登录'}}</text>
      <text class="role">{{userInfo.role || '普通用户'}}</text>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="menu-list">
    <!-- 基础功能组 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="navigateTo" data-page="userInfo">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/user.png"></image>
          <text class="menu-text">个人信息</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-page="carHistory">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/history.png"></image>
          <text class="menu-text">用车记录</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-page="settings">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/notification.png"></image>
          <text class="menu-text">通知设置</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>
    </view>

    <!-- 管理功能组 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="navigateTo" data-page="approvers" wx:if="{{permissions.canManageApprovers}}">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/approver.png" mode="aspectFit"></image>
          <text class="menu-text">审批人管理</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-page="vehicles" wx:if="{{permissions.canManageVehicles}}">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/car.png" mode="aspectFit"></image>
          <text class="menu-text">车辆管理</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>

      <view class="menu-item" bindtap="navigateTo" data-page="approvalCenter" wx:if="{{permissions.canApprove}}">
        <view class="menu-item-left">
          <image class="menu-icon" src="/images/approval.png"></image>
          <text class="menu-text">审批中心</text>
        </view>
        <image class="menu-arrow" src="/images/arrow.png"></image>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>版本 1.0.0</text>
  </view>
</view>