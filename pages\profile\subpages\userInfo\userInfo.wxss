
/* userInfo.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 非编辑模式 */
.info-display {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 180rpx;
  height: 180rpx;
  border-radius: 90rpx;
}

.info-list {
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  width: 150rpx;
  color: #666;
  font-size: 28rpx;
}

.value {
  flex: 1;
  font-size: 28rpx;
}

.edit-btn {
  width: 100%;
  background-color: #1aad19;
  color: #fff;
  font-size: 30rpx;
  margin-top: 30rpx;
  border-radius: 40rpx;
}

/* 编辑模式 */
.edit-form {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  padding: 0 20rpx;
  margin-top: 10rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-actions {
  display: flex;
  justify-content: space-between;
}

.cancel-btn, .save-btn {
  width: 45%;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.save-btn {
  background-color: #1aad19;
  color: #fff;
}