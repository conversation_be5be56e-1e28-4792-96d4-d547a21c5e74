
// 权限管理模块 - 负责用户权限的检查和管理
const dataManager = require('./dataManager');  // 导入数据管理模块

// 检查用户是否是管理员
const isAdmin = () => {
  const userInfo = dataManager.getData('userInfo');  // 获取用户信息
  return userInfo && userInfo.role === '管理员';  // 检查角色是否为管理员
};

// 获取用户权限配置
const getUserPermissions = () => {
  const userInfo = dataManager.getData('userInfo');  // 获取用户信息
  
  // 默认权限（所有权限默认为true，用于测试）
  const permissions = {
    canManageApprovers: true,  // 是否可以管理审批人
    canManageVehicles: true,   // 是否可以管理车辆
    canApprove: true,          // 是否可以审批
    canViewAllRecords: true,   // 是否可以查看所有记录
    canManageUsers: true       // 是否可以管理用户
  };
  
  if (!userInfo) return permissions;  // 如果没有用户信息，返回默认权限
  
  // 根据用户角色分配权限（当前全部设置为true，用于测试）
  switch (userInfo.role) {
    case '管理员':
      // 管理员拥有所有权限
      permissions.canManageApprovers = true;  // 允许管理审批人
      permissions.canManageVehicles = true;   // 允许管理车辆
      permissions.canApprove = true;          // 允许审批
      permissions.canViewAllRecords = true;   // 允许查看所有记录
      permissions.canManageUsers = true;      // 允许管理用户
      break;
      
    case '部门主管':
      // 部门主管拥有审批权限和查看记录权限
      permissions.canApprove = true;          // 允许审批
      permissions.canViewAllRecords = true;   // 允许查看所有记录
      break;
      
    case '普通员工':
      // 普通员工也暂时设置所有权限为true（用于测试）
      permissions.canManageApprovers = true;  // 允许管理审批人
      permissions.canManageVehicles = true;   // 允许管理车辆
      permissions.canApprove = true;          // 允许审批
      permissions.canViewAllRecords = true;   // 允许查看所有记录
      permissions.canManageUsers = true;      // 允许管理用户
      break;
  }
  
  return permissions;  // 返回权限配置
};

// 检查特定权限
const checkPermission = (permissionKey) => {
  const permissions = getUserPermissions();  // 获取用户权限
  return permissions[permissionKey] === true;  // 检查指定权限是否为true
};

// 导出模块接口
module.exports = {
  isAdmin,            // 检查是否是管理员
  getUserPermissions, // 获取用户权限
  checkPermission     // 检查特定权限
};