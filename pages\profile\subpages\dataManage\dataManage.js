
// dataManage.js
const backupManager = require('../../../../utils/backupManager');
const dataManager = require('../../../../utils/dataManager');

Page({
  data: {
    backupFiles: [],
    isLoading: false,
    isRestoring: false
  },

  onLoad: function() {
    this.loadBackupFiles();
  },

  // 加载备份文件列表
  loadBackupFiles: function() {
    this.setData({ isLoading: true });
    
    backupManager.getBackupFiles()
      .then(files => {
        this.setData({
          backupFiles: files,
          isLoading: false
        });
      })
      .catch(err => {
        console.error('获取备份文件失败:', err);
        wx.showToast({
          title: '获取备份文件失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      });
  },

  // 创建新备份
  createBackup: function() {
    wx.showLoading({
      title: '备份中...',
    });
    
    backupManager.backupToFile()
      .then(filePath => {
        wx.hideLoading();
        wx.showToast({
          title: '备份成功',
          icon: 'success'
        });
        this.loadBackupFiles();
      })
      .catch(err => {
        wx.hideLoading();
        console.error('备份失败:', err);
        wx.showToast({
          title: '备份失败',
          icon: 'none'
        });
      });
  },

  // 恢复数据
  restoreData: function(e) {
    const filePath = e.currentTarget.dataset.path;
    const fileName = e.currentTarget.dataset.name;
    
    wx.showModal({
      title: '确认恢复',
      content: `确定要从备份 ${fileName} 恢复数据吗？当前数据将被覆盖。`,
      success: (res) => {
        if (res.confirm) {
          this.setData({ isRestoring: true });
          wx.showLoading({
            title: '恢复中...',
          });
          
          backupManager.restoreFromFile(filePath)
            .then(() => {
              wx.hideLoading();
              this.setData({ isRestoring: false });
              wx.showToast({
                title: '恢复成功',
                icon: 'success'
              });
              
              // 通知其他页面数据已更新
              const pages = getCurrentPages();
              pages.forEach(page => {
                if (page.refreshData) {
                  page.refreshData();
                }
              });
            })
            .catch(err => {
              wx.hideLoading();
              this.setData({ isRestoring: false });
              console.error('恢复失败:', err);
              wx.showToast({
                title: '恢复失败: ' + err.message,
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 删除备份
  deleteBackup: function(e) {
    const filePath = e.currentTarget.dataset.path;
    const fileName = e.currentTarget.dataset.name;
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除备份 ${fileName} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
          });
          
          backupManager.deleteBackupFile(filePath)
            .then(() => {
              wx.hideLoading();
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.loadBackupFiles();
            })
            .catch(err => {
              wx.hideLoading();
              console.error('删除失败:', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadBackupFiles();
    wx.stopPullDownRefresh();
  }
})