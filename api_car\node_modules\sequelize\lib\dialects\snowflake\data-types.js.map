{"version": 3, "sources": ["../../../src/dialects/snowflake/data-types.js"], "sourcesContent": ["'use strict';\n\nconst momentTz = require('moment-timezone');\nconst moment = require('moment');\n\nmodule.exports = BaseTypes => {\n  BaseTypes.ABSTRACT.prototype.dialectTypes = 'https://dev.snowflake.com/doc/refman/5.7/en/data-types.html';\n\n  /**\n   * types: [buffer_type, ...]\n   *\n   * @see buffer_type here https://dev.snowflake.com/doc/refman/5.7/en/c-api-prepared-statement-type-codes.html\n   * @see hex here https://github.com/sidorares/node-mysql2/blob/master/lib/constants/types.js\n   */\n\n  BaseTypes.DATE.types.snowflake = ['DATETIME'];\n  BaseTypes.STRING.types.snowflake = ['VAR_STRING'];\n  BaseTypes.CHAR.types.snowflake = ['STRING'];\n  BaseTypes.TEXT.types.snowflake = ['BLOB'];\n  BaseTypes.TINYINT.types.snowflake = ['TINY'];\n  BaseTypes.SMALLINT.types.snowflake = ['SHORT'];\n  BaseTypes.MEDIUMINT.types.snowflake = ['INT24'];\n  BaseTypes.INTEGER.types.snowflake = ['LONG'];\n  BaseTypes.BIGINT.types.snowflake = ['LONGLONG'];\n  BaseTypes.FLOAT.types.snowflake = ['FLOAT'];\n  BaseTypes.TIME.types.snowflake = ['TIME'];\n  BaseTypes.DATEONLY.types.snowflake = ['DATE'];\n  BaseTypes.BOOLEAN.types.snowflake = ['TINY'];\n  BaseTypes.BLOB.types.snowflake = ['TINYBLOB', 'BLOB', 'LONGBLOB'];\n  BaseTypes.DECIMAL.types.snowflake = ['NEWDECIMAL'];\n  BaseTypes.UUID.types.snowflake = false;\n  // Enum is not supported\n  // https://docs.snowflake.com/en/sql-reference/data-types-unsupported.html\n  BaseTypes.ENUM.types.snowflake = false;\n  BaseTypes.REAL.types.snowflake = ['DOUBLE'];\n  BaseTypes.DOUBLE.types.snowflake = ['DOUBLE'];\n  BaseTypes.GEOMETRY.types.snowflake = ['GEOMETRY'];\n  BaseTypes.JSON.types.snowflake = ['JSON'];\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return 'TIMESTAMP';\n    }\n    _stringify(date, options) {\n      if (!moment.isMoment(date)) {\n        date = this._applyTimezone(date, options);\n      }\n      if (this._length) {\n        return date.format('YYYY-MM-DD HH:mm:ss.SSS');\n      }\n      return date.format('YYYY-MM-DD HH:mm:ss');\n    }\n    static parse(value, options) {\n      value = value.string();\n      if (value === null) {\n        return value;\n      }\n      if (momentTz.tz.zone(options.timezone)) {\n        value = momentTz.tz(value, options.timezone).toDate();\n      }\n      else {\n        value = new Date(`${value} ${options.timezone}`);\n      }\n      return value;\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(value) {\n      return value.string();\n    }\n  }\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      // https://community.snowflake.com/s/question/0D50Z00009LH2fl/what-is-the-best-way-to-store-uuids\n      return 'VARCHAR(36)';\n    }\n  }\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      return 'TEXT';\n    }\n  }\n\n  class BOOLEAN extends BaseTypes.BOOLEAN {\n    toSql() {\n      return 'BOOLEAN';\n    }\n  }\n\n  class JSONTYPE extends BaseTypes.JSON {\n    _stringify(value, options) {\n      return options.operation === 'where' && typeof value === 'string' ? value : JSON.stringify(value);\n    }\n  }\n\n  return {\n    TEXT,\n    DATE,\n    BOOLEAN,\n    DATEONLY,\n    UUID,\n    JSON: JSONTYPE\n  };\n};\n"], "mappings": ";AAEA,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AAEvB,OAAO,UAAU,eAAa;AAC5B,YAAU,SAAS,UAAU,eAAe;AAS5C,YAAU,KAAK,MAAM,YAAY,CAAC;AAClC,YAAU,OAAO,MAAM,YAAY,CAAC;AACpC,YAAU,KAAK,MAAM,YAAY,CAAC;AAClC,YAAU,KAAK,MAAM,YAAY,CAAC;AAClC,YAAU,QAAQ,MAAM,YAAY,CAAC;AACrC,YAAU,SAAS,MAAM,YAAY,CAAC;AACtC,YAAU,UAAU,MAAM,YAAY,CAAC;AACvC,YAAU,QAAQ,MAAM,YAAY,CAAC;AACrC,YAAU,OAAO,MAAM,YAAY,CAAC;AACpC,YAAU,MAAM,MAAM,YAAY,CAAC;AACnC,YAAU,KAAK,MAAM,YAAY,CAAC;AAClC,YAAU,SAAS,MAAM,YAAY,CAAC;AACtC,YAAU,QAAQ,MAAM,YAAY,CAAC;AACrC,YAAU,KAAK,MAAM,YAAY,CAAC,YAAY,QAAQ;AACtD,YAAU,QAAQ,MAAM,YAAY,CAAC;AACrC,YAAU,KAAK,MAAM,YAAY;AAGjC,YAAU,KAAK,MAAM,YAAY;AACjC,YAAU,KAAK,MAAM,YAAY,CAAC;AAClC,YAAU,OAAO,MAAM,YAAY,CAAC;AACpC,YAAU,SAAS,MAAM,YAAY,CAAC;AACtC,YAAU,KAAK,MAAM,YAAY,CAAC;AAElC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA,IAET,WAAW,MAAM,SAAS;AACxB,UAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,eAAO,KAAK,eAAe,MAAM;AAAA;AAEnC,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,OAAO;AAAA;AAErB,aAAO,KAAK,OAAO;AAAA;AAAA,WAEd,MAAM,OAAO,SAAS;AAC3B,cAAQ,MAAM;AACd,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA;AAET,UAAI,SAAS,GAAG,KAAK,QAAQ,WAAW;AACtC,gBAAQ,SAAS,GAAG,OAAO,QAAQ,UAAU;AAAA,aAE1C;AACH,gBAAQ,IAAI,KAAK,GAAG,SAAS,QAAQ;AAAA;AAEvC,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,OAAO;AAClB,aAAO,MAAM;AAAA;AAAA;AAGjB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AAEN,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,KAAK;AAAA,IACpC,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,cAAc,WAAW,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU;AAAA;AAAA;AAI/F,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA;AAAA;", "names": []}