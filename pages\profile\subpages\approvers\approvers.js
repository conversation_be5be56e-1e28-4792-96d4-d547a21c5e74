
// 审批人管理页面
Page({
  data: {
    approvers: [],               // 审批人列表数据
    showAddDialog: false,        // 是否显示添加/编辑对话框
    currentApprover: null,       // 当前正在编辑的审批人对象
    formData: {                  // 表单数据
      name: '',                  // 审批人姓名
      department: '',            // 所属部门
      role: '部门主管'           // 角色（默认为部门主管）
    }
  },

  onLoad: function(options) {
    // 页面加载时获取审批人列表
    this.loadApprovers();
  },

  // 加载审批人列表数据
  loadApprovers: function() {
    // TODO: 替换为实际API调用
    // 示例数据
    const demoData = [
      {
        id: 1,                   // 审批人ID
        name: "李主管",          // 姓名
        department: "技术部",    // 部门
        role: "部门主管"         // 角色
      },
      {
        id: 2,                   // 审批人ID
        name: "王经理",          // 姓名
        department: "销售部",    // 部门
        role: "部门经理"         // 角色
      }
    ];
    // 更新页面数据
    this.setData({ approvers: demoData });
  },

  // 打开添加审批人对话框
  openAddDialog: function() {
    // 重置表单数据并显示对话框
    this.setData({
      showAddDialog: true,       // 显示对话框
      currentApprover: null,     // 清空当前编辑的审批人
      formData: {                // 重置表单数据
        name: '',                // 姓名
        department: '',          // 部门
        role: '部门主管'         // 角色（默认）
      }
    });
  },

  // 打开编辑审批人对话框
  openEditDialog: function(e) {
    const { approver } = e.currentTarget.dataset;  // 获取要编辑的审批人数据
    // 设置表单数据并显示对话框
    this.setData({
      showAddDialog: true,       // 显示对话框
      currentApprover: approver, // 设置当前编辑的审批人
      formData: { ...approver }  // 复制审批人数据到表单
    });
  },

  // 处理表单输入
  onFormInput: function(e) {
    const { field } = e.currentTarget.dataset;  // 获取输入字段名
    const { value } = e.detail;                 // 获取输入的值
    // 更新表单数据
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交表单（添加/编辑审批人）
  submitForm: function() {
    const { currentApprover, formData } = this.data;  // 获取表单数据
    // TODO: 替换为实际API调用
    if (currentApprover) {
      // 编辑现有审批人
      wx.showToast({
        title: '审批人信息已更新',
        icon: 'success'
      });
    } else {
      // 添加新审批人
      wx.showToast({
        title: '审批人添加成功',
        icon: 'success'
      });
    }
    this.setData({ showAddDialog: false });  // 关闭对话框
    this.loadApprovers();  // 重新加载审批人列表
  },

  // 删除审批人
  deleteApprover: function(e) {
    const { id } = e.currentTarget.dataset;  // 获取要删除的审批人ID
    // 显示确认对话框
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此审批人吗？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认删除
          // TODO: 替换为实际API调用
          wx.showToast({
            title: '审批人已删除',
            icon: 'success'
          });
          this.loadApprovers();  // 重新加载审批人列表
        }
      }
    });
  },

  // 关闭对话框
  closeDialog: function() {
    this.setData({ showAddDialog: false });  // 隐藏对话框
  }
});