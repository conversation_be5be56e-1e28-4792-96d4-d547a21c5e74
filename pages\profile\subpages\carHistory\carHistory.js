
// carHistory.js
const dataManager = require('../../../../utils/dataManager');

Page({
  data: {
    carUsageHistory: [],
    currentTab: 'all', // all, pending, completed, rejected
    statusMap: {
      'pending': '待审批',
      'completed': '已完成',
      'rejected': '已拒绝'
    }
  },

  onLoad: function() {
    wx.showLoading({
      title: '加载中...',
    });
    this.loadCarHistory();
    setTimeout(() => {
      wx.hideLoading();
    }, 500);
  },

  // 加载用车记录
  loadCarHistory: function() {
    const history = dataManager.getData('carUsageHistory') || [];
    this.setData({
      carUsageHistory: history
    });
    this.filterRecords(this.data.currentTab);
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
    this.filterRecords(tab);
  },

  // 根据状态筛选记录
  filterRecords: function(tab) {
    const history = dataManager.getData('carUsageHistory') || [];
    let filteredHistory;

    switch(tab) {
      case 'pending':
        filteredHistory = history.filter(item => item.status === '待审批');
        break;
      case 'completed':
        filteredHistory = history.filter(item => item.status === '已完成');
        break;
      case 'rejected':
        filteredHistory = history.filter(item => item.status === '已拒绝');
        break;
      default:
        filteredHistory = history;
    }

    this.setData({
      carUsageHistory: filteredHistory
    });
  },

  // 查看记录详情
  viewDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const record = this.data.carUsageHistory.find(item => item.id === id);
    
    if (record) {
      wx.showModal({
        title: '用车详情',
        content: `申请日期：${record.date}\n` +
                `用车原因：${record.reason}\n` +
                `车牌号码：${record.vehicle}\n` +
                `审批人：${record.approver}\n` +
                `审批时间：${record.approveTime || '待审批'}\n` +
                `当前状态：${record.status}`,
        showCancel: false
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadCarHistory();
    wx.stopPullDownRefresh();
  }
})