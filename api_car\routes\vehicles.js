const express = require('express');
const router = express.Router();
const vehicleController = require('../controllers/vehicleController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validate, validateQuery, validateParams } = require('../middleware/validation');
const { vehicleSchemas, commonSchemas } = require('../utils/validation');

// 获取车辆列表
router.get('/', 
  authenticateToken, 
  validateQuery(vehicleSchemas.vehicleQuery),
  vehicleController.getVehicles
);

// 获取单个车辆信息
router.get('/:id', 
  authenticateToken, 
  validateParams(commonSchemas.idParam),
  vehicleController.getVehicleById
);

// 添加车辆
router.post('/', 
  authenticateToken, 
  requireAdmin,
  validate(vehicleSchemas.createVehicle), 
  vehicleController.createVehicle
);

// 更新车辆信息
router.put('/:id', 
  authenticateToken, 
  requireAdmin,
  validateParams(commonSchemas.idParam),
  validate(vehicleSchemas.updateVehicle), 
  vehicleController.updateVehicle
);

// 删除车辆
router.delete('/:id', 
  authenticateToken, 
  requireAdmin,
  validateParams(commonSchemas.idParam),
  vehicleController.deleteVehicle
);

module.exports = router;
