
Page({
  data: {
    pendingList: [
      {
        id: 1,
        applicant: '张三',
        department: '技术部',
        purpose: '客户拜访',
        startTime: '2023-10-15',
        endTime: '2023-10-15',
        destination: '广州市天河区',
        status: 'pending'
      },
      {
        id: 2,
        applicant: '李四',
        department: '销售部',
        purpose: '产品演示',
        startTime: '2023-10-16',
        endTime: '2023-10-17',
        destination: '深圳市南山区',
        status: 'pending'
      }
    ]
  },

  onLoad: function(options) {
    // 页面加载时执行
  },

  // 审批通过
  approveApplication: function(e) {
    const { id } = e.currentTarget.dataset;
    this.updateStatus(id, 'approved');
    wx.showToast({
      title: '已批准',
      icon: 'success'
    });
  },

  // 审批拒绝
  rejectApplication: function(e) {
    const { id } = e.currentTarget.dataset;
    this.updateStatus(id, 'rejected');
    wx.showToast({
      title: '已拒绝',
      icon: 'none'
    });
  },

  // 更新状态
  updateStatus: function(id, status) {
    const { pendingList } = this.data;
    const updatedList = pendingList.map(item => {
      if (item.id === id) {
        return { ...item, status };
      }
      return item;
    });
    
    this.setData({
      pendingList: updatedList
    });
  }
});