const express = require('express');
const router = express.Router();

// 导入各模块路由
const userRoutes = require('./user');
const approverRoutes = require('./approvers');
const vehicleRoutes = require('./vehicles');
const carUsageRoutes = require('./carUsage');
const settingsRoutes = require('./settings');

// 注册路由
router.use('/user', userRoutes);
router.use('/approvers', approverRoutes);
router.use('/vehicles', vehicleRoutes);
router.use('/car-usage', carUsageRoutes);
router.use('/settings', settingsRoutes);

module.exports = router;
