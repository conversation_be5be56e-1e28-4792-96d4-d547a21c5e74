
// 数据管理模块 - 负责小程序数据的获取和管理（通过API）
const api = require('./apiManager');  // 导入API管理模块

// 获取日志管理器（用于记录操作日志）
const logger = wx.getLogManager({level: 1});  // 设置日志级别为1

// 数据变更监听器集合
const listeners = new Set();  // 用于存储数据变更的回调函数

// 本地缓存键
const CACHE_KEYS = {
  USER_INFO: 'userInfo',
  APPROVERS: 'approvers',
  VEHICLES: 'vehicles',
  CAR_USAGE_HISTORY: 'carUsageHistory',
  NOTIFICATION_SETTINGS: 'notificationSettings'
};

// 初始化数据 - 在应用启动时调用
const initData = async () => {
  logger.info('初始化数据，从API获取');  // 记录初始化开始
  try {
    // 预加载一些常用数据到缓存
    await Promise.all([
      _fetchAndCacheUserInfo(),
      _fetchAndCacheApprovers(),
      _fetchAndCacheVehicles(),
      _fetchAndCacheNotificationSettings()
    ]);
    
    logger.info('数据初始化完成');
  } catch (error) {
    logger.warn('初始化数据过程中发生错误:', error);  // 记录错误
  }
};

// 私有方法：获取并缓存用户信息
const _fetchAndCacheUserInfo = async () => {
  try {
    const userInfo = await api.user.getUserInfo();
    wx.setStorageSync(CACHE_KEYS.USER_INFO, userInfo);
    return userInfo;
  } catch (error) {
    logger.error('获取用户信息失败:', error);
    return null;
  }
};

// 私有方法：获取并缓存审批人列表
const _fetchAndCacheApprovers = async () => {
  try {
    const approvers = await api.approvers.getApprovers();
    wx.setStorageSync(CACHE_KEYS.APPROVERS, approvers);
    return approvers;
  } catch (error) {
    logger.error('获取审批人列表失败:', error);
    return [];
  }
};

// 私有方法：获取并缓存车辆列表
const _fetchAndCacheVehicles = async () => {
  try {
    const vehicles = await api.vehicles.getVehicles();
    wx.setStorageSync(CACHE_KEYS.VEHICLES, vehicles);
    return vehicles;
  } catch (error) {
    logger.error('获取车辆列表失败:', error);
    return [];
  }
};

// 私有方法：获取并缓存通知设置
const _fetchAndCacheNotificationSettings = async () => {
  try {
    const settings = await api.settings.getNotificationSettings();
    wx.setStorageSync(CACHE_KEYS.NOTIFICATION_SETTINGS, settings);
    return settings;
  } catch (error) {
    logger.error('获取通知设置失败:', error);
    return {
      approvalNotice: true,
      usageReminder: true
    };
  }
};

// 获取所有数据（为了兼容性保留，但实际上现在是异步获取各部分数据）
const getAllData = async () => {
  try {
    // 从API获取最新数据
    const [userInfo, approvers, vehicles, carUsageHistory, notificationSettings] = await Promise.all([
      api.user.getUserInfo(),
      api.approvers.getApprovers(),
      api.vehicles.getVehicles(),
      api.carUsage.getHistory(),
      api.settings.getNotificationSettings()
    ]);
    
    return {
      userInfo,
      approvers,
      vehicles,
      carUsageHistory,
      notificationSettings
    };
  } catch (e) {
    logger.error('获取所有数据失败:', e);
    
    // 从缓存获取数据作为备选
    return {
      userInfo: wx.getStorageSync(CACHE_KEYS.USER_INFO) || null,
      approvers: wx.getStorageSync(CACHE_KEYS.APPROVERS) || [],
      vehicles: wx.getStorageSync(CACHE_KEYS.VEHICLES) || [],
      carUsageHistory: wx.getStorageSync(CACHE_KEYS.CAR_USAGE_HISTORY) || [],
      notificationSettings: wx.getStorageSync(CACHE_KEYS.NOTIFICATION_SETTINGS) || {
        approvalNotice: true,
        usageReminder: true
      }
    };
  }
};

// 获取指定键的数据
const getData = async (key) => {
  try {
    // 根据不同的键调用不同的API
    switch (key) {
      case 'userInfo':
        return await api.user.getUserInfo();
      case 'approvers':
        return await api.approvers.getApprovers();
      case 'vehicles':
        return await api.vehicles.getVehicles();
      case 'carUsageHistory':
        return await api.carUsage.getHistory();
      case 'notificationSettings':
        return await api.settings.getNotificationSettings();
      default:
        logger.warn(`未知的数据键: ${key}`);
        return null;
    }
  } catch (e) {
    logger.error(`获取数据[${key}]失败:`, e);
    
    // 从缓存获取数据作为备选
    return wx.getStorageSync(key) || null;
  }
};

// 保存数据
const saveData = async (key, value) => {
  try {
    // 根据不同的键调用不同的API
    switch (key) {
      case 'userInfo':
        await api.user.updateUserInfo(value);
        break;
      case 'approvers':
        // 这里需要更复杂的逻辑，因为approvers是一个数组
        // 实际应用中可能需要比较新旧数据，执行增删改操作
        // 这里简化处理，假设前端会传完整的新数组
        for (const approver of value) {
          if (approver.id) {
            await api.approvers.updateApprover(approver.id, approver);
          } else {
            await api.approvers.addApprover(approver);
          }
        }
        break;
      case 'vehicles':
        // 同上，简化处理
        for (const vehicle of value) {
          if (vehicle.id) {
            await api.vehicles.updateVehicle(vehicle.id, vehicle);
          } else {
            await api.vehicles.addVehicle(vehicle);
          }
        }
        break;
      case 'carUsageHistory':
        // 用车历史通常只添加不修改
        for (const record of value) {
          if (!record.id) {
            await api.carUsage.addRecord(record);
          }
        }
        break;
      case 'notificationSettings':
        await api.settings.updateNotificationSettings(value);
        break;
      default:
        logger.warn(`未知的数据键: ${key}`);
        return false;
    }
    
    // 更新本地缓存
    wx.setStorageSync(key, value);
    
    // 通知所有监听器数据已更新
    notifyListeners(key, value);
    
    return true;  // 保存成功
  } catch (e) {
    logger.error(`保存数据[${key}]失败:`, e);
    return false;  // 保存失败
  }
};

// 添加数据变更监听器
const addListener = (callback) => {
  listeners.add(callback);  // 添加回调函数到监听器集合
};

// 移除数据变更监听器
const removeListener = (callback) => {
  listeners.delete(callback);  // 从监听器集合中移除回调函数
};

// 通知所有监听器数据已更新
const notifyListeners = (key, value) => {
  listeners.forEach(callback => {
    try {
      callback(key, value);  // 调用每个监听器的回调函数
    } catch (e) {
      logger.error('监听器调用失败:', e);  // 记录错误
    }
  });
};

// 导出模块接口
module.exports = {
  initData,         // 初始化数据
  getAllData,       // 获取所有数据
  getData,          // 获取指定数据
  saveData,         // 保存数据
  addListener,      // 添加监听器
  removeListener    // 移除监听器
};