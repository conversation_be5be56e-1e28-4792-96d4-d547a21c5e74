{"version": 3, "sources": ["../src/model.js"], "sourcesContent": ["'use strict';\n\nconst assert = require('assert');\nconst _ = require('lodash');\nconst Dottie = require('dottie');\n\nconst Utils = require('./utils');\nconst { logger } = require('./utils/logger');\nconst BelongsTo = require('./associations/belongs-to');\nconst BelongsToMany = require('./associations/belongs-to-many');\nconst InstanceValidator = require('./instance-validator');\nconst QueryTypes = require('./query-types');\nconst sequelizeErrors = require('./errors');\nconst Association = require('./associations/base');\nconst HasMany = require('./associations/has-many');\nconst DataTypes = require('./data-types');\nconst Hooks = require('./hooks');\nconst associationsMixin = require('./associations/mixin');\nconst Op = require('./operators');\nconst { noDoubleNestedGroup } = require('./utils/deprecations');\n\n\n// This list will quickly become dated, but failing to maintain this list just means\n// we won't throw a warning when we should. At least most common cases will forever be covered\n// so we stop throwing erroneous warnings when we shouldn't.\nconst validQueryKeywords = new Set(['where', 'attributes', 'paranoid', 'include', 'order', 'limit', 'offset',\n  'transaction', 'lock', 'raw', 'logging', 'benchmark', 'having', 'searchPath', 'rejectOnEmpty', 'plain',\n  'scope', 'group', 'through', 'defaults', 'distinct', 'primary', 'exception', 'type', 'hooks', 'force',\n  'name']);\n\n// List of attributes that should not be implicitly passed into subqueries/includes.\nconst nonCascadingOptions = ['include', 'attributes', 'originalAttributes', 'order', 'where', 'limit', 'offset', 'plain', 'group', 'having'];\n\n/**\n * A Model represents a table in the database. Instances of this class represent a database row.\n *\n * Model instances operate with the concept of a `dataValues` property, which stores the actual values represented by the instance.\n * By default, the values from dataValues can also be accessed directly from the Instance, that is:\n * ```js\n * instance.field\n * // is the same as\n * instance.get('field')\n * // is the same as\n * instance.getDataValue('field')\n * ```\n * However, if getters and/or setters are defined for `field` they will be invoked, instead of returning the value from `dataValues`.\n * Accessing properties directly or using `get` is preferred for regular use, `getDataValue` should only be used for custom getters.\n *\n * @see\n   * {@link Sequelize#define} for more information about getters and setters\n * @mixes Hooks\n */\nclass Model {\n  static get queryInterface() {\n    return this.sequelize.getQueryInterface();\n  }\n\n  static get queryGenerator() {\n    return this.queryInterface.queryGenerator;\n  }\n\n  /**\n   * A reference to the sequelize instance\n   *\n   * @see\n   * {@link Sequelize}\n   *\n   * @property sequelize\n   *\n   * @returns {Sequelize}\n   */\n  get sequelize() {\n    return this.constructor.sequelize;\n  }\n\n  /**\n   * Builds a new model instance.\n   *\n   * @param {object}  [values={}] an object of key value pairs\n   * @param {object}  [options] instance construction options\n   * @param {boolean} [options.raw=false] If set to true, values will ignore field and virtual setters.\n   * @param {boolean} [options.isNewRecord=true] Is this a new record\n   * @param {Array}   [options.include] an array of include options - Used to build prefetched/included model instances. See `set`\n   */\n  constructor(values = {}, options = {}) {\n    if (!this.constructor._overwrittenAttributesChecked) {\n      this.constructor._overwrittenAttributesChecked = true;\n\n      // setTimeout is hacky but necessary.\n      // Public Class Fields declared by descendants of this class\n      // will not be available until after their call to super, so after\n      // this constructor is done running.\n      setTimeout(() => {\n        const overwrittenAttributes = [];\n        for (const key of Object.keys(this.constructor._attributeManipulation)) {\n          if (Object.prototype.hasOwnProperty.call(this, key)) {\n            overwrittenAttributes.push(key);\n          }\n        }\n\n        if (overwrittenAttributes.length > 0) {\n          logger.warn(`Model ${JSON.stringify(this.constructor.name)} is declaring public class fields for attribute(s): ${overwrittenAttributes.map(attr => JSON.stringify(attr)).join(', ')}.` +\n            '\\nThese class fields are shadowing Sequelize\\'s attribute getters & setters.' +\n            '\\nSee https://sequelize.org/main/manual/model-basics.html#caveat-with-public-class-fields');\n        }\n      }, 0);\n    }\n\n    options = {\n      isNewRecord: true,\n      _schema: this.constructor._schema,\n      _schemaDelimiter: this.constructor._schemaDelimiter,\n      ...options\n    };\n\n    if (options.attributes) {\n      options.attributes = options.attributes.map(attribute => Array.isArray(attribute) ? attribute[1] : attribute);\n    }\n\n    if (!options.includeValidated) {\n      this.constructor._conformIncludes(options, this.constructor);\n      if (options.include) {\n        this.constructor._expandIncludeAll(options);\n        this.constructor._validateIncludedElements(options);\n      }\n    }\n\n    this.dataValues = {};\n    this._previousDataValues = {};\n    this.uniqno = 1;\n    this._changed = new Set();\n    this._options = options;\n\n    /**\n     * Returns true if this instance has not yet been persisted to the database\n     *\n     * @property isNewRecord\n     * @returns {boolean}\n     */\n    this.isNewRecord = options.isNewRecord;\n\n    this._initValues(values, options);\n  }\n\n  _initValues(values, options) {\n    let defaults;\n    let key;\n\n    values = { ...values };\n\n    if (options.isNewRecord) {\n      defaults = {};\n\n      if (this.constructor._hasDefaultValues) {\n        defaults = _.mapValues(this.constructor._defaultValues, valueFn => {\n          const value = valueFn();\n          return value && value instanceof Utils.SequelizeMethod ? value : _.cloneDeep(value);\n        });\n      }\n\n      // set id to null if not passed as value, a newly created dao has no id\n      // removing this breaks bulkCreate\n      // do after default values since it might have UUID as a default value\n      if (this.constructor.primaryKeyAttributes.length) {\n        this.constructor.primaryKeyAttributes.forEach(primaryKeyAttribute => {\n          if (!Object.prototype.hasOwnProperty.call(defaults, primaryKeyAttribute)) {\n            defaults[primaryKeyAttribute] = null;\n          }\n        });\n      }\n\n      if (this.constructor._timestampAttributes.createdAt && defaults[this.constructor._timestampAttributes.createdAt]) {\n        this.dataValues[this.constructor._timestampAttributes.createdAt] = Utils.toDefaultValue(defaults[this.constructor._timestampAttributes.createdAt], this.sequelize.options.dialect);\n        delete defaults[this.constructor._timestampAttributes.createdAt];\n      }\n\n      if (this.constructor._timestampAttributes.updatedAt && defaults[this.constructor._timestampAttributes.updatedAt]) {\n        this.dataValues[this.constructor._timestampAttributes.updatedAt] = Utils.toDefaultValue(defaults[this.constructor._timestampAttributes.updatedAt], this.sequelize.options.dialect);\n        delete defaults[this.constructor._timestampAttributes.updatedAt];\n      }\n\n      if (this.constructor._timestampAttributes.deletedAt && defaults[this.constructor._timestampAttributes.deletedAt]) {\n        this.dataValues[this.constructor._timestampAttributes.deletedAt] = Utils.toDefaultValue(defaults[this.constructor._timestampAttributes.deletedAt], this.sequelize.options.dialect);\n        delete defaults[this.constructor._timestampAttributes.deletedAt];\n      }\n\n      for (key in defaults) {\n        if (values[key] === undefined) {\n          this.set(key, Utils.toDefaultValue(defaults[key], this.sequelize.options.dialect), { raw: true });\n          delete values[key];\n        }\n      }\n    }\n\n    this.set(values, options);\n  }\n\n  // validateIncludedElements should have been called before this method\n  static _paranoidClause(model, options = {}) {\n    // Apply on each include\n    // This should be handled before handling where conditions because of logic with returns\n    // otherwise this code will never run on includes of a already conditionable where\n    if (options.include) {\n      for (const include of options.include) {\n        this._paranoidClause(include.model, include);\n      }\n    }\n\n    // apply paranoid when groupedLimit is used\n    if (_.get(options, 'groupedLimit.on.options.paranoid')) {\n      const throughModel = _.get(options, 'groupedLimit.on.through.model');\n      if (throughModel) {\n        options.groupedLimit.through = this._paranoidClause(throughModel, options.groupedLimit.through);\n      }\n    }\n\n    if (!model.options.timestamps || !model.options.paranoid || options.paranoid === false) {\n      // This model is not paranoid, nothing to do here;\n      return options;\n    }\n\n    const deletedAtCol = model._timestampAttributes.deletedAt;\n    const deletedAtAttribute = model.rawAttributes[deletedAtCol];\n    const deletedAtObject = {};\n\n    let deletedAtDefaultValue = Object.prototype.hasOwnProperty.call(deletedAtAttribute, 'defaultValue') ? deletedAtAttribute.defaultValue : null;\n\n    deletedAtDefaultValue = deletedAtDefaultValue || {\n      [Op.eq]: null\n    };\n\n    deletedAtObject[deletedAtAttribute.field || deletedAtCol] = deletedAtDefaultValue;\n\n    if (Utils.isWhereEmpty(options.where)) {\n      options.where = deletedAtObject;\n    } else {\n      options.where = { [Op.and]: [deletedAtObject, options.where] };\n    }\n\n    return options;\n  }\n\n  static _addDefaultAttributes() {\n    const tail = {};\n    let head = {};\n\n    // Add id if no primary key was manually added to definition\n    // Can't use this.primaryKeys here, since this function is called before PKs are identified\n    if (!_.some(this.rawAttributes, 'primaryKey')) {\n      if ('id' in this.rawAttributes) {\n        // Something is fishy here!\n        throw new Error(`A column called 'id' was added to the attributes of '${this.tableName}' but not marked with 'primaryKey: true'`);\n      }\n\n      head = {\n        id: {\n          type: new DataTypes.INTEGER(),\n          allowNull: false,\n          primaryKey: true,\n          autoIncrement: true,\n          _autoGenerated: true\n        }\n      };\n    }\n\n    if (this._timestampAttributes.createdAt) {\n      tail[this._timestampAttributes.createdAt] = {\n        type: DataTypes.DATE,\n        allowNull: false,\n        _autoGenerated: true\n      };\n    }\n\n    if (this._timestampAttributes.updatedAt) {\n      tail[this._timestampAttributes.updatedAt] = {\n        type: DataTypes.DATE,\n        allowNull: false,\n        _autoGenerated: true\n      };\n    }\n\n    if (this._timestampAttributes.deletedAt) {\n      tail[this._timestampAttributes.deletedAt] = {\n        type: DataTypes.DATE,\n        _autoGenerated: true\n      };\n    }\n\n    if (this._versionAttribute) {\n      tail[this._versionAttribute] = {\n        type: DataTypes.INTEGER,\n        allowNull: false,\n        defaultValue: 0,\n        _autoGenerated: true\n      };\n    }\n\n    const newRawAttributes = {\n      ...head,\n      ...this.rawAttributes\n    };\n    _.each(tail, (value, attr) => {\n      if (newRawAttributes[attr] === undefined) {\n        newRawAttributes[attr] = value;\n      }\n    });\n\n    this.rawAttributes = newRawAttributes;\n\n    if (!Object.keys(this.primaryKeys).length) {\n      this.primaryKeys.id = this.rawAttributes.id;\n    }\n  }\n\n  /**\n   * Returns the attributes of the model.\n   *\n   * @returns {object|any}\n  */\n  static getAttributes() {\n    return this.rawAttributes;\n  }\n\n  static _findAutoIncrementAttribute() {\n    this.autoIncrementAttribute = null;\n\n    for (const name in this.rawAttributes) {\n      if (Object.prototype.hasOwnProperty.call(this.rawAttributes, name)) {\n        const definition = this.rawAttributes[name];\n        if (definition && definition.autoIncrement) {\n          if (this.autoIncrementAttribute) {\n            throw new Error('Invalid Instance definition. Only one autoincrement field allowed.');\n          }\n          this.autoIncrementAttribute = name;\n        }\n      }\n    }\n  }\n\n  static _conformIncludes(options, self) {\n    if (!options.include) return;\n\n    // if include is not an array, wrap in an array\n    if (!Array.isArray(options.include)) {\n      options.include = [options.include];\n    } else if (!options.include.length) {\n      delete options.include;\n      return;\n    }\n\n    // convert all included elements to { model: Model } form\n    options.include = options.include.map(include => this._conformInclude(include, self));\n  }\n\n  static _transformStringAssociation(include, self) {\n    if (self && typeof include === 'string') {\n      if (!Object.prototype.hasOwnProperty.call(self.associations, include)) {\n        throw new Error(`Association with alias \"${include}\" does not exist on ${self.name}`);\n      }\n      return self.associations[include];\n    }\n    return include;\n  }\n\n  static _conformInclude(include, self) {\n    if (include) {\n      let model;\n\n      if (include._pseudo) return include;\n\n      include = this._transformStringAssociation(include, self);\n\n      if (include instanceof Association) {\n        if (self && include.target.name === self.name) {\n          model = include.source;\n        } else {\n          model = include.target;\n        }\n\n        return { model, association: include, as: include.as };\n      }\n\n      if (include.prototype && include.prototype instanceof Model) {\n        return { model: include };\n      }\n\n      if (_.isPlainObject(include)) {\n        if (include.association) {\n          include.association = this._transformStringAssociation(include.association, self);\n\n          if (self && include.association.target.name === self.name) {\n            model = include.association.source;\n          } else {\n            model = include.association.target;\n          }\n\n          if (!include.model) include.model = model;\n          if (!include.as) include.as = include.association.as;\n\n          this._conformIncludes(include, model);\n          return include;\n        }\n\n        if (include.model) {\n          this._conformIncludes(include, include.model);\n          return include;\n        }\n\n        if (include.all) {\n          this._conformIncludes(include);\n          return include;\n        }\n      }\n    }\n\n    throw new Error('Include unexpected. Element has to be either a Model, an Association or an object.');\n  }\n\n  static _expandIncludeAllElement(includes, include) {\n    // check 'all' attribute provided is valid\n    let all = include.all;\n    delete include.all;\n\n    if (all !== true) {\n      if (!Array.isArray(all)) {\n        all = [all];\n      }\n\n      const validTypes = {\n        BelongsTo: true,\n        HasOne: true,\n        HasMany: true,\n        One: ['BelongsTo', 'HasOne'],\n        Has: ['HasOne', 'HasMany'],\n        Many: ['HasMany']\n      };\n\n      for (let i = 0; i < all.length; i++) {\n        const type = all[i];\n        if (type === 'All') {\n          all = true;\n          break;\n        }\n\n        const types = validTypes[type];\n        if (!types) {\n          throw new sequelizeErrors.EagerLoadingError(`include all '${type}' is not valid - must be BelongsTo, HasOne, HasMany, One, Has, Many or All`);\n        }\n\n        if (types !== true) {\n          // replace type placeholder e.g. 'One' with its constituent types e.g. 'HasOne', 'BelongsTo'\n          all.splice(i, 1);\n          i--;\n          for (let j = 0; j < types.length; j++) {\n            if (!all.includes(types[j])) {\n              all.unshift(types[j]);\n              i++;\n            }\n          }\n        }\n      }\n    }\n\n    // add all associations of types specified to includes\n    const nested = include.nested;\n    if (nested) {\n      delete include.nested;\n\n      if (!include.include) {\n        include.include = [];\n      } else if (!Array.isArray(include.include)) {\n        include.include = [include.include];\n      }\n    }\n\n    const used = [];\n    (function addAllIncludes(parent, includes) {\n      _.forEach(parent.associations, association => {\n        if (all !== true && !all.includes(association.associationType)) {\n          return;\n        }\n\n        // check if model already included, and skip if so\n        const model = association.target;\n        const as = association.options.as;\n\n        const predicate = { model };\n        if (as) {\n          // We only add 'as' to the predicate if it actually exists\n          predicate.as = as;\n        }\n\n        if (_.some(includes, predicate)) {\n          return;\n        }\n\n        // skip if recursing over a model already nested\n        if (nested && used.includes(model)) {\n          return;\n        }\n        used.push(parent);\n\n        // include this model\n        const thisInclude = Utils.cloneDeep(include);\n        thisInclude.model = model;\n        if (as) {\n          thisInclude.as = as;\n        }\n        includes.push(thisInclude);\n\n        // run recursively if nested\n        if (nested) {\n          addAllIncludes(model, thisInclude.include);\n          if (thisInclude.include.length === 0) delete thisInclude.include;\n        }\n      });\n      used.pop();\n    })(this, includes);\n  }\n\n  static _validateIncludedElements(options, tableNames) {\n    if (!options.model) options.model = this;\n\n    tableNames = tableNames || {};\n    options.includeNames = [];\n    options.includeMap = {};\n\n    /* Legacy */\n    options.hasSingleAssociation = false;\n    options.hasMultiAssociation = false;\n\n    if (!options.parent) {\n      options.topModel = options.model;\n      options.topLimit = options.limit;\n    }\n\n    options.include = options.include.map(include => {\n      include = this._conformInclude(include);\n      include.parent = options;\n      include.topLimit = options.topLimit;\n\n      this._validateIncludedElement.call(options.model, include, tableNames, options);\n\n      if (include.duplicating === undefined) {\n        include.duplicating = include.association.isMultiAssociation;\n      }\n\n      include.hasDuplicating = include.hasDuplicating || include.duplicating;\n      include.hasRequired = include.hasRequired || include.required;\n\n      options.hasDuplicating = options.hasDuplicating || include.hasDuplicating;\n      options.hasRequired = options.hasRequired || include.required;\n\n      options.hasWhere = options.hasWhere || include.hasWhere || !!include.where;\n      return include;\n    });\n\n    for (const include of options.include) {\n      include.hasParentWhere = options.hasParentWhere || !!options.where;\n      include.hasParentRequired = options.hasParentRequired || !!options.required;\n\n      if (include.subQuery !== false && options.hasDuplicating && options.topLimit) {\n        if (include.duplicating) {\n          include.subQuery = include.subQuery || false;\n          include.subQueryFilter = include.hasRequired;\n        } else {\n          include.subQuery = include.hasRequired;\n          include.subQueryFilter = false;\n        }\n      } else {\n        include.subQuery = include.subQuery || false;\n        if (include.duplicating) {\n          include.subQueryFilter = include.subQuery;\n        } else {\n          include.subQueryFilter = false;\n          include.subQuery = include.subQuery || include.hasParentRequired && include.hasRequired && !include.separate;\n        }\n      }\n\n      options.includeMap[include.as] = include;\n      options.includeNames.push(include.as);\n\n      // Set top level options\n      if (options.topModel === options.model && options.subQuery === undefined && options.topLimit) {\n        if (include.subQuery) {\n          options.subQuery = include.subQuery;\n        } else if (include.hasDuplicating) {\n          options.subQuery = true;\n        }\n      }\n\n      /* Legacy */\n      options.hasIncludeWhere = options.hasIncludeWhere || include.hasIncludeWhere || !!include.where;\n      options.hasIncludeRequired = options.hasIncludeRequired || include.hasIncludeRequired || !!include.required;\n\n      if (include.association.isMultiAssociation || include.hasMultiAssociation) {\n        options.hasMultiAssociation = true;\n      }\n      if (include.association.isSingleAssociation || include.hasSingleAssociation) {\n        options.hasSingleAssociation = true;\n      }\n    }\n\n    if (options.topModel === options.model && options.subQuery === undefined) {\n      options.subQuery = false;\n    }\n    return options;\n  }\n\n  static _validateIncludedElement(include, tableNames, options) {\n    tableNames[include.model.getTableName()] = true;\n\n    if (include.attributes && !options.raw) {\n      include.model._expandAttributes(include);\n\n      include.originalAttributes = include.model._injectDependentVirtualAttributes(include.attributes);\n\n      include = Utils.mapFinderOptions(include, include.model);\n\n      if (include.attributes.length) {\n        _.each(include.model.primaryKeys, (attr, key) => {\n          // Include the primary key if it's not already included - take into account that the pk might be aliased (due to a .field prop)\n          if (!include.attributes.some(includeAttr => {\n            if (attr.field !== key) {\n              return Array.isArray(includeAttr) && includeAttr[0] === attr.field && includeAttr[1] === key;\n            }\n            return includeAttr === key;\n          })) {\n            include.attributes.unshift(key);\n          }\n        });\n      }\n    } else {\n      include = Utils.mapFinderOptions(include, include.model);\n    }\n\n    // pseudo include just needed the attribute logic, return\n    if (include._pseudo) {\n      if (!include.attributes) {\n        include.attributes = Object.keys(include.model.tableAttributes);\n      }\n      return Utils.mapFinderOptions(include, include.model);\n    }\n\n    // check if the current Model is actually associated with the passed Model - or it's a pseudo include\n    const association = include.association || this._getIncludedAssociation(include.model, include.as);\n\n    include.association = association;\n    include.as = association.as;\n\n    // If through, we create a pseudo child include, to ease our parsing later on\n    if (include.association.through && Object(include.association.through.model) === include.association.through.model) {\n      if (!include.include) include.include = [];\n      const through = include.association.through;\n\n      include.through = _.defaults(include.through || {}, {\n        model: through.model,\n        as: through.model.name,\n        association: {\n          isSingleAssociation: true\n        },\n        _pseudo: true,\n        parent: include\n      });\n\n\n      if (through.scope) {\n        include.through.where = include.through.where ? { [Op.and]: [include.through.where, through.scope] } : through.scope;\n      }\n\n      include.include.push(include.through);\n      tableNames[through.tableName] = true;\n    }\n\n    // include.model may be the main model, while the association target may be scoped - thus we need to look at association.target/source\n    let model;\n    if (include.model.scoped === true) {\n      // If the passed model is already scoped, keep that\n      model = include.model;\n    } else {\n      // Otherwise use the model that was originally passed to the association\n      model = include.association.target.name === include.model.name ? include.association.target : include.association.source;\n    }\n\n    model._injectScope(include);\n\n    // This check should happen after injecting the scope, since the scope may contain a .attributes\n    if (!include.attributes) {\n      include.attributes = Object.keys(include.model.tableAttributes);\n    }\n\n    include = Utils.mapFinderOptions(include, include.model);\n\n    if (include.required === undefined) {\n      include.required = !!include.where;\n    }\n\n    if (include.association.scope) {\n      include.where = include.where ? { [Op.and]: [include.where, include.association.scope] } : include.association.scope;\n    }\n\n    if (include.limit && include.separate === undefined) {\n      include.separate = true;\n    }\n\n    if (include.separate === true) {\n      if (!(include.association instanceof HasMany)) {\n        throw new Error('Only HasMany associations support include.separate');\n      }\n\n      include.duplicating = false;\n\n      if (\n        options.attributes\n        && options.attributes.length\n        && !_.flattenDepth(options.attributes, 2).includes(association.sourceKey)\n      ) {\n        options.attributes.push(association.sourceKey);\n      }\n\n      if (\n        include.attributes\n        && include.attributes.length\n        && !_.flattenDepth(include.attributes, 2).includes(association.foreignKey)\n      ) {\n        include.attributes.push(association.foreignKey);\n      }\n    }\n\n    // Validate child includes\n    if (Object.prototype.hasOwnProperty.call(include, 'include')) {\n      this._validateIncludedElements.call(include.model, include, tableNames);\n    }\n\n    return include;\n  }\n\n  static _getIncludedAssociation(targetModel, targetAlias) {\n    const associations = this.getAssociations(targetModel);\n    let association = null;\n    if (associations.length === 0) {\n      throw new sequelizeErrors.EagerLoadingError(`${targetModel.name} is not associated to ${this.name}!`);\n    }\n    if (associations.length === 1) {\n      association = this.getAssociationForAlias(targetModel, targetAlias);\n      if (association) {\n        return association;\n      }\n      if (targetAlias) {\n        const existingAliases = this.getAssociations(targetModel).map(association => association.as);\n        throw new sequelizeErrors.EagerLoadingError(`${targetModel.name} is associated to ${this.name} using an alias. ` +\n          `You've included an alias (${targetAlias}), but it does not match the alias(es) defined in your association (${existingAliases.join(', ')}).`);\n      }\n      throw new sequelizeErrors.EagerLoadingError(`${targetModel.name} is associated to ${this.name} using an alias. ` +\n        'You must use the \\'as\\' keyword to specify the alias within your include statement.');\n    }\n    association = this.getAssociationForAlias(targetModel, targetAlias);\n    if (!association) {\n      throw new sequelizeErrors.EagerLoadingError(`${targetModel.name} is associated to ${this.name} multiple times. ` +\n        'To identify the correct association, you must use the \\'as\\' keyword to specify the alias of the association you want to include.');\n    }\n    return association;\n  }\n\n\n  static _expandIncludeAll(options) {\n    const includes = options.include;\n    if (!includes) {\n      return;\n    }\n\n    for (let index = 0; index < includes.length; index++) {\n      const include = includes[index];\n\n      if (include.all) {\n        includes.splice(index, 1);\n        index--;\n\n        this._expandIncludeAllElement(includes, include);\n      }\n    }\n\n    includes.forEach(include => {\n      this._expandIncludeAll.call(include.model, include);\n    });\n  }\n\n  static _conformIndex(index) {\n    if (!index.fields) {\n      throw new Error('Missing \"fields\" property for index definition');\n    }\n\n    index = _.defaults(index, {\n      type: '',\n      parser: null\n    });\n\n    if (index.type && index.type.toLowerCase() === 'unique') {\n      index.unique = true;\n      delete index.type;\n    }\n\n    return index;\n  }\n\n\n  static _uniqIncludes(options) {\n    if (!options.include) return;\n\n    options.include = _(options.include)\n      .groupBy(include => `${include.model && include.model.name}-${include.as}`)\n      .map(includes => this._assignOptions(...includes))\n      .value();\n  }\n\n  static _baseMerge(...args) {\n    _.assignWith(...args);\n    this._conformIncludes(args[0], this);\n    this._uniqIncludes(args[0]);\n    return args[0];\n  }\n\n  static _mergeFunction(objValue, srcValue, key) {\n    if (Array.isArray(objValue) && Array.isArray(srcValue)) {\n      return _.union(objValue, srcValue);\n    }\n\n    if (['where', 'having'].includes(key)) {\n      if (this.options && this.options.whereMergeStrategy === 'and') {\n        return combineWheresWithAnd(objValue, srcValue);\n      }\n\n      if (srcValue instanceof Utils.SequelizeMethod) {\n        srcValue = { [Op.and]: srcValue };\n      }\n\n      if (_.isPlainObject(objValue) && _.isPlainObject(srcValue)) {\n        return Object.assign(objValue, srcValue);\n      }\n    } else if (key === 'attributes' && _.isPlainObject(objValue) && _.isPlainObject(srcValue)) {\n      return _.assignWith(objValue, srcValue, (objValue, srcValue) => {\n        if (Array.isArray(objValue) && Array.isArray(srcValue)) {\n          return _.union(objValue, srcValue);\n        }\n      });\n    }\n    // If we have a possible object/array to clone, we try it.\n    // Otherwise, we return the original value when it's not undefined,\n    // or the resulting object in that case.\n    if (srcValue) {\n      return Utils.cloneDeep(srcValue, true);\n    }\n    return srcValue === undefined ? objValue : srcValue;\n  }\n\n  static _assignOptions(...args) {\n    return this._baseMerge(...args, this._mergeFunction.bind(this));\n  }\n\n  static _defaultsOptions(target, opts) {\n    return this._baseMerge(target, opts, (srcValue, objValue, key) => {\n      return this._mergeFunction(objValue, srcValue, key);\n    });\n  }\n\n  /**\n   * Initialize a model, representing a table in the DB, with attributes and options.\n   *\n   * The table columns are defined by the hash that is given as the first argument.\n   * Each attribute of the hash represents a column.\n   *\n   * @example\n   * Project.init({\n   *   columnA: {\n   *     type: Sequelize.BOOLEAN,\n   *     validate: {\n   *       is: ['[a-z]','i'],        // will only allow letters\n   *       max: 23,                  // only allow values <= 23\n   *       isIn: {\n   *         args: [['en', 'zh']],\n   *         msg: \"Must be English or Chinese\"\n   *       }\n   *     },\n   *     field: 'column_a'\n   *     // Other attributes here\n   *   },\n   *   columnB: Sequelize.STRING,\n   *   columnC: 'MY VERY OWN COLUMN TYPE'\n   * }, {sequelize})\n   *\n   * sequelize.models.modelName // The model will now be available in models under the class name\n   *\n   * @see\n   * <a href=\"/master/manual/model-basics.html\">Model Basics</a> guide\n   *\n   * @see\n   * <a href=\"/master/manual/model-basics.html\">Hooks</a> guide\n   *\n   * @see\n   * <a href=\"/master/manual/validations-and-constraints.html\"/>Validations & Constraints</a> guide\n   *\n   * @param {object}                  attributes An object, where each attribute is a column of the table. Each column can be either a DataType, a string or a type-description object, with the properties described below:\n   * @param {string|DataTypes|object} attributes.column The description of a database column\n   * @param {string|DataTypes}        attributes.column.type A string or a data type\n   * @param {boolean}                 [attributes.column.allowNull=true] If false, the column will have a NOT NULL constraint, and a not null validation will be run before an instance is saved.\n   * @param {any}                     [attributes.column.defaultValue=null] A literal default value, a JavaScript function, or an SQL function (see `sequelize.fn`)\n   * @param {string|boolean}          [attributes.column.unique=false] If true, the column will get a unique constraint. If a string is provided, the column will be part of a composite unique index. If multiple columns have the same string, they will be part of the same unique index\n   * @param {boolean}                 [attributes.column.primaryKey=false] If true, this attribute will be marked as primary key\n   * @param {string}                  [attributes.column.field=null] If set, sequelize will map the attribute name to a different name in the database\n   * @param {boolean}                 [attributes.column.autoIncrement=false] If true, this column will be set to auto increment\n   * @param {boolean}                 [attributes.column.autoIncrementIdentity=false] If true, combined with autoIncrement=true, will use Postgres `GENERATED BY DEFAULT AS IDENTITY` instead of `SERIAL`. Postgres 10+ only.\n   * @param {string}                  [attributes.column.comment=null] Comment for this column\n   * @param {string|Model}            [attributes.column.references=null] An object with reference configurations\n   * @param {string|Model}            [attributes.column.references.model] If this column references another table, provide it here as a Model, or a string\n   * @param {string}                  [attributes.column.references.key='id'] The column of the foreign table that this column references\n   * @param {string}                  [attributes.column.onUpdate] What should happen when the referenced key is updated. One of CASCADE, RESTRICT, SET DEFAULT, SET NULL or NO ACTION\n   * @param {string}                  [attributes.column.onDelete] What should happen when the referenced key is deleted. One of CASCADE, RESTRICT, SET DEFAULT, SET NULL or NO ACTION\n   * @param {Function}                [attributes.column.get] Provide a custom getter for this column. Use `this.getDataValue(String)` to manipulate the underlying values.\n   * @param {Function}                [attributes.column.set] Provide a custom setter for this column. Use `this.setDataValue(String, Value)` to manipulate the underlying values.\n   * @param {object}                  [attributes.column.validate] An object of validations to execute for this column every time the model is saved. Can be either the name of a validation provided by validator.js, a validation function provided by extending validator.js (see the `DAOValidator` property for more details), or a custom validation function. Custom validation functions are called with the value of the field and the instance itself as the `this` binding, and can possibly take a second callback argument, to signal that they are asynchronous. If the validator is sync, it should throw in the case of a failed validation; if it is async, the callback should be called with the error text.\n   * @param {object}                  options These options are merged with the default define options provided to the Sequelize constructor\n   * @param {object}                  options.sequelize Define the sequelize instance to attach to the new Model. Throw error if none is provided.\n   * @param {string}                  [options.modelName] Set name of the model. By default its same as Class name.\n   * @param {object}                  [options.defaultScope={}] Define the default search scope to use for this model. Scopes have the same form as the options passed to find / findAll\n   * @param {object}                  [options.scopes] More scopes, defined in the same way as defaultScope above. See `Model.scope` for more information about how scopes are defined, and what you can do with them\n   * @param {boolean}                 [options.omitNull] Don't persist null values. This means that all columns with null values will not be saved\n   * @param {boolean}                 [options.timestamps=true] Adds createdAt and updatedAt timestamps to the model.\n   * @param {boolean}                 [options.paranoid=false] Calling `destroy` will not delete the model, but instead set a `deletedAt` timestamp if this is true. Needs `timestamps=true` to work\n   * @param {boolean}                 [options.underscored=false] Add underscored field to all attributes, this covers user defined attributes, timestamps and foreign keys. Will not affect attributes with explicitly set `field` option\n   * @param {boolean}                 [options.freezeTableName=false] If freezeTableName is true, sequelize will not try to alter the model name to get the table name. Otherwise, the model name will be pluralized\n   * @param {object}                  [options.name] An object with two attributes, `singular` and `plural`, which are used when this model is associated to others.\n   * @param {string}                  [options.name.singular=Utils.singularize(modelName)] Singular name for model\n   * @param {string}                  [options.name.plural=Utils.pluralize(modelName)] Plural name for model\n   * @param {Array<object>}           [options.indexes] indexes definitions\n   * @param {string}                  [options.indexes[].name] The name of the index. Defaults to model name + _ + fields concatenated\n   * @param {string}                  [options.indexes[].type] Index type. Only used by mysql. One of `UNIQUE`, `FULLTEXT` and `SPATIAL`\n   * @param {string}                  [options.indexes[].using] The method to create the index by (`USING` statement in SQL). BTREE and HASH are supported by mysql and postgres, and postgres additionally supports GIST and GIN.\n   * @param {string}                  [options.indexes[].operator] Specify index operator.\n   * @param {boolean}                 [options.indexes[].unique=false] Should the index by unique? Can also be triggered by setting type to `UNIQUE`\n   * @param {boolean}                 [options.indexes[].concurrently=false] PostgresSQL will build the index without taking any write locks. Postgres only\n   * @param {Array<string|object>}    [options.indexes[].fields] An array of the fields to index. Each field can either be a string containing the name of the field, a sequelize object (e.g `sequelize.fn`), or an object with the following attributes: `attribute` (field name), `length` (create a prefix index of length chars), `order` (the direction the column should be sorted in), `collate` (the collation (sort order) for the column)\n   * @param {string|boolean}          [options.createdAt] Override the name of the createdAt attribute if a string is provided, or disable it if false. Timestamps must be true. Underscored field will be set with underscored setting.\n   * @param {string|boolean}          [options.updatedAt] Override the name of the updatedAt attribute if a string is provided, or disable it if false. Timestamps must be true. Underscored field will be set with underscored setting.\n   * @param {string|boolean}          [options.deletedAt] Override the name of the deletedAt attribute if a string is provided, or disable it if false. Timestamps must be true. Underscored field will be set with underscored setting.\n   * @param {string}                  [options.tableName] Defaults to pluralized model name, unless freezeTableName is true, in which case it uses model name verbatim\n   * @param {string}                  [options.schema='public'] schema\n   * @param {string}                  [options.engine] Specify engine for model's table\n   * @param {string}                  [options.charset] Specify charset for model's table\n   * @param {string}                  [options.comment] Specify comment for model's table\n   * @param {string}                  [options.collate] Specify collation for model's table\n   * @param {string}                  [options.initialAutoIncrement] Set the initial AUTO_INCREMENT value for the table in MySQL.\n   * @param {object}                  [options.hooks] An object of hook function that are called before and after certain lifecycle events. The possible hooks are: beforeValidate, afterValidate, validationFailed, beforeBulkCreate, beforeBulkDestroy, beforeBulkUpdate, beforeCreate, beforeDestroy, beforeUpdate, afterCreate, beforeSave, afterDestroy, afterUpdate, afterBulkCreate, afterSave, afterBulkDestroy and afterBulkUpdate. See Hooks for more information about hook functions and their signatures. Each property can either be a function, or an array of functions.\n   * @param {object}                  [options.validate] An object of model wide validations. Validations have access to all model values via `this`. If the validator function takes an argument, it is assumed to be async, and is called with a callback that accepts an optional error.\n   * @param {'and'|'overwrite'}       [options.whereMergeStrategy] Specify the scopes merging strategy (default 'overwrite'). 'and' strategy will merge `where` properties of scopes together by adding `Op.and` at the top-most level. 'overwrite' strategy will overwrite similar attributes using the lastly defined one.\n   *\n   * @returns {Model}\n   */\n  static init(attributes, options = {}) {\n    if (!options.sequelize) {\n      throw new Error('No Sequelize instance passed');\n    }\n\n    this.sequelize = options.sequelize;\n\n    const globalOptions = this.sequelize.options;\n\n    options = Utils.merge(_.cloneDeep(globalOptions.define), options);\n\n    if (!options.modelName) {\n      options.modelName = this.name;\n    }\n\n    options = Utils.merge({\n      name: {\n        plural: Utils.pluralize(options.modelName),\n        singular: Utils.singularize(options.modelName)\n      },\n      indexes: [],\n      omitNull: globalOptions.omitNull,\n      schema: globalOptions.schema\n    }, options);\n\n    this.sequelize.runHooks('beforeDefine', attributes, options);\n\n    if (options.modelName !== this.name) {\n      Object.defineProperty(this, 'name', { value: options.modelName });\n    }\n    delete options.modelName;\n\n    this.options = {\n      timestamps: true,\n      validate: {},\n      freezeTableName: false,\n      underscored: false,\n      paranoid: false,\n      rejectOnEmpty: false,\n      whereCollection: null,\n      schema: null,\n      schemaDelimiter: '',\n      defaultScope: {},\n      scopes: {},\n      indexes: [],\n      whereMergeStrategy: 'overwrite',\n      ...options\n    };\n\n    // if you call \"define\" multiple times for the same modelName, do not clutter the factory\n    if (this.sequelize.isDefined(this.name)) {\n      this.sequelize.modelManager.removeModel(this.sequelize.modelManager.getModel(this.name));\n    }\n\n    this.associations = {};\n    this._setupHooks(options.hooks);\n\n    this.underscored = this.options.underscored;\n\n    if (!this.options.tableName) {\n      this.tableName = this.options.freezeTableName ? this.name : Utils.underscoredIf(Utils.pluralize(this.name), this.underscored);\n    } else {\n      this.tableName = this.options.tableName;\n    }\n\n    this._schema = this.options.schema;\n    this._schemaDelimiter = this.options.schemaDelimiter;\n\n    // error check options\n    _.each(options.validate, (validator, validatorType) => {\n      if (Object.prototype.hasOwnProperty.call(attributes, validatorType)) {\n        throw new Error(`A model validator function must not have the same name as a field. Model: ${this.name}, field/validation name: ${validatorType}`);\n      }\n\n      if (typeof validator !== 'function') {\n        throw new Error(`Members of the validate option must be functions. Model: ${this.name}, error with validate member ${validatorType}`);\n      }\n    });\n\n    if (!_.includes(['and', 'overwrite'], this.options && this.options.whereMergeStrategy)) {\n      throw new Error(`Invalid value ${this.options && this.options.whereMergeStrategy} for whereMergeStrategy. Allowed values are 'and' and 'overwrite'.`);\n    }\n\n\n    this.rawAttributes = _.mapValues(attributes, (attribute, name) => {\n      attribute = this.sequelize.normalizeAttribute(attribute);\n\n      if (attribute.type === undefined) {\n        throw new Error(`Unrecognized datatype for attribute \"${this.name}.${name}\"`);\n      }\n\n      if (attribute.allowNull !== false && _.get(attribute, 'validate.notNull')) {\n        throw new Error(`Invalid definition for \"${this.name}.${name}\", \"notNull\" validator is only allowed with \"allowNull:false\"`);\n      }\n\n      if (_.get(attribute, 'references.model.prototype') instanceof Model) {\n        attribute.references.model = attribute.references.model.getTableName();\n      }\n\n      return attribute;\n    });\n\n    const tableName = this.getTableName();\n    this._indexes = this.options.indexes\n      .map(index => Utils.nameIndex(this._conformIndex(index), tableName));\n\n    this.primaryKeys = {};\n    this._readOnlyAttributes = new Set();\n    this._timestampAttributes = {};\n\n    // setup names of timestamp attributes\n    if (this.options.timestamps) {\n      for (const key of ['createdAt', 'updatedAt', 'deletedAt']) {\n        if (!['undefined', 'string', 'boolean'].includes(typeof this.options[key])) {\n          throw new Error(`Value for \"${key}\" option must be a string or a boolean, got ${typeof this.options[key]}`);\n        }\n        if (this.options[key] === '') {\n          throw new Error(`Value for \"${key}\" option cannot be an empty string`);\n        }\n      }\n\n      if (this.options.createdAt !== false) {\n        this._timestampAttributes.createdAt =\n          typeof this.options.createdAt === 'string' ? this.options.createdAt : 'createdAt';\n        this._readOnlyAttributes.add(this._timestampAttributes.createdAt);\n      }\n      if (this.options.updatedAt !== false) {\n        this._timestampAttributes.updatedAt =\n          typeof this.options.updatedAt === 'string' ? this.options.updatedAt : 'updatedAt';\n        this._readOnlyAttributes.add(this._timestampAttributes.updatedAt);\n      }\n      if (this.options.paranoid && this.options.deletedAt !== false) {\n        this._timestampAttributes.deletedAt =\n          typeof this.options.deletedAt === 'string' ? this.options.deletedAt : 'deletedAt';\n        this._readOnlyAttributes.add(this._timestampAttributes.deletedAt);\n      }\n    }\n\n    // setup name for version attribute\n    if (this.options.version) {\n      this._versionAttribute = typeof this.options.version === 'string' ? this.options.version : 'version';\n      this._readOnlyAttributes.add(this._versionAttribute);\n    }\n\n    this._hasReadOnlyAttributes = this._readOnlyAttributes.size > 0;\n\n    // Add head and tail default attributes (id, timestamps)\n    this._addDefaultAttributes();\n    this.refreshAttributes();\n    this._findAutoIncrementAttribute();\n\n    this._scope = this.options.defaultScope;\n    this._scopeNames = ['defaultScope'];\n\n    this.sequelize.modelManager.addModel(this);\n    this.sequelize.runHooks('afterDefine', this);\n\n    return this;\n  }\n\n  static refreshAttributes() {\n    const attributeManipulation = {};\n\n    this.prototype._customGetters = {};\n    this.prototype._customSetters = {};\n\n    ['get', 'set'].forEach(type => {\n      const opt = `${type}terMethods`;\n      const funcs = { ...this.options[opt] };\n      const _custom = type === 'get' ? this.prototype._customGetters : this.prototype._customSetters;\n\n      _.each(funcs, (method, attribute) => {\n        _custom[attribute] = method;\n\n        if (type === 'get') {\n          funcs[attribute] = function() {\n            return this.get(attribute);\n          };\n        }\n        if (type === 'set') {\n          funcs[attribute] = function(value) {\n            return this.set(attribute, value);\n          };\n        }\n      });\n\n      _.each(this.rawAttributes, (options, attribute) => {\n        if (Object.prototype.hasOwnProperty.call(options, type)) {\n          _custom[attribute] = options[type];\n        }\n\n        if (type === 'get') {\n          funcs[attribute] = function() {\n            return this.get(attribute);\n          };\n        }\n        if (type === 'set') {\n          funcs[attribute] = function(value) {\n            return this.set(attribute, value);\n          };\n        }\n      });\n\n      _.each(funcs, (fct, name) => {\n        if (!attributeManipulation[name]) {\n          attributeManipulation[name] = {\n            configurable: true\n          };\n        }\n        attributeManipulation[name][type] = fct;\n      });\n    });\n\n    this._dataTypeChanges = {};\n    this._dataTypeSanitizers = {};\n\n    this._hasBooleanAttributes = false;\n    this._hasDateAttributes = false;\n    this._jsonAttributes = new Set();\n    this._virtualAttributes = new Set();\n    this._defaultValues = {};\n    this.prototype.validators = {};\n\n    this.fieldRawAttributesMap = {};\n\n    this.primaryKeys = {};\n    this.uniqueKeys = {};\n\n    _.each(this.rawAttributes, (definition, name) => {\n      definition.type = this.sequelize.normalizeDataType(definition.type);\n\n      definition.Model = this;\n      definition.fieldName = name;\n      definition._modelAttribute = true;\n\n      if (definition.field === undefined) {\n        definition.field = Utils.underscoredIf(name, this.underscored);\n      }\n\n      if (definition.primaryKey === true) {\n        this.primaryKeys[name] = definition;\n      }\n\n      this.fieldRawAttributesMap[definition.field] = definition;\n\n      if (definition.type._sanitize) {\n        this._dataTypeSanitizers[name] = definition.type._sanitize;\n      }\n\n      if (definition.type._isChanged) {\n        this._dataTypeChanges[name] = definition.type._isChanged;\n      }\n\n      if (definition.type instanceof DataTypes.BOOLEAN) {\n        this._hasBooleanAttributes = true;\n      } else if (definition.type instanceof DataTypes.DATE || definition.type instanceof DataTypes.DATEONLY) {\n        this._hasDateAttributes = true;\n      } else if (definition.type instanceof DataTypes.JSON) {\n        this._jsonAttributes.add(name);\n      } else if (definition.type instanceof DataTypes.VIRTUAL) {\n        this._virtualAttributes.add(name);\n      }\n\n      if (Object.prototype.hasOwnProperty.call(definition, 'defaultValue')) {\n        this._defaultValues[name] = () => Utils.toDefaultValue(definition.defaultValue, this.sequelize.options.dialect);\n      }\n\n      if (Object.prototype.hasOwnProperty.call(definition, 'unique') && definition.unique) {\n        let idxName;\n        if (\n          typeof definition.unique === 'object' &&\n          Object.prototype.hasOwnProperty.call(definition.unique, 'name')\n        ) {\n          idxName = definition.unique.name;\n        } else if (typeof definition.unique === 'string') {\n          idxName = definition.unique;\n        } else {\n          idxName = `${this.tableName}_${name}_unique`;\n        }\n\n        const idx = this.uniqueKeys[idxName] || { fields: [] };\n\n        idx.fields.push(definition.field);\n        idx.msg = idx.msg || definition.unique.msg || null;\n        idx.name = idxName || false;\n        idx.column = name;\n        idx.customIndex = definition.unique !== true;\n\n        this.uniqueKeys[idxName] = idx;\n      }\n\n      if (Object.prototype.hasOwnProperty.call(definition, 'validate')) {\n        this.prototype.validators[name] = definition.validate;\n      }\n\n      if (definition.index === true && definition.type instanceof DataTypes.JSONB) {\n        this._indexes.push(\n          Utils.nameIndex(\n            this._conformIndex({\n              fields: [definition.field || name],\n              using: 'gin'\n            }),\n            this.getTableName()\n          )\n        );\n\n        delete definition.index;\n      }\n    });\n\n    // Create a map of field to attribute names\n    this.fieldAttributeMap = _.reduce(this.fieldRawAttributesMap, (map, value, key) => {\n      if (key !== value.fieldName) {\n        map[key] = value.fieldName;\n      }\n      return map;\n    }, {});\n\n    this._hasJsonAttributes = !!this._jsonAttributes.size;\n\n    this._hasVirtualAttributes = !!this._virtualAttributes.size;\n\n    this._hasDefaultValues = !_.isEmpty(this._defaultValues);\n\n    this.tableAttributes = _.omitBy(this.rawAttributes, (_a, key) => this._virtualAttributes.has(key));\n\n    this.prototype._hasCustomGetters = Object.keys(this.prototype._customGetters).length;\n    this.prototype._hasCustomSetters = Object.keys(this.prototype._customSetters).length;\n\n    for (const key of Object.keys(attributeManipulation)) {\n      if (Object.prototype.hasOwnProperty.call(Model.prototype, key)) {\n        this.sequelize.log(`Not overriding built-in method from model attribute: ${key}`);\n        continue;\n      }\n      Object.defineProperty(this.prototype, key, attributeManipulation[key]);\n    }\n\n    this.prototype.rawAttributes = this.rawAttributes;\n    this.prototype._isAttribute = key => Object.prototype.hasOwnProperty.call(this.prototype.rawAttributes, key);\n\n    // Primary key convenience constiables\n    this.primaryKeyAttributes = Object.keys(this.primaryKeys);\n    this.primaryKeyAttribute = this.primaryKeyAttributes[0];\n    if (this.primaryKeyAttribute) {\n      this.primaryKeyField = this.rawAttributes[this.primaryKeyAttribute].field || this.primaryKeyAttribute;\n    }\n\n    this._hasPrimaryKeys = this.primaryKeyAttributes.length > 0;\n    this._isPrimaryKey = key => this.primaryKeyAttributes.includes(key);\n\n    this._attributeManipulation = attributeManipulation;\n  }\n\n  /**\n   * Remove attribute from model definition\n   *\n   * @param {string} attribute name of attribute to remove\n   */\n  static removeAttribute(attribute) {\n    delete this.rawAttributes[attribute];\n    this.refreshAttributes();\n  }\n\n  /**\n   * Sync this Model to the DB, that is create the table.\n   *\n   * @param {object} [options] sync options\n   *\n   * @see\n   * {@link Sequelize#sync} for options\n   *\n   * @returns {Promise<Model>}\n   */\n  static async sync(options) {\n    options = { ...this.options, ...options };\n    options.hooks = options.hooks === undefined ? true : !!options.hooks;\n\n    const attributes = this.tableAttributes;\n    const rawAttributes = this.fieldRawAttributesMap;\n\n    if (options.hooks) {\n      await this.runHooks('beforeSync', options);\n    }\n\n    const tableName = this.getTableName(options);\n\n    let tableExists;\n    if (options.force) {\n      await this.drop(options);\n      tableExists = false;\n    } else {\n      tableExists = await this.queryInterface.tableExists(tableName, options);\n    }\n\n    if (!tableExists) {\n      await this.queryInterface.createTable(tableName, attributes, options, this);\n    } else {\n      // enums are always updated, even if alter is not set. createTable calls it too.\n      await this.queryInterface.ensureEnums(tableName, attributes, options, this);\n    }\n\n    if (tableExists && options.alter) {\n      const tableInfos = await Promise.all([\n        this.queryInterface.describeTable(tableName, options),\n        this.queryInterface.getForeignKeyReferencesForTable(tableName, options)\n      ]);\n\n      const columns = tableInfos[0];\n      // Use for alter foreign keys\n      const foreignKeyReferences = tableInfos[1];\n      const removedConstraints = {};\n\n      for (const columnName in attributes) {\n        if (!Object.prototype.hasOwnProperty.call(attributes, columnName)) continue;\n        if (!columns[columnName] && !columns[attributes[columnName].field]) {\n          await this.queryInterface.addColumn(tableName, attributes[columnName].field || columnName, attributes[columnName], options);\n        }\n      }\n\n      if (options.alter === true || typeof options.alter === 'object' && options.alter.drop !== false) {\n        for (const columnName in columns) {\n          if (!Object.prototype.hasOwnProperty.call(columns, columnName)) continue;\n          const currentAttribute = rawAttributes[columnName];\n          if (!currentAttribute) {\n            await this.queryInterface.removeColumn(tableName, columnName, options);\n            continue;\n          }\n          if (currentAttribute.primaryKey) continue;\n          // Check foreign keys. If it's a foreign key, it should remove constraint first.\n          const references = currentAttribute.references;\n          if (currentAttribute.references) {\n            const database = this.sequelize.config.database;\n            const schema = this.sequelize.config.schema;\n            // Find existed foreign keys\n            for (const foreignKeyReference of foreignKeyReferences) {\n              const constraintName = foreignKeyReference.constraintName;\n              if (!!constraintName\n                && foreignKeyReference.tableCatalog === database\n                && (schema ? foreignKeyReference.tableSchema === schema : true)\n                && foreignKeyReference.referencedTableName === references.model\n                && foreignKeyReference.referencedColumnName === references.key\n                && (schema ? foreignKeyReference.referencedTableSchema === schema : true)\n                && !removedConstraints[constraintName]) {\n                // Remove constraint on foreign keys.\n                await this.queryInterface.removeConstraint(tableName, constraintName, options);\n                removedConstraints[constraintName] = true;\n              }\n            }\n          }\n\n          await this.queryInterface.changeColumn(tableName, columnName, currentAttribute, options);\n        }\n      }\n    }\n\n    const existingIndexes = await this.queryInterface.showIndex(tableName, options);\n    const missingIndexes = this._indexes.filter(item1 =>\n      !existingIndexes.some(item2 => item1.name === item2.name)\n    ).sort((index1, index2) => {\n      if (this.sequelize.options.dialect === 'postgres') {\n      // move concurrent indexes to the bottom to avoid weird deadlocks\n        if (index1.concurrently === true) return 1;\n        if (index2.concurrently === true) return -1;\n      }\n\n      return 0;\n    });\n\n    for (const index of missingIndexes) {\n      await this.queryInterface.addIndex(tableName, { ...options, ...index });\n    }\n\n    if (options.hooks) {\n      await this.runHooks('afterSync', options);\n    }\n\n    return this;\n  }\n\n  /**\n   * Drop the table represented by this Model\n   *\n   * @param {object}   [options] drop options\n   * @param {boolean}  [options.cascade=false]   Also drop all objects depending on this table, such as views. Only works in postgres\n   * @param {Function} [options.logging=false]   A function that gets executed while running the query to log the sql.\n   * @param {boolean}  [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   *\n   * @returns {Promise}\n   */\n  static async drop(options) {\n    return await this.queryInterface.dropTable(this.getTableName(options), options);\n  }\n\n  static async dropSchema(schema) {\n    return await this.queryInterface.dropSchema(schema);\n  }\n\n  /**\n   * Apply a schema to this model. For postgres, this will actually place the schema in front of the table name - `\"schema\".\"tableName\"`,\n   * while the schema will be prepended to the table name for mysql and sqlite - `'schema.tablename'`.\n   *\n   * This method is intended for use cases where the same model is needed in multiple schemas. In such a use case it is important\n   * to call `model.schema(schema, [options]).sync()` for each model to ensure the models are created in the correct schema.\n   *\n   * If a single default schema per model is needed, set the `options.schema='schema'` parameter during the `define()` call\n   * for the model.\n   *\n   * @param {string}   schema The name of the schema\n   * @param {object}   [options] schema options\n   * @param {string}   [options.schemaDelimiter='.'] The character(s) that separates the schema name from the table name\n   * @param {Function} [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {boolean}  [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   *\n   * @see\n   * {@link Sequelize#define} for more information about setting a default schema.\n   *\n   * @returns {Model}\n   */\n  static schema(schema, options) {\n\n    const clone = class extends this {};\n    Object.defineProperty(clone, 'name', { value: this.name });\n\n    clone._schema = schema;\n\n    if (options) {\n      if (typeof options === 'string') {\n        clone._schemaDelimiter = options;\n      } else if (options.schemaDelimiter) {\n        clone._schemaDelimiter = options.schemaDelimiter;\n      }\n    }\n\n    return clone;\n  }\n\n  /**\n   * Get the table name of the model, taking schema into account. The method will return The name as a string if the model has no schema,\n   * or an object with `tableName`, `schema` and `delimiter` properties.\n   *\n   * @returns {string|object}\n   */\n  static getTableName() {\n    return this.queryGenerator.addSchema(this);\n  }\n\n  /**\n   * Get un-scoped model\n   *\n   * @returns {Model}\n   */\n  static unscoped() {\n    return this.scope();\n  }\n\n  /**\n   * Add a new scope to the model. This is especially useful for adding scopes with includes, when the model you want to include is not available at the time this model is defined.\n   *\n   * By default this will throw an error if a scope with that name already exists. Pass `override: true` in the options object to silence this error.\n   *\n   * @param {string}          name The name of the scope. Use `defaultScope` to override the default scope\n   * @param {object|Function} scope scope or options\n   * @param {object}          [options] scope options\n   * @param {boolean}         [options.override=false] override old scope if already defined\n   */\n  static addScope(name, scope, options) {\n    options = { override: false, ...options };\n\n    if ((name === 'defaultScope' && Object.keys(this.options.defaultScope).length > 0 || name in this.options.scopes) && options.override === false) {\n      throw new Error(`The scope ${name} already exists. Pass { override: true } as options to silence this error`);\n    }\n\n    if (name === 'defaultScope') {\n      this.options.defaultScope = this._scope = scope;\n    } else {\n      this.options.scopes[name] = scope;\n    }\n  }\n\n  /**\n   * Apply a scope created in `define` to the model.\n   *\n   * @example <caption>how to create scopes</caption>\n   * const Model = sequelize.define('model', attributes, {\n   *   defaultScope: {\n   *     where: {\n   *       username: 'dan'\n   *     },\n   *     limit: 12\n   *   },\n   *   scopes: {\n   *     isALie: {\n   *       where: {\n   *         stuff: 'cake'\n   *       }\n   *     },\n   *     complexFunction: function(email, accessLevel) {\n   *       return {\n   *         where: {\n   *           email: {\n   *             [Op.like]: email\n   *           },\n   *           access_level {\n   *             [Op.gte]: accessLevel\n   *           }\n   *         }\n   *       }\n   *     }\n   *   }\n   * })\n   *\n   * # As you have defined a default scope, every time you do Model.find, the default scope is appended to your query. Here's a couple of examples:\n   *\n   * Model.findAll() // WHERE username = 'dan'\n   * Model.findAll({ where: { age: { [Op.gt]: 12 } } }) // WHERE age > 12 AND username = 'dan'\n   *\n   * @example <caption>To invoke scope functions you can do</caption>\n   * Model.scope({ method: ['complexFunction', '<EMAIL>', 42]}).findAll()\n   * // WHERE email like '<EMAIL>%' AND access_level >= 42\n   *\n   * @param {?Array|object|string} [option] The scope(s) to apply. Scopes can either be passed as consecutive arguments, or as an array of arguments. To apply simple scopes and scope functions with no arguments, pass them as strings. For scope function, pass an object, with a `method` property. The value can either be a string, if the method does not take any arguments, or an array, where the first element is the name of the method, and consecutive elements are arguments to that method. Pass null to remove all scopes, including the default.\n   *\n   * @returns {Model} A reference to the model, with the scope(s) applied. Calling scope again on the returned model will clear the previous scope.\n   */\n  static scope(option) {\n    const self = class extends this {};\n    let scope;\n    let scopeName;\n\n    Object.defineProperty(self, 'name', { value: this.name });\n\n    self._scope = {};\n    self._scopeNames = [];\n    self.scoped = true;\n\n    if (!option) {\n      return self;\n    }\n\n    const options = _.flatten(arguments);\n\n    for (const option of options) {\n      scope = null;\n      scopeName = null;\n\n      if (_.isPlainObject(option)) {\n        if (option.method) {\n          if (Array.isArray(option.method) && !!self.options.scopes[option.method[0]]) {\n            scopeName = option.method[0];\n            scope = self.options.scopes[scopeName].apply(self, option.method.slice(1));\n          }\n          else if (self.options.scopes[option.method]) {\n            scopeName = option.method;\n            scope = self.options.scopes[scopeName].apply(self);\n          }\n        } else {\n          scope = option;\n        }\n      } else if (option === 'defaultScope' && _.isPlainObject(self.options.defaultScope)) {\n        scope = self.options.defaultScope;\n      } else {\n        scopeName = option;\n        scope = self.options.scopes[scopeName];\n        if (typeof scope === 'function') {\n          scope = scope();\n        }\n      }\n\n      if (scope) {\n        this._conformIncludes(scope, this);\n        // clone scope so it doesn't get modified\n        this._assignOptions(self._scope, Utils.cloneDeep(scope));\n        self._scopeNames.push(scopeName ? scopeName : 'defaultScope');\n      } else {\n        throw new sequelizeErrors.SequelizeScopeError(`Invalid scope ${scopeName} called.`);\n      }\n    }\n\n    return self;\n  }\n\n  /**\n   * Search for multiple instances.\n   *\n   * @example <caption>Simple search using AND and =</caption>\n   * Model.findAll({\n   *   where: {\n   *     attr1: 42,\n   *     attr2: 'cake'\n   *   }\n   * })\n   *\n   * # WHERE attr1 = 42 AND attr2 = 'cake'\n   *\n   * @example <caption>Using greater than, less than etc.</caption>\n   * const {gt, lte, ne, in: opIn} = Sequelize.Op;\n   *\n   * Model.findAll({\n   *   where: {\n   *     attr1: {\n   *       [gt]: 50\n   *     },\n   *     attr2: {\n   *       [lte]: 45\n   *     },\n   *     attr3: {\n   *       [opIn]: [1,2,3]\n   *     },\n   *     attr4: {\n   *       [ne]: 5\n   *     }\n   *   }\n   * })\n   *\n   * # WHERE attr1 > 50 AND attr2 <= 45 AND attr3 IN (1,2,3) AND attr4 != 5\n   *\n   * @example <caption>Queries using OR</caption>\n   * const {or, and, gt, lt} = Sequelize.Op;\n   *\n   * Model.findAll({\n   *   where: {\n   *     name: 'a project',\n   *     [or]: [\n   *       {id: [1, 2, 3]},\n   *       {\n   *         [and]: [\n   *           {id: {[gt]: 10}},\n   *           {id: {[lt]: 100}}\n   *         ]\n   *       }\n   *     ]\n   *   }\n   * });\n   *\n   * # WHERE `Model`.`name` = 'a project' AND (`Model`.`id` IN (1, 2, 3) OR (`Model`.`id` > 10 AND `Model`.`id` < 100));\n   *\n   * @see\n   * {@link Operators} for possible operators\n   * __Alias__: _all_\n   *\n   * The promise is resolved with an array of Model instances if the query succeeds._\n   *\n   * @param  {object}                                                    [options] A hash of options to describe the scope of the search\n   * @param  {object}                                                    [options.where] A hash of attributes to describe your search. See above for examples.\n   * @param  {Array<string>|object}                                      [options.attributes] A list of the attributes that you want to select, or an object with `include` and `exclude` keys. To rename an attribute, you can pass an array, with two elements - the first is the name of the attribute in the DB (or some kind of expression such as `Sequelize.literal`, `Sequelize.fn` and so on), and the second is the name you want the attribute to have in the returned instance\n   * @param  {Array<string>}                                             [options.attributes.include] Select all the attributes of the model, plus some additional ones. Useful for aggregations, e.g. `{ attributes: { include: [[sequelize.fn('COUNT', sequelize.col('id')), 'total']] }`\n   * @param  {Array<string>}                                             [options.attributes.exclude] Select all the attributes of the model, except some few. Useful for security purposes e.g. `{ attributes: { exclude: ['password'] } }`\n   * @param  {boolean}                                                   [options.paranoid=true] If true, only non-deleted records will be returned. If false, both deleted and non-deleted records will be returned. Only applies if `options.paranoid` is true for the model.\n   * @param  {Array<object|Model|string>}                                [options.include] A list of associations to eagerly load using a left join. Supported is either `{ include: [ Model1, Model2, ...]}` or `{ include: [{ model: Model1, as: 'Alias' }]}` or `{ include: ['Alias']}`. If your association are set up with an `as` (eg. `X.hasMany(Y, { as: 'Z' }`, you need to specify Z in the as attribute when eager loading Y).\n   * @param  {Model}                                                     [options.include[].model] The model you want to eagerly load\n   * @param  {string}                                                    [options.include[].as] The alias of the relation, in case the model you want to eagerly load is aliased. For `hasOne` / `belongsTo`, this should be the singular name, and for `hasMany`, it should be the plural\n   * @param  {Association}                                               [options.include[].association] The association you want to eagerly load. (This can be used instead of providing a model/as pair)\n   * @param  {object}                                                    [options.include[].where] Where clauses to apply to the child models. Note that this converts the eager load to an inner join, unless you explicitly set `required: false`\n   * @param  {boolean}                                                   [options.include[].or=false] Whether to bind the ON and WHERE clause together by OR instead of AND.\n   * @param  {object}                                                    [options.include[].on] Supply your own ON condition for the join.\n   * @param  {Array<string>}                                             [options.include[].attributes] A list of attributes to select from the child model\n   * @param  {boolean}                                                   [options.include[].required] If true, converts to an inner join, which means that the parent model will only be loaded if it has any matching children. True if `include.where` is set, false otherwise.\n   * @param  {boolean}                                                   [options.include[].right] If true, converts to a right join if dialect support it. Ignored if `include.required` is true.\n   * @param  {boolean}                                                   [options.include[].separate] If true, runs a separate query to fetch the associated instances, only supported for hasMany associations\n   * @param  {number}                                                    [options.include[].limit] Limit the joined rows, only supported with include.separate=true\n   * @param  {string}                                                    [options.include[].through.as] The alias for the join model, in case you want to give it a different name than the default one.\n   * @param  {boolean}                                                   [options.include[].through.paranoid] If true, only non-deleted records will be returned from the join table. If false, both deleted and non-deleted records will be returned. Only applies if through model is paranoid.\n   * @param  {object}                                                    [options.include[].through.where] Filter on the join model for belongsToMany relations\n   * @param  {Array}                                                     [options.include[].through.attributes] A list of attributes to select from the join model for belongsToMany relations\n   * @param  {Array<object|Model|string>}                                [options.include[].include] Load further nested related models\n   * @param  {boolean}                                                   [options.include[].duplicating] Mark the include as duplicating, will prevent a subquery from being used.\n   * @param  {Array|Sequelize.fn|Sequelize.col|Sequelize.literal}        [options.order] Specifies an ordering. Using an array, you can provide several columns / functions to order by. Each element can be further wrapped in a two-element array. The first element is the column / function to order by, the second is the direction. For example: `order: [['name', 'DESC']]`. In this way the column will be escaped, but the direction will not.\n   * @param  {number}                                                    [options.limit] Limit for result\n   * @param  {number}                                                    [options.offset] Offset for result\n   * @param  {Transaction}                                               [options.transaction] Transaction to run query under\n   * @param  {string|object}                                             [options.lock] Lock the selected rows. Possible options are transaction.LOCK.UPDATE and transaction.LOCK.SHARE. Postgres also supports transaction.LOCK.KEY_SHARE, transaction.LOCK.NO_KEY_UPDATE and specific model locks with joins.\n   * @param  {boolean}                                                   [options.skipLocked] Skip locked rows. Only supported in Postgres.\n   * @param  {boolean}                                                   [options.raw] Return raw result. See sequelize.query for more information.\n   * @param  {Function}                                                  [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param  {boolean}                                                   [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {object}                                                    [options.having] Having options\n   * @param  {string}                                                    [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   * @param  {boolean|Error}                                             [options.rejectOnEmpty=false] Throws an error when no records found\n   * @param  {boolean}                                                   [options.dotNotation] Allows including tables having the same attribute/column names - which have a dot in them.\n   * @param  {boolean}                                                   [options.nest=false] If true, transforms objects with `.` separated property names into nested objects.\n   *\n   * @see\n   * {@link Sequelize#query}\n   *\n   * @returns {Promise<Array<Model>>}\n   */\n  static async findAll(options) {\n    if (options !== undefined && !_.isPlainObject(options)) {\n      throw new sequelizeErrors.QueryError('The argument passed to findAll must be an options object, use findByPk if you wish to pass a single primary key value');\n    }\n\n    if (options !== undefined && options.attributes) {\n      if (!Array.isArray(options.attributes) && !_.isPlainObject(options.attributes)) {\n        throw new sequelizeErrors.QueryError('The attributes option must be an array of column names or an object');\n      }\n    }\n\n    this.warnOnInvalidOptions(options, Object.keys(this.rawAttributes));\n\n    const tableNames = {};\n\n    tableNames[this.getTableName(options)] = true;\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    _.defaults(options, { hooks: true });\n\n    // set rejectOnEmpty option, defaults to model options\n    options.rejectOnEmpty = Object.prototype.hasOwnProperty.call(options, 'rejectOnEmpty')\n      ? options.rejectOnEmpty\n      : this.options.rejectOnEmpty;\n\n    this._injectScope(options);\n\n    if (options.hooks) {\n      await this.runHooks('beforeFind', options);\n    }\n    this._conformIncludes(options, this);\n    this._expandAttributes(options);\n    this._expandIncludeAll(options);\n\n    if (options.hooks) {\n      await this.runHooks('beforeFindAfterExpandIncludeAll', options);\n    }\n    options.originalAttributes = this._injectDependentVirtualAttributes(options.attributes);\n\n    if (options.include) {\n      options.hasJoin = true;\n\n      this._validateIncludedElements(options, tableNames);\n\n      // If we're not raw, we have to make sure we include the primary key for de-duplication\n      if (\n        options.attributes\n        && !options.raw\n        && this.primaryKeyAttribute\n        && !options.attributes.includes(this.primaryKeyAttribute)\n        && (!options.group || !options.hasSingleAssociation || options.hasMultiAssociation)\n      ) {\n        options.attributes = [this.primaryKeyAttribute].concat(options.attributes);\n      }\n    }\n\n    if (!options.attributes) {\n      options.attributes = Object.keys(this.rawAttributes);\n      options.originalAttributes = this._injectDependentVirtualAttributes(options.attributes);\n    }\n\n    // whereCollection is used for non-primary key updates\n    this.options.whereCollection = options.where || null;\n\n    Utils.mapFinderOptions(options, this);\n\n    options = this._paranoidClause(this, options);\n\n    if (options.hooks) {\n      await this.runHooks('beforeFindAfterOptions', options);\n    }\n    const selectOptions = { ...options, tableNames: Object.keys(tableNames) };\n    const results = await this.queryInterface.select(this, this.getTableName(selectOptions), selectOptions);\n    if (options.hooks) {\n      await this.runHooks('afterFind', results, options);\n    }\n\n    //rejectOnEmpty mode\n    if (_.isEmpty(results) && options.rejectOnEmpty) {\n      if (typeof options.rejectOnEmpty === 'function') {\n        throw new options.rejectOnEmpty();\n      }\n      if (typeof options.rejectOnEmpty === 'object') {\n        throw options.rejectOnEmpty;\n      }\n      throw new sequelizeErrors.EmptyResultError();\n    }\n\n    return await Model._findSeparate(results, options);\n  }\n\n  static warnOnInvalidOptions(options, validColumnNames) {\n    if (!_.isPlainObject(options)) {\n      return;\n    }\n\n    const unrecognizedOptions = Object.keys(options).filter(k => !validQueryKeywords.has(k));\n    const unexpectedModelAttributes = _.intersection(unrecognizedOptions, validColumnNames);\n    if (!options.where && unexpectedModelAttributes.length > 0) {\n      logger.warn(`Model attributes (${unexpectedModelAttributes.join(', ')}) passed into finder method options of model ${this.name}, but the options.where object is empty. Did you forget to use options.where?`);\n    }\n  }\n\n  static _injectDependentVirtualAttributes(attributes) {\n    if (!this._hasVirtualAttributes) return attributes;\n    if (!attributes || !Array.isArray(attributes)) return attributes;\n\n    for (const attribute of attributes) {\n      if (\n        this._virtualAttributes.has(attribute)\n        && this.rawAttributes[attribute].type.fields\n      ) {\n        attributes = attributes.concat(this.rawAttributes[attribute].type.fields);\n      }\n    }\n\n    attributes = _.uniq(attributes);\n\n    return attributes;\n  }\n\n  static async _findSeparate(results, options) {\n    if (!options.include || options.raw || !results) return results;\n\n    const original = results;\n    if (options.plain) results = [results];\n\n    if (!results.length) return original;\n\n    await Promise.all(options.include.map(async include => {\n      if (!include.separate) {\n        return await Model._findSeparate(\n          results.reduce((memo, result) => {\n            let associations = result.get(include.association.as);\n\n            // Might be an empty belongsTo relation\n            if (!associations) return memo;\n\n            // Force array so we can concat no matter if it's 1:1 or :M\n            if (!Array.isArray(associations)) associations = [associations];\n\n            for (let i = 0, len = associations.length; i !== len; ++i) {\n              memo.push(associations[i]);\n            }\n            return memo;\n          }, []),\n          {\n\n            ..._.omit(options, 'include', 'attributes', 'order', 'where', 'limit', 'offset', 'plain', 'scope'),\n            include: include.include || []\n          }\n        );\n      }\n\n      const map = await include.association.get(results, {\n\n        ..._.omit(options, nonCascadingOptions),\n        ..._.omit(include, ['parent', 'association', 'as', 'originalAttributes'])\n      });\n\n      for (const result of results) {\n        result.set(\n          include.association.as,\n          map[result.get(include.association.sourceKey)],\n          { raw: true }\n        );\n      }\n    }));\n\n    return original;\n  }\n\n  /**\n   * Search for a single instance by its primary key._\n   *\n   * @param  {number|bigint|string|Buffer}      param The value of the desired instance's primary key.\n   * @param  {object}                           [options] find options\n   * @param  {Transaction}                      [options.transaction] Transaction to run query under\n   * @param  {string}                           [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @see\n   * {@link Model.findAll}           for a full explanation of options, Note that options.where is not supported.\n   *\n   * @returns {Promise<Model>}\n   */\n  static async findByPk(param, options) {\n    // return Promise resolved with null if no arguments are passed\n    if ([null, undefined].includes(param)) {\n      return null;\n    }\n\n    options = Utils.cloneDeep(options) || {};\n\n    if (typeof param === 'number' || typeof param === 'bigint' || typeof param === 'string' || Buffer.isBuffer(param)) {\n      options.where = {\n        [this.primaryKeyAttribute]: param\n      };\n    } else {\n      throw new Error(`Argument passed to findByPk is invalid: ${param}`);\n    }\n\n    // Bypass a possible overloaded findOne\n    // note: in v6, we don't bypass overload https://github.com/sequelize/sequelize/issues/14003\n    return await this.findOne(options);\n  }\n\n  /**\n   * Search for a single instance. Returns the first instance found, or null if none can be found.\n   *\n   * @param  {object}       [options] A hash of options to describe the scope of the search\n   * @param  {Transaction}  [options.transaction] Transaction to run query under\n   * @param  {string}       [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @see\n   * {@link Model.findAll} for an explanation of options\n   *\n   * @returns {Promise<Model|null>}\n   */\n  static async findOne(options) {\n    if (options !== undefined && !_.isPlainObject(options)) {\n      throw new Error('The argument passed to findOne must be an options object, use findByPk if you wish to pass a single primary key value');\n    }\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    if (options.limit === undefined) {\n      const uniqueSingleColumns = _.chain(this.uniqueKeys).values().filter(c => c.fields.length === 1).map('column').value();\n\n      // Don't add limit if querying directly on the pk or a unique column\n      if (!options.where || !_.some(options.where, (value, key) =>\n        (key === this.primaryKeyAttribute || uniqueSingleColumns.includes(key)) &&\n          (Utils.isPrimitive(value) || Buffer.isBuffer(value))\n      )) {\n        options.limit = 1;\n      }\n    }\n\n    // Bypass a possible overloaded findAll.\n    // note: in v6, we don't bypass overload https://github.com/sequelize/sequelize/issues/14003\n    return await this.findAll(_.defaults(options, {\n      plain: true\n    }));\n  }\n\n  /**\n   * Run an aggregation method on the specified field\n   *\n   * @param {string}          attribute The attribute to aggregate over. Can be a field name or *\n   * @param {string}          aggregateFunction The function to use for aggregation, e.g. sum, max etc.\n   * @param {object}          [options] Query options. See sequelize.query for full options\n   * @param {object}          [options.where] A hash of search attributes.\n   * @param {Function}        [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {boolean}         [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param {DataTypes|string} [options.dataType] The type of the result. If `field` is a field in this Model, the default will be the type of that field, otherwise defaults to float.\n   * @param {boolean}         [options.distinct] Applies DISTINCT to the field being aggregated over\n   * @param {Transaction}     [options.transaction] Transaction to run query under\n   * @param {boolean}         [options.plain] When `true`, the first returned value of `aggregateFunction` is cast to `dataType` and returned. If additional attributes are specified, along with `group` clauses, set `plain` to `false` to return all values of all returned rows.  Defaults to `true`\n   *\n   * @returns {Promise<DataTypes|object>} Returns the aggregate result cast to `options.dataType`, unless `options.plain` is false, in which case the complete data result is returned.\n   */\n  static async aggregate(attribute, aggregateFunction, options) {\n    options = Utils.cloneDeep(options);\n\n    // We need to preserve attributes here as the `injectScope` call would inject non aggregate columns.\n    const prevAttributes = options.attributes;\n    this._injectScope(options);\n    options.attributes = prevAttributes;\n    this._conformIncludes(options, this);\n\n    if (options.include) {\n      this._expandIncludeAll(options);\n      this._validateIncludedElements(options);\n    }\n\n    const attrOptions = this.rawAttributes[attribute];\n    const field = attrOptions && attrOptions.field || attribute;\n    let aggregateColumn = this.sequelize.col(field);\n\n    if (options.distinct) {\n      aggregateColumn = this.sequelize.fn('DISTINCT', aggregateColumn);\n    }\n\n    let { group } = options;\n    if (Array.isArray(group) && Array.isArray(group[0])) {\n      noDoubleNestedGroup();\n      group = _.flatten(group);\n    }\n    options.attributes = _.unionBy(\n      options.attributes,\n      group,\n      [[this.sequelize.fn(aggregateFunction, aggregateColumn), aggregateFunction]],\n      a => Array.isArray(a) ? a[1] : a\n    );\n\n    if (!options.dataType) {\n      if (attrOptions) {\n        options.dataType = attrOptions.type;\n      } else {\n        // Use FLOAT as fallback\n        options.dataType = new DataTypes.FLOAT();\n      }\n    } else {\n      options.dataType = this.sequelize.normalizeDataType(options.dataType);\n    }\n\n    Utils.mapOptionFieldNames(options, this);\n    options = this._paranoidClause(this, options);\n\n    const value = await this.queryInterface.rawSelect(this.getTableName(options), options, aggregateFunction, this);\n    return value;\n  }\n\n  /**\n   * Count the number of records matching the provided where clause.\n   *\n   * If you provide an `include` option, the number of matching associations will be counted instead.\n   *\n   * @param {object}        [options] options\n   * @param {object}        [options.where] A hash of search attributes.\n   * @param {object}        [options.include] Include options. See `find` for details\n   * @param {boolean}       [options.paranoid=true] Set `true` to count only non-deleted records. Can be used on models with `paranoid` enabled\n   * @param {boolean}       [options.distinct] Apply COUNT(DISTINCT(col)) on primary key or on options.col.\n   * @param {string}        [options.col] Column on which COUNT() should be applied\n   * @param {Array}         [options.attributes] Used in conjunction with `group`\n   * @param {Array}         [options.group] For creating complex counts. Will return multiple rows as needed.\n   * @param {Transaction}   [options.transaction] Transaction to run query under\n   * @param {Function}      [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {boolean}       [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param {string}        [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @returns {Promise<number>}\n   */\n  static async count(options) {\n    options = Utils.cloneDeep(options);\n    options = _.defaults(options, { hooks: true });\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    options.raw = true;\n    if (options.hooks) {\n      await this.runHooks('beforeCount', options);\n    }\n    let col = options.col || '*';\n    if (options.include) {\n      col = `${this.name}.${options.col || this.primaryKeyField}`;\n    }\n    if (options.distinct && col === '*') {\n      col = this.primaryKeyField;\n    }\n    options.plain = !options.group;\n    options.dataType = new DataTypes.INTEGER();\n    options.includeIgnoreAttributes = false;\n\n    // No limit, offset or order for the options max be given to count()\n    // Set them to null to prevent scopes setting those values\n    options.limit = null;\n    options.offset = null;\n    options.order = null;\n\n    const result = await this.aggregate(col, 'count', options);\n\n    // When grouping is used, some dialects such as PG are returning the count as string\n    // --> Manually convert it to number\n    if (Array.isArray(result)) {\n      return result.map(item => ({\n        ...item,\n        count: Number(item.count)\n      }));\n    }\n\n    return result;\n  }\n\n  /**\n   * Find all the rows matching your query, within a specified offset / limit, and get the total number of rows matching your query. This is very useful for paging\n   *\n   * @example\n   * const result = await Model.findAndCountAll({\n   *   where: ...,\n   *   limit: 12,\n   *   offset: 12\n   * });\n   *\n   * # In the above example, `result.rows` will contain rows 13 through 24, while `result.count` will return the total number of rows that matched your query.\n   *\n   * # When you add includes, only those which are required (either because they have a where clause, or because `required` is explicitly set to true on the include) will be added to the count part.\n   *\n   * # Suppose you want to find all users who have a profile attached:\n   *\n   * User.findAndCountAll({\n   *   include: [\n   *      { model: Profile, required: true}\n   *   ],\n   *   limit: 3\n   * });\n   *\n   * # Because the include for `Profile` has `required` set it will result in an inner join, and only the users who have a profile will be counted. If we remove `required` from the include, both users with and without profiles will be counted\n   *\n   * @param {object} [options] See findAll options\n   *\n   * @see\n   * {@link Model.findAll} for a specification of find and query options\n   * @see\n   * {@link Model.count} for a specification of count options\n   *\n   * @returns {Promise<{count: number | number[], rows: Model[]}>}\n   */\n  static async findAndCountAll(options) {\n    if (options !== undefined && !_.isPlainObject(options)) {\n      throw new Error('The argument passed to findAndCountAll must be an options object, use findByPk if you wish to pass a single primary key value');\n    }\n\n    const countOptions = Utils.cloneDeep(options);\n\n    if (countOptions.attributes) {\n      countOptions.attributes = undefined;\n    }\n\n    const [count, rows] = await Promise.all([\n      this.count(countOptions),\n      this.findAll(options)\n    ]);\n\n    return {\n      count,\n      rows: count === 0 ? [] : rows\n    };\n  }\n\n  /**\n   * Find the maximum value of field\n   *\n   * @param {string} field attribute / field name\n   * @param {object} [options] See aggregate\n   *\n   * @see\n   * {@link Model.aggregate} for options\n   *\n   * @returns {Promise<*>}\n   */\n  static async max(field, options) {\n    return await this.aggregate(field, 'max', options);\n  }\n\n  /**\n   * Find the minimum value of field\n   *\n   * @param {string} field attribute / field name\n   * @param {object} [options] See aggregate\n   *\n   * @see\n   * {@link Model.aggregate} for options\n   *\n   * @returns {Promise<*>}\n   */\n  static async min(field, options) {\n    return await this.aggregate(field, 'min', options);\n  }\n\n  /**\n   * Find the sum of field\n   *\n   * @param {string} field attribute / field name\n   * @param {object} [options] See aggregate\n   *\n   * @see\n   * {@link Model.aggregate} for options\n   *\n   * @returns {Promise<number>}\n   */\n  static async sum(field, options) {\n    return await this.aggregate(field, 'sum', options);\n  }\n\n  /**\n   * Builds a new model instance.\n   *\n   * @param {object|Array} values An object of key value pairs or an array of such. If an array, the function will return an array of instances.\n   * @param {object}  [options] Instance build options\n   * @param {boolean} [options.raw=false] If set to true, values will ignore field and virtual setters.\n   * @param {boolean} [options.isNewRecord=true] Is this new record\n   * @param {Array}   [options.include] an array of include options - Used to build prefetched/included model instances. See `set`\n   *\n   * @returns {Model|Array<Model>}\n   */\n  static build(values, options) {\n    if (Array.isArray(values)) {\n      return this.bulkBuild(values, options);\n    }\n\n    return new this(values, options);\n  }\n\n  static bulkBuild(valueSets, options) {\n    options = { isNewRecord: true, ...options };\n\n    if (!options.includeValidated) {\n      this._conformIncludes(options, this);\n      if (options.include) {\n        this._expandIncludeAll(options);\n        this._validateIncludedElements(options);\n      }\n    }\n\n    if (options.attributes) {\n      options.attributes = options.attributes.map(attribute => Array.isArray(attribute) ? attribute[1] : attribute);\n    }\n\n    return valueSets.map(values => this.build(values, options));\n  }\n\n  /**\n   * Builds a new model instance and calls save on it.\n   *\n   * @see\n   * {@link Model.build}\n   * @see\n   * {@link Model.save}\n   *\n   * @param  {object}         values                       Hash of data values to create new record with\n   * @param  {object}         [options]                    Build and query options\n   * @param  {boolean}        [options.raw=false]          If set to true, values will ignore field and virtual setters.\n   * @param  {boolean}        [options.isNewRecord=true]   Is this new record\n   * @param  {Array}          [options.include]            An array of include options - Used to build prefetched/included model instances. See `set`\n   * @param  {string[]}       [options.fields]             An optional array of strings, representing database columns. If fields is provided, only those columns will be validated and saved.\n   * @param  {boolean}        [options.silent=false]       If true, the updatedAt timestamp will not be updated.\n   * @param  {boolean}        [options.validate=true]      If false, validations won't be run.\n   * @param  {boolean}        [options.hooks=true]         Run before and after create / update + validate hooks\n   * @param  {Function}       [options.logging=false]      A function that gets executed while running the query to log the sql.\n   * @param  {boolean}        [options.benchmark=false]    Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {Transaction}    [options.transaction]        Transaction to run query under\n   * @param  {string}         [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   * @param  {boolean|Array}  [options.returning=true]     Appends RETURNING <model columns> to get back all defined values; if an array of column names, append RETURNING <columns> to get back specific columns (Postgres only)\n   *\n   * @returns {Promise<Model>}\n   *\n   */\n  static async create(values, options) {\n    options = Utils.cloneDeep(options || {});\n\n    return await this.build(values, {\n      isNewRecord: true,\n      attributes: options.fields,\n      include: options.include,\n      raw: options.raw,\n      silent: options.silent\n    }).save(options);\n  }\n\n  /**\n   * Find a row that matches the query, or build (but don't save) the row if none is found.\n   * The successful result of the promise will be (instance, built)\n   *\n   * @param {object}   options find options\n   * @param {object}   options.where A hash of search attributes. If `where` is a plain object it will be appended with defaults to build a new instance.\n   * @param {object}   [options.defaults] Default values to use if building a new instance\n   * @param {object}   [options.transaction] Transaction to run query under\n   *\n   * @returns {Promise<Model,boolean>}\n   */\n  static async findOrBuild(options) {\n    if (!options || !options.where || arguments.length > 1) {\n      throw new Error(\n        'Missing where attribute in the options parameter passed to findOrBuild. ' +\n        'Please note that the API has changed, and is now options only (an object with where, defaults keys, transaction etc.)'\n      );\n    }\n\n    let values;\n\n    let instance = await this.findOne(options);\n    if (instance === null) {\n      values = { ...options.defaults };\n      if (_.isPlainObject(options.where)) {\n        values = Utils.defaults(values, options.where);\n      }\n\n      instance = this.build(values, options);\n\n      return [instance, true];\n    }\n\n    return [instance, false];\n  }\n\n  /**\n   * Find a row that matches the query, or build and save the row if none is found\n   * The successful result of the promise will be (instance, created)\n   *\n   * If no transaction is passed in the `options` object, a new transaction will be created internally, to prevent the race condition where a matching row is created by another connection after the find but before the insert call.\n   * However, it is not always possible to handle this case in SQLite, specifically if one transaction inserts and another tries to select before the first one has committed. In this case, an instance of sequelize. TimeoutError will be thrown instead.\n   * If a transaction is created, a savepoint will be created instead, and any unique constraint violation will be handled internally.\n   *\n   * @see\n   * {@link Model.findAll} for a full specification of find and options\n   *\n   * @param {object}      options find and create options\n   * @param {object}      options.where where A hash of search attributes. If `where` is a plain object it will be appended with defaults to build a new instance.\n   * @param {object}      [options.defaults] Default values to use if creating a new instance\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   *\n   * @returns {Promise<Model,boolean>}\n   */\n  static async findOrCreate(options) {\n    if (!options || !options.where || arguments.length > 1) {\n      throw new Error(\n        'Missing where attribute in the options parameter passed to findOrCreate. ' +\n        'Please note that the API has changed, and is now options only (an object with where, defaults keys, transaction etc.)'\n      );\n    }\n\n    options = { ...options };\n\n    if (options.defaults) {\n      const defaults = Object.keys(options.defaults);\n      const unknownDefaults = defaults.filter(name => !this.rawAttributes[name]);\n\n      if (unknownDefaults.length) {\n        logger.warn(`Unknown attributes (${unknownDefaults}) passed to defaults option of findOrCreate`);\n      }\n    }\n\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    const internalTransaction = !options.transaction;\n    let values;\n    let transaction;\n\n    try {\n      const t = await this.sequelize.transaction(options);\n      transaction = t;\n      options.transaction = t;\n\n      const found = await this.findOne(Utils.defaults({ transaction }, options));\n      if (found !== null) {\n        return [found, false];\n      }\n\n      values = { ...options.defaults };\n      if (_.isPlainObject(options.where)) {\n        values = Utils.defaults(values, options.where);\n      }\n\n      options.exception = true;\n      options.returning = true;\n\n      try {\n        const created = await this.create(values, options);\n        if (created.get(this.primaryKeyAttribute, { raw: true }) === null) {\n          // If the query returned an empty result for the primary key, we know that this was actually a unique constraint violation\n          throw new sequelizeErrors.UniqueConstraintError();\n        }\n\n        return [created, true];\n      } catch (err) {\n        if (!(err instanceof sequelizeErrors.UniqueConstraintError)) throw err;\n        const flattenedWhere = Utils.flattenObjectDeep(options.where);\n        const flattenedWhereKeys = Object.keys(flattenedWhere).map(name => _.last(name.split('.')));\n        const whereFields = flattenedWhereKeys.map(name => _.get(this.rawAttributes, `${name}.field`, name));\n        const defaultFields = options.defaults && Object.keys(options.defaults)\n          .filter(name => this.rawAttributes[name])\n          .map(name => this.rawAttributes[name].field || name);\n\n        const errFieldKeys = Object.keys(err.fields);\n        const errFieldsWhereIntersects = Utils.intersects(errFieldKeys, whereFields);\n        if (defaultFields && !errFieldsWhereIntersects && Utils.intersects(errFieldKeys, defaultFields)) {\n          throw err;\n        }\n\n        if (errFieldsWhereIntersects) {\n          _.each(err.fields, (value, key) => {\n            const name = this.fieldRawAttributesMap[key].fieldName;\n            if (value.toString() !== options.where[name].toString()) {\n              throw new Error(`${this.name}#findOrCreate: value used for ${name} was not equal for both the find and the create calls, '${options.where[name]}' vs '${value}'`);\n            }\n          });\n        }\n\n        // Someone must have created a matching instance inside the same transaction since we last did a find. Let's find it!\n        const otherCreated = await this.findOne(Utils.defaults({\n          transaction: internalTransaction ? null : transaction\n        }, options));\n\n        // Sanity check, ideally we caught this at the defaultFeilds/err.fields check\n        // But if we didn't and instance is null, we will throw\n        if (otherCreated === null) throw err;\n\n        return [otherCreated, false];\n      }\n    } finally {\n      if (internalTransaction && transaction) {\n        await transaction.commit();\n      }\n    }\n  }\n\n  /**\n   * A more performant findOrCreate that may not work under a transaction (working in postgres)\n   * Will execute a find call, if empty then attempt to create, if unique constraint then attempt to find again\n   *\n   * @see\n   * {@link Model.findAll} for a full specification of find and options\n   *\n   * @param {object} options find options\n   * @param {object} options.where A hash of search attributes. If `where` is a plain object it will be appended with defaults to build a new instance.\n   * @param {object} [options.defaults] Default values to use if creating a new instance\n   *\n   * @returns {Promise<Model,boolean>}\n   */\n  static async findCreateFind(options) {\n    if (!options || !options.where) {\n      throw new Error(\n        'Missing where attribute in the options parameter passed to findCreateFind.'\n      );\n    }\n\n    let values = { ...options.defaults };\n    if (_.isPlainObject(options.where)) {\n      values = Utils.defaults(values, options.where);\n    }\n\n\n    const found = await this.findOne(options);\n    if (found) return [found, false];\n\n    try {\n      const createOptions = { ...options };\n\n      // To avoid breaking a postgres transaction, run the create with `ignoreDuplicates`.\n      if (this.sequelize.options.dialect === 'postgres' && options.transaction) {\n        createOptions.ignoreDuplicates = true;\n      }\n\n      const created = await this.create(values, createOptions);\n      return [created, true];\n    } catch (err) {\n      if (!(err instanceof sequelizeErrors.UniqueConstraintError || err instanceof sequelizeErrors.EmptyResultError)) {\n        throw err;\n      }\n\n      const foundAgain = await this.findOne(options);\n      return [foundAgain, false];\n    }\n  }\n\n  /**\n   * Insert or update a single row. An update will be executed if a row which matches the supplied values on either the primary key or a unique key is found. Note that the unique index must be defined in your sequelize model and not just in the table. Otherwise you may experience a unique constraint violation, because sequelize fails to identify the row that should be updated.\n   *\n   * **Implementation details:**\n   *\n   * * MySQL - Implemented with ON DUPLICATE KEY UPDATE`\n   * * PostgreSQL - Implemented with ON CONFLICT DO UPDATE. If update data contains PK field, then PK is selected as the default conflict key. Otherwise first unique constraint/index will be selected, which can satisfy conflict key requirements.\n   * * SQLite - Implemented with ON CONFLICT DO UPDATE\n   * * MSSQL - Implemented as a single query using `MERGE` and `WHEN (NOT) MATCHED THEN`\n   *\n   * **Note** that Postgres/SQLite returns null for created, no matter if the row was created or updated\n   *\n   * @param  {object}        values                                        hash of values to upsert\n   * @param  {object}        [options]                                     upsert options\n   * @param  {boolean}       [options.validate=true]                       Run validations before the row is inserted\n   * @param  {Array}         [options.fields=Object.keys(this.attributes)] The fields to update if the record already exists. Defaults to all changed fields.  If none of the specified fields are present on the provided `values` object, an insert will still be attempted, but duplicate key conflicts will be ignored.\n   * @param  {boolean}       [options.hooks=true]                          Run before / after upsert hooks?\n   * @param  {boolean}       [options.returning=true]                      If true, fetches back auto generated values\n   * @param  {Transaction}   [options.transaction]                         Transaction to run query under\n   * @param  {Function}      [options.logging=false]                       A function that gets executed while running the query to log the sql.\n   * @param  {boolean}       [options.benchmark=false]                     Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {string}        [options.searchPath=DEFAULT]                  An optional parameter to specify the schema search_path (Postgres only)\n   * @param  {Array<string>} [options.conflictFields]                      Optional override for the conflict fields in the ON CONFLICT part of the query. Only supported in Postgres >= 9.5 and SQLite >= 3.24.0\n   *\n   * @returns {Promise<Array<Model, boolean | null>>} returns an array with two elements, the first being the new record and the second being `true` if it was just created or `false` if it already existed (except on Postgres and SQLite, which can't detect this and will always return `null` instead of a boolean).\n   */\n  static async upsert(values, options) {\n    options = {\n      hooks: true,\n      returning: true,\n      validate: true,\n      ...Utils.cloneDeep(options)\n    };\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    const createdAtAttr = this._timestampAttributes.createdAt;\n    const updatedAtAttr = this._timestampAttributes.updatedAt;\n    const hasPrimary = this.primaryKeyField in values || this.primaryKeyAttribute in values;\n    const instance = this.build(values);\n\n    options.model = this;\n    options.instance = instance;\n\n    const changed = Array.from(instance._changed);\n    if (!options.fields) {\n      options.fields = changed;\n    }\n\n    if (options.validate) {\n      await instance.validate(options);\n    }\n    // Map field names\n    const updatedDataValues = _.pick(instance.dataValues, changed);\n    const insertValues = Utils.mapValueFieldNames(instance.dataValues, Object.keys(instance.rawAttributes), this);\n    const updateValues = Utils.mapValueFieldNames(updatedDataValues, options.fields, this);\n    const now = Utils.now(this.sequelize.options.dialect);\n\n    // Attach createdAt\n    if (createdAtAttr && !insertValues[createdAtAttr]) {\n      const field = this.rawAttributes[createdAtAttr].field || createdAtAttr;\n      insertValues[field] = this._getDefaultTimestamp(createdAtAttr) || now;\n    }\n    if (updatedAtAttr && !insertValues[updatedAtAttr]) {\n      const field = this.rawAttributes[updatedAtAttr].field || updatedAtAttr;\n      insertValues[field] = updateValues[field] = this._getDefaultTimestamp(updatedAtAttr) || now;\n    }\n\n    // Db2 does not allow NULL values for unique columns.\n    // Add dummy values if not provided by test case or user.\n    if (this.sequelize.options.dialect === 'db2') {\n      this.uniqno = this.sequelize.dialect.queryGenerator.addUniqueFields(\n        insertValues, this.rawAttributes, this.uniqno);\n    }\n\n    // Build adds a null value for the primary key, if none was given by the user.\n    // We need to remove that because of some Postgres technicalities.\n    if (!hasPrimary && this.primaryKeyAttribute && !this.rawAttributes[this.primaryKeyAttribute].defaultValue) {\n      delete insertValues[this.primaryKeyField];\n      delete updateValues[this.primaryKeyField];\n    }\n\n    if (options.hooks) {\n      await this.runHooks('beforeUpsert', values, options);\n    }\n    const result = await this.queryInterface.upsert(this.getTableName(options), insertValues, updateValues, instance.where(), options);\n\n    const [record] = result;\n    record.isNewRecord = false;\n\n    if (options.hooks) {\n      await this.runHooks('afterUpsert', result, options);\n      return result;\n    }\n    return result;\n  }\n\n  /**\n   * Create and insert multiple instances in bulk.\n   *\n   * The success handler is passed an array of instances, but please notice that these may not completely represent the state of the rows in the DB. This is because MySQL\n   * and SQLite do not make it easy to obtain back automatically generated IDs and other default values in a way that can be mapped to multiple records.\n   * To obtain Instances for the newly created values, you will need to query for them again.\n   *\n   * If validation fails, the promise is rejected with an array-like AggregateError\n   *\n   * @param  {Array}          records                          List of objects (key/value pairs) to create instances from\n   * @param  {object}         [options]                        Bulk create options\n   * @param  {Array}          [options.fields]                 Fields to insert (defaults to all fields)\n   * @param  {boolean}        [options.validate=false]         Should each row be subject to validation before it is inserted. The whole insert will fail if one row fails validation\n   * @param  {boolean}        [options.hooks=true]             Run before / after bulk create hooks?\n   * @param  {boolean}        [options.individualHooks=false]  Run before / after create hooks for each individual Instance? BulkCreate hooks will still be run if options.hooks is true.\n   * @param  {boolean}        [options.ignoreDuplicates=false] Ignore duplicate values for primary keys? (not supported by MSSQL or Postgres < 9.5)\n   * @param  {Array}          [options.updateOnDuplicate]      Fields to update if row key already exists (on duplicate key update)? (only supported by MySQL, MariaDB, SQLite >= 3.24.0 & Postgres >= 9.5).\n   * @param  {Array}          [options.conflictAttributes]     Optional override for the conflict fields in the ON CONFLICT part of the query. Only supported in Postgres >= 9.5 and SQLite >= 3.24.0\n   * @param  {Transaction}    [options.transaction]            Transaction to run query under\n   * @param  {Function}       [options.logging=false]          A function that gets executed while running the query to log the sql.\n   * @param  {boolean}        [options.benchmark=false]        Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {boolean|Array}  [options.returning=false]        If true, append RETURNING <model columns> to get back all defined values; if an array of column names, append RETURNING <columns> to get back specific columns (Postgres only)\n   * @param  {string}         [options.searchPath=DEFAULT]     An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @returns {Promise<Array<Model>>}\n   */\n  static async bulkCreate(records, options = {}) {\n    if (!records.length) {\n      return [];\n    }\n\n    const dialect = this.sequelize.options.dialect;\n    const now = Utils.now(this.sequelize.options.dialect);\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    options.model = this;\n\n    if (!options.includeValidated) {\n      this._conformIncludes(options, this);\n      if (options.include) {\n        this._expandIncludeAll(options);\n        this._validateIncludedElements(options);\n      }\n    }\n\n    const instances = records.map(values => this.build(values, { isNewRecord: true, include: options.include }));\n\n    const recursiveBulkCreate = async (instances, options) => {\n      options = {\n        validate: false,\n        hooks: true,\n        individualHooks: false,\n        ignoreDuplicates: false,\n        ...options\n      };\n\n      if (options.returning === undefined) {\n        if (options.association) {\n          options.returning = false;\n        } else {\n          options.returning = true;\n        }\n      }\n      if (options.ignoreDuplicates && !this.sequelize.dialect.supports.inserts.ignoreDuplicates &&\n          !this.sequelize.dialect.supports.inserts.onConflictDoNothing) {\n        throw new Error(`${dialect} does not support the ignoreDuplicates option.`);\n      }\n      if (options.updateOnDuplicate && (dialect !== 'mysql' && dialect !== 'mariadb' && dialect !== 'sqlite' && dialect !== 'postgres')) {\n        throw new Error(`${dialect} does not support the updateOnDuplicate option.`);\n      }\n\n      const model = options.model;\n\n      options.fields = options.fields || Object.keys(model.rawAttributes);\n      const createdAtAttr = model._timestampAttributes.createdAt;\n      const updatedAtAttr = model._timestampAttributes.updatedAt;\n\n      if (options.updateOnDuplicate !== undefined) {\n        if (Array.isArray(options.updateOnDuplicate) && options.updateOnDuplicate.length) {\n          options.updateOnDuplicate = _.intersection(\n            _.without(Object.keys(model.tableAttributes), createdAtAttr),\n            options.updateOnDuplicate\n          );\n        } else {\n          throw new Error('updateOnDuplicate option only supports non-empty array.');\n        }\n      }\n\n      // Run before hook\n      if (options.hooks) {\n        await model.runHooks('beforeBulkCreate', instances, options);\n      }\n      // Validate\n      if (options.validate) {\n        const errors = [];\n        const validateOptions = { ...options };\n        validateOptions.hooks = options.individualHooks;\n\n        await Promise.all(instances.map(async instance => {\n          try {\n            await instance.validate(validateOptions);\n          } catch (err) {\n            errors.push(new sequelizeErrors.BulkRecordError(err, instance));\n          }\n        }));\n\n        delete options.skip;\n        if (errors.length) {\n          throw new sequelizeErrors.AggregateError(errors);\n        }\n      }\n      if (options.individualHooks) {\n        await Promise.all(instances.map(async instance => {\n          const individualOptions = {\n            ...options,\n            validate: false,\n            hooks: true\n          };\n          delete individualOptions.fields;\n          delete individualOptions.individualHooks;\n          delete individualOptions.ignoreDuplicates;\n\n          await instance.save(individualOptions);\n        }));\n      } else {\n        if (options.include && options.include.length) {\n          await Promise.all(options.include.filter(include => include.association instanceof BelongsTo).map(async include => {\n            const associationInstances = [];\n            const associationInstanceIndexToInstanceMap = [];\n\n            for (const instance of instances) {\n              const associationInstance = instance.get(include.as);\n              if (associationInstance) {\n                associationInstances.push(associationInstance);\n                associationInstanceIndexToInstanceMap.push(instance);\n              }\n            }\n\n            if (!associationInstances.length) {\n              return;\n            }\n\n            const includeOptions = _(Utils.cloneDeep(include))\n              .omit(['association'])\n              .defaults({\n                transaction: options.transaction,\n                logging: options.logging\n              }).value();\n\n            const createdAssociationInstances = await recursiveBulkCreate(associationInstances, includeOptions);\n            for (const idx in createdAssociationInstances) {\n              const associationInstance = createdAssociationInstances[idx];\n              const instance = associationInstanceIndexToInstanceMap[idx];\n\n              await include.association.set(instance, associationInstance, { save: false, logging: options.logging });\n            }\n          }));\n        }\n\n        // Create all in one query\n        // Recreate records from instances to represent any changes made in hooks or validation\n        records = instances.map(instance => {\n          const values = instance.dataValues;\n\n          // set createdAt/updatedAt attributes\n          if (createdAtAttr && !values[createdAtAttr]) {\n            values[createdAtAttr] = now;\n            if (!options.fields.includes(createdAtAttr)) {\n              options.fields.push(createdAtAttr);\n            }\n          }\n          if (updatedAtAttr && !values[updatedAtAttr]) {\n            values[updatedAtAttr] = now;\n            if (!options.fields.includes(updatedAtAttr)) {\n              options.fields.push(updatedAtAttr);\n            }\n          }\n\n          const out = Utils.mapValueFieldNames(values, options.fields, model);\n          for (const key of model._virtualAttributes) {\n            delete out[key];\n          }\n          return out;\n        });\n\n        // Map attributes to fields for serial identification\n        const fieldMappedAttributes = {};\n        for (const attr in model.tableAttributes) {\n          fieldMappedAttributes[model.rawAttributes[attr].field || attr] = model.rawAttributes[attr];\n        }\n\n        // Map updateOnDuplicate attributes to fields\n        if (options.updateOnDuplicate) {\n          options.updateOnDuplicate = options.updateOnDuplicate.map(attr => model.rawAttributes[attr].field || attr);\n\n          if (options.conflictAttributes) {\n            options.upsertKeys = options.conflictAttributes.map(\n              attrName => model.rawAttributes[attrName].field || attrName\n            );\n          } else {\n            const upsertKeys = [];\n\n            for (const i of model._indexes) {\n              if (i.unique && !i.where) { // Don't infer partial indexes\n                upsertKeys.push(...i.fields);\n              }\n            }\n\n            const firstUniqueKey = Object.values(model.uniqueKeys).find(c => c.fields.length > 0);\n\n            if (firstUniqueKey && firstUniqueKey.fields) {\n              upsertKeys.push(...firstUniqueKey.fields);\n            }\n\n            options.upsertKeys = upsertKeys.length > 0\n              ? upsertKeys\n              : Object.values(model.primaryKeys).map(x => x.field);\n          }\n        }\n\n        // Map returning attributes to fields\n        if (options.returning && Array.isArray(options.returning)) {\n          options.returning = options.returning.map(attr => _.get(model.rawAttributes[attr], 'field', attr));\n        }\n\n        const results = await model.queryInterface.bulkInsert(model.getTableName(options), records, options, fieldMappedAttributes);\n        if (Array.isArray(results)) {\n          results.forEach((result, i) => {\n            const instance = instances[i];\n\n            for (const key in result) {\n              if (!instance || key === model.primaryKeyAttribute &&\n                instance.get(model.primaryKeyAttribute) &&\n                ['mysql', 'mariadb', 'sqlite'].includes(dialect)) {\n                // The query.js for these DBs is blind, it autoincrements the\n                // primarykey value, even if it was set manually. Also, it can\n                // return more results than instances, bug?.\n                continue;\n              }\n              if (Object.prototype.hasOwnProperty.call(result, key)) {\n                const record = result[key];\n\n                const attr = _.find(model.rawAttributes, attribute => attribute.fieldName === key || attribute.field === key);\n\n                instance.dataValues[attr && attr.fieldName || key] = record;\n              }\n            }\n          });\n        }\n      }\n\n      if (options.include && options.include.length) {\n        await Promise.all(options.include.filter(include => !(include.association instanceof BelongsTo ||\n          include.parent && include.parent.association instanceof BelongsToMany)).map(async include => {\n          const associationInstances = [];\n          const associationInstanceIndexToInstanceMap = [];\n\n          for (const instance of instances) {\n            let associated = instance.get(include.as);\n            if (!Array.isArray(associated)) associated = [associated];\n\n            for (const associationInstance of associated) {\n              if (associationInstance) {\n                if (!(include.association instanceof BelongsToMany)) {\n                  associationInstance.set(include.association.foreignKey, instance.get(include.association.sourceKey || instance.constructor.primaryKeyAttribute, { raw: true }), { raw: true });\n                  Object.assign(associationInstance, include.association.scope);\n                }\n                associationInstances.push(associationInstance);\n                associationInstanceIndexToInstanceMap.push(instance);\n              }\n            }\n          }\n\n          if (!associationInstances.length) {\n            return;\n          }\n\n          const includeOptions = _(Utils.cloneDeep(include))\n            .omit(['association'])\n            .defaults({\n              transaction: options.transaction,\n              logging: options.logging\n            }).value();\n\n          const createdAssociationInstances = await recursiveBulkCreate(associationInstances, includeOptions);\n          if (include.association instanceof BelongsToMany) {\n            const valueSets = [];\n\n            for (const idx in createdAssociationInstances) {\n              const associationInstance = createdAssociationInstances[idx];\n              const instance = associationInstanceIndexToInstanceMap[idx];\n\n              const values = {\n                [include.association.foreignKey]: instance.get(instance.constructor.primaryKeyAttribute, { raw: true }),\n                [include.association.otherKey]: associationInstance.get(associationInstance.constructor.primaryKeyAttribute, { raw: true }),\n                // Include values defined in the association\n                ...include.association.through.scope\n              };\n              if (associationInstance[include.association.through.model.name]) {\n                for (const attr of Object.keys(include.association.through.model.rawAttributes)) {\n                  if (include.association.through.model.rawAttributes[attr]._autoGenerated ||\n                    attr === include.association.foreignKey ||\n                    attr === include.association.otherKey ||\n                    typeof associationInstance[include.association.through.model.name][attr] === 'undefined') {\n                    continue;\n                  }\n                  values[attr] = associationInstance[include.association.through.model.name][attr];\n                }\n              }\n\n              valueSets.push(values);\n            }\n\n            const throughOptions = _(Utils.cloneDeep(include))\n              .omit(['association', 'attributes'])\n              .defaults({\n                transaction: options.transaction,\n                logging: options.logging\n              }).value();\n            throughOptions.model = include.association.throughModel;\n            const throughInstances = include.association.throughModel.bulkBuild(valueSets, throughOptions);\n\n            await recursiveBulkCreate(throughInstances, throughOptions);\n          }\n        }));\n      }\n\n      // map fields back to attributes\n      instances.forEach(instance => {\n        for (const attr in model.rawAttributes) {\n          if (model.rawAttributes[attr].field &&\n              instance.dataValues[model.rawAttributes[attr].field] !== undefined &&\n              model.rawAttributes[attr].field !== attr\n          ) {\n            instance.dataValues[attr] = instance.dataValues[model.rawAttributes[attr].field];\n            delete instance.dataValues[model.rawAttributes[attr].field];\n          }\n          instance._previousDataValues[attr] = instance.dataValues[attr];\n          instance.changed(attr, false);\n        }\n        instance.isNewRecord = false;\n      });\n\n      // Run after hook\n      if (options.hooks) {\n        await model.runHooks('afterBulkCreate', instances, options);\n      }\n\n      return instances;\n    };\n\n    return await recursiveBulkCreate(instances, options);\n  }\n\n  /**\n   * Truncate all instances of the model. This is a convenient method for Model.destroy({ truncate: true }).\n   *\n   * @param {object}           [options] The options passed to Model.destroy in addition to truncate\n   * @param {boolean|Function} [options.cascade = false] Truncates all tables that have foreign-key references to the named table, or to any tables added to the group due to CASCADE.\n   * @param {boolean}          [options.restartIdentity=false] Automatically restart sequences owned by columns of the truncated table.\n   * @param {Transaction}      [options.transaction] Transaction to run query under\n   * @param {boolean|Function} [options.logging] A function that logs sql queries, or false for no logging\n   * @param {boolean}          [options.benchmark=false] Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param {string}           [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @returns {Promise}\n   *\n   * @see\n   * {@link Model.destroy} for more information\n   */\n  static async truncate(options) {\n    options = Utils.cloneDeep(options) || {};\n    options.truncate = true;\n    return await this.destroy(options);\n  }\n\n  /**\n   * Delete multiple instances, or set their deletedAt timestamp to the current time if `paranoid` is enabled.\n   *\n   * @param  {object}       options                         destroy options\n   * @param  {object}       [options.where]                 Filter the destroy\n   * @param  {boolean}      [options.hooks=true]            Run before / after bulk destroy hooks?\n   * @param  {boolean}      [options.individualHooks=false] If set to true, destroy will SELECT all records matching the where parameter and will execute before / after destroy hooks on each row\n   * @param  {number}       [options.limit]                 How many rows to delete\n   * @param  {boolean}      [options.force=false]           Delete instead of setting deletedAt to current timestamp (only applicable if `paranoid` is enabled)\n   * @param  {boolean}      [options.truncate=false]        If set to true, dialects that support it will use TRUNCATE instead of DELETE FROM. If a table is truncated the where and limit options are ignored\n   * @param  {boolean}      [options.cascade=false]         Only used in conjunction with TRUNCATE. Truncates  all tables that have foreign-key references to the named table, or to any tables added to the group due to CASCADE.\n   * @param  {boolean}      [options.restartIdentity=false] Only used in conjunction with TRUNCATE. Automatically restart sequences owned by columns of the truncated table.\n   * @param  {Transaction}  [options.transaction] Transaction to run query under\n   * @param  {Function}     [options.logging=false]         A function that gets executed while running the query to log the sql.\n   * @param  {boolean}      [options.benchmark=false]       Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   *\n   * @returns {Promise<number>} The number of destroyed rows\n   */\n  static async destroy(options) {\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    this._injectScope(options);\n\n    if (!options || !(options.where || options.truncate)) {\n      throw new Error('Missing where or truncate attribute in the options parameter of model.destroy.');\n    }\n\n    if (!options.truncate && !_.isPlainObject(options.where) && !Array.isArray(options.where) && !(options.where instanceof Utils.SequelizeMethod)) {\n      throw new Error('Expected plain object, array or sequelize method in the options.where parameter of model.destroy.');\n    }\n\n    options = _.defaults(options, {\n      hooks: true,\n      individualHooks: false,\n      force: false,\n      cascade: false,\n      restartIdentity: false\n    });\n\n    options.type = QueryTypes.BULKDELETE;\n\n    Utils.mapOptionFieldNames(options, this);\n    options.model = this;\n\n\n    // Run before hook\n    if (options.hooks) {\n      await this.runHooks('beforeBulkDestroy', options);\n    }\n    let instances;\n    // Get daos and run beforeDestroy hook on each record individually\n    if (options.individualHooks) {\n      instances = await this.findAll({ where: options.where, transaction: options.transaction, logging: options.logging, benchmark: options.benchmark });\n\n      await Promise.all(instances.map(instance => this.runHooks('beforeDestroy', instance, options)));\n    }\n    let result;\n    // Run delete query (or update if paranoid)\n    if (this._timestampAttributes.deletedAt && !options.force) {\n      // Set query type appropriately when running soft delete\n      options.type = QueryTypes.BULKUPDATE;\n\n      const attrValueHash = {};\n      const deletedAtAttribute = this.rawAttributes[this._timestampAttributes.deletedAt];\n      const field = this.rawAttributes[this._timestampAttributes.deletedAt].field;\n      const where = {\n        [field]: Object.prototype.hasOwnProperty.call(deletedAtAttribute, 'defaultValue') ? deletedAtAttribute.defaultValue : null\n      };\n\n\n      attrValueHash[field] = Utils.now(this.sequelize.options.dialect);\n      result = await this.queryInterface.bulkUpdate(this.getTableName(options), attrValueHash, Object.assign(where, options.where), options, this.rawAttributes);\n    } else {\n      result = await this.queryInterface.bulkDelete(this.getTableName(options), options.where, options, this);\n    }\n    // Run afterDestroy hook on each record individually\n    if (options.individualHooks) {\n      await Promise.all(\n        instances.map(instance => this.runHooks('afterDestroy', instance, options))\n      );\n    }\n    // Run after hook\n    if (options.hooks) {\n      await this.runHooks('afterBulkDestroy', options);\n    }\n    return result;\n  }\n\n  /**\n   * Restore multiple instances if `paranoid` is enabled.\n   *\n   * @param  {object}       options                         restore options\n   * @param  {object}       [options.where]                 Filter the restore\n   * @param  {boolean}      [options.hooks=true]            Run before / after bulk restore hooks?\n   * @param  {boolean}      [options.individualHooks=false] If set to true, restore will find all records within the where parameter and will execute before / after bulkRestore hooks on each row\n   * @param  {number}       [options.limit]                 How many rows to undelete (only for mysql)\n   * @param  {Function}     [options.logging=false]         A function that gets executed while running the query to log the sql.\n   * @param  {boolean}      [options.benchmark=false]       Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {Transaction}  [options.transaction]           Transaction to run query under\n   *\n   * @returns {Promise}\n   */\n  static async restore(options) {\n    if (!this._timestampAttributes.deletedAt) throw new Error('Model is not paranoid');\n\n    options = {\n      hooks: true,\n      individualHooks: false,\n      ...options\n    };\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    options.type = QueryTypes.RAW;\n    options.model = this;\n\n    Utils.mapOptionFieldNames(options, this);\n\n    // Run before hook\n    if (options.hooks) {\n      await this.runHooks('beforeBulkRestore', options);\n    }\n\n    let instances;\n    // Get daos and run beforeRestore hook on each record individually\n    if (options.individualHooks) {\n      instances = await this.findAll({ where: options.where, transaction: options.transaction, logging: options.logging, benchmark: options.benchmark, paranoid: false });\n\n      await Promise.all(instances.map(instance => this.runHooks('beforeRestore', instance, options)));\n    }\n    // Run undelete query\n    const attrValueHash = {};\n    const deletedAtCol = this._timestampAttributes.deletedAt;\n    const deletedAtAttribute = this.rawAttributes[deletedAtCol];\n    const deletedAtDefaultValue = Object.prototype.hasOwnProperty.call(deletedAtAttribute, 'defaultValue') ? deletedAtAttribute.defaultValue : null;\n\n    attrValueHash[deletedAtAttribute.field || deletedAtCol] = deletedAtDefaultValue;\n    options.omitNull = false;\n    const result = await this.queryInterface.bulkUpdate(this.getTableName(options), attrValueHash, options.where, options, this.rawAttributes);\n    // Run afterDestroy hook on each record individually\n    if (options.individualHooks) {\n      await Promise.all(\n        instances.map(instance => this.runHooks('afterRestore', instance, options))\n      );\n    }\n    // Run after hook\n    if (options.hooks) {\n      await this.runHooks('afterBulkRestore', options);\n    }\n    return result;\n  }\n\n  /**\n   * Update multiple instances that match the where options.\n   *\n   * @param  {object}         values                          hash of values to update\n   * @param  {object}         options                         update options\n   * @param  {object}         options.where                   Options to describe the scope of the search.\n   * @param  {boolean}        [options.paranoid=true]         If true, only non-deleted records will be updated. If false, both deleted and non-deleted records will be updated. Only applies if `options.paranoid` is true for the model.\n   * @param  {Array}          [options.fields]                Fields to update (defaults to all fields)\n   * @param  {boolean}        [options.validate=true]         Should each row be subject to validation before it is inserted. The whole insert will fail if one row fails validation\n   * @param  {boolean}        [options.hooks=true]            Run before / after bulk update hooks?\n   * @param  {boolean}        [options.sideEffects=true]      Whether or not to update the side effects of any virtual setters.\n   * @param  {boolean}        [options.individualHooks=false] Run before / after update hooks?. If true, this will execute a SELECT followed by individual UPDATEs. A select is needed, because the row data needs to be passed to the hooks\n   * @param  {boolean|Array}  [options.returning=false]       If true, append RETURNING <model columns> to get back all defined values; if an array of column names, append RETURNING <columns> to get back specific columns (Postgres only)\n   * @param  {number}         [options.limit]                 How many rows to update (only for mysql and mariadb, implemented as TOP(n) for MSSQL; for sqlite it is supported only when rowid is present)\n   * @param  {Function}       [options.logging=false]         A function that gets executed while running the query to log the sql.\n   * @param  {boolean}        [options.benchmark=false]       Pass query execution time in milliseconds as second argument to logging function (options.logging).\n   * @param  {Transaction}    [options.transaction]           Transaction to run query under\n   * @param  {boolean}        [options.silent=false]          If true, the updatedAt timestamp will not be updated.\n   *\n   * @returns {Promise<Array<number,number>>}  The promise returns an array with one or two elements. The first element is always the number\n   * of affected rows, while the second element is the actual affected rows (only supported in postgres with `options.returning` true).\n   *\n   */\n  static async update(values, options) {\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    this._injectScope(options);\n    this._optionsMustContainWhere(options);\n\n    options = this._paranoidClause(this, _.defaults(options, {\n      validate: true,\n      hooks: true,\n      individualHooks: false,\n      returning: false,\n      force: false,\n      sideEffects: true\n    }));\n\n    options.type = QueryTypes.BULKUPDATE;\n\n    // Clone values so it doesn't get modified for caller scope and ignore undefined values\n    values = _.omitBy(values, value => value === undefined);\n\n    // Remove values that are not in the options.fields\n    if (options.fields && options.fields instanceof Array) {\n      for (const key of Object.keys(values)) {\n        if (!options.fields.includes(key)) {\n          delete values[key];\n        }\n      }\n    } else {\n      const updatedAtAttr = this._timestampAttributes.updatedAt;\n      options.fields = _.intersection(Object.keys(values), Object.keys(this.tableAttributes));\n      if (updatedAtAttr && !options.fields.includes(updatedAtAttr)) {\n        options.fields.push(updatedAtAttr);\n      }\n    }\n\n    if (this._timestampAttributes.updatedAt && !options.silent) {\n      values[this._timestampAttributes.updatedAt] = this._getDefaultTimestamp(this._timestampAttributes.updatedAt) || Utils.now(this.sequelize.options.dialect);\n    }\n\n    options.model = this;\n\n    let valuesUse;\n    // Validate\n    if (options.validate) {\n      const build = this.build(values);\n      build.set(this._timestampAttributes.updatedAt, values[this._timestampAttributes.updatedAt], { raw: true });\n\n      if (options.sideEffects) {\n        Object.assign(values, _.pick(build.get(), build.changed()));\n        options.fields = _.union(options.fields, Object.keys(values));\n      }\n\n      // We want to skip validations for all other fields\n      options.skip = _.difference(Object.keys(this.rawAttributes), Object.keys(values));\n      const attributes = await build.validate(options);\n      options.skip = undefined;\n      if (attributes && attributes.dataValues) {\n        values = _.pick(attributes.dataValues, Object.keys(values));\n      }\n    }\n    // Run before hook\n    if (options.hooks) {\n      options.attributes = values;\n      await this.runHooks('beforeBulkUpdate', options);\n      values = options.attributes;\n      delete options.attributes;\n    }\n\n    valuesUse = values;\n\n    // Get instances and run beforeUpdate hook on each record individually\n    let instances;\n    let updateDoneRowByRow = false;\n    if (options.individualHooks) {\n      instances = await this.findAll({\n        where: options.where,\n        transaction: options.transaction,\n        logging: options.logging,\n        benchmark: options.benchmark,\n        paranoid: options.paranoid\n      });\n\n      if (instances.length) {\n        // Run beforeUpdate hooks on each record and check whether beforeUpdate hook changes values uniformly\n        // i.e. whether they change values for each record in the same way\n        let changedValues;\n        let different = false;\n\n        instances = await Promise.all(instances.map(async instance => {\n          // Record updates in instances dataValues\n          Object.assign(instance.dataValues, values);\n          // Set the changed fields on the instance\n          _.forIn(valuesUse, (newValue, attr) => {\n            if (newValue !== instance._previousDataValues[attr]) {\n              instance.setDataValue(attr, newValue);\n            }\n          });\n\n          // Run beforeUpdate hook\n          await this.runHooks('beforeUpdate', instance, options);\n          if (!different) {\n            const thisChangedValues = {};\n            _.forIn(instance.dataValues, (newValue, attr) => {\n              if (newValue !== instance._previousDataValues[attr]) {\n                thisChangedValues[attr] = newValue;\n              }\n            });\n\n            if (!changedValues) {\n              changedValues = thisChangedValues;\n            } else {\n              different = !_.isEqual(changedValues, thisChangedValues);\n            }\n          }\n\n          return instance;\n        }));\n\n        if (!different) {\n          const keys = Object.keys(changedValues);\n          // Hooks do not change values or change them uniformly\n          if (keys.length) {\n            // Hooks change values - record changes in valuesUse so they are executed\n            valuesUse = changedValues;\n            options.fields = _.union(options.fields, keys);\n          }\n        } else {\n          instances = await Promise.all(instances.map(async instance => {\n            const individualOptions = {\n              ...options,\n              hooks: false,\n              validate: false\n            };\n            delete individualOptions.individualHooks;\n\n            return instance.save(individualOptions);\n          }));\n          updateDoneRowByRow = true;\n        }\n      }\n    }\n    let result;\n    if (updateDoneRowByRow) {\n      result = [instances.length, instances];\n    } else if (_.isEmpty(valuesUse)\n       || Object.keys(valuesUse).length === 1 && valuesUse[this._timestampAttributes.updatedAt]) {\n      // only updatedAt is being passed, then skip update\n      result = [0];\n    } else {\n      valuesUse = Utils.mapValueFieldNames(valuesUse, options.fields, this);\n      options = Utils.mapOptionFieldNames(options, this);\n      options.hasTrigger = this.options ? this.options.hasTrigger : false;\n\n      const affectedRows = await this.queryInterface.bulkUpdate(this.getTableName(options), valuesUse, options.where, options, this.tableAttributes);\n      if (options.returning) {\n        result = [affectedRows.length, affectedRows];\n        instances = affectedRows;\n      } else {\n        result = [affectedRows];\n      }\n    }\n\n    if (options.individualHooks) {\n      await Promise.all(instances.map(instance => this.runHooks('afterUpdate', instance, options)));\n      result[1] = instances;\n    }\n    // Run after hook\n    if (options.hooks) {\n      options.attributes = values;\n      await this.runHooks('afterBulkUpdate', options);\n      delete options.attributes;\n    }\n    return result;\n  }\n\n  /**\n   * Run a describe query on the table.\n   *\n   * @param {string} [schema] schema name to search table in\n   * @param {object} [options] query options\n   *\n   * @returns {Promise} hash of attributes and their types\n   */\n  static async describe(schema, options) {\n    return await this.queryInterface.describeTable(this.tableName, { schema: schema || this._schema || undefined, ...options });\n  }\n\n  static _getDefaultTimestamp(attr) {\n    if (!!this.rawAttributes[attr] && !!this.rawAttributes[attr].defaultValue) {\n      return Utils.toDefaultValue(this.rawAttributes[attr].defaultValue, this.sequelize.options.dialect);\n    }\n    return undefined;\n  }\n\n  static _expandAttributes(options) {\n    if (!_.isPlainObject(options.attributes)) {\n      return;\n    }\n    let attributes = Object.keys(this.rawAttributes);\n\n    if (options.attributes.exclude) {\n      attributes = attributes.filter(elem => !options.attributes.exclude.includes(elem));\n    }\n\n    if (options.attributes.include) {\n      attributes = attributes.concat(options.attributes.include);\n    }\n\n    options.attributes = attributes;\n  }\n\n  // Inject _scope into options.\n  static _injectScope(options) {\n    const scope = Utils.cloneDeep(this._scope);\n    this._defaultsOptions(options, scope);\n  }\n\n  static [Symbol.for('nodejs.util.inspect.custom')]() {\n    return this.name;\n  }\n\n  static hasAlias(alias) {\n    return Object.prototype.hasOwnProperty.call(this.associations, alias);\n  }\n\n  /**\n   * Increment the value of one or more columns. This is done in the database, which means it does not use the values currently stored on the Instance. The increment is done using a\n   * ``` SET column = column + X WHERE foo = 'bar' ``` query. To get the correct value after an increment into the Instance you should do a reload.\n   *\n   * @example <caption>increment number by 1</caption>\n   * Model.increment('number', { where: { foo: 'bar' });\n   *\n   * @example <caption>increment number and count by 2</caption>\n   * Model.increment(['number', 'count'], { by: 2, where: { foo: 'bar' } });\n   *\n   * @example <caption>increment answer by 42, and decrement tries by 1</caption>\n   * // `by` is ignored, as each column has its own value\n   * Model.increment({ answer: 42, tries: -1}, { by: 2, where: { foo: 'bar' } });\n   *\n   * @see\n   * {@link Model#reload}\n   *\n   * @param  {string|Array|object}  fields                       If a string is provided, that column is incremented by the value of `by` given in options. If an array is provided, the same is true for each column. If and object is provided, each column is incremented by the value given.\n   * @param  {object}               options                      increment options\n   * @param  {object}               options.where                conditions hash\n   * @param  {number}               [options.by=1]               The number to increment by\n   * @param  {boolean}              [options.silent=false]       If true, the updatedAt timestamp will not be updated.\n   * @param  {Function}             [options.logging=false]      A function that gets executed while running the query to log the sql.\n   * @param  {Transaction}          [options.transaction]        Transaction to run query under\n   * @param  {string}               [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @returns {Promise<Model[],?number>} returns an array of affected rows and affected count with `options.returning` true, whenever supported by dialect\n   */\n  static async increment(fields, options) {\n    options = options || {};\n    if (typeof fields === 'string') fields = [fields];\n    if (Array.isArray(fields)) {\n      fields = fields.map(f => {\n        if (this.rawAttributes[f] && this.rawAttributes[f].field && this.rawAttributes[f].field !== f) {\n          return this.rawAttributes[f].field;\n        }\n        return f;\n      });\n    } else if (fields && typeof fields === 'object') {\n      fields = Object.keys(fields).reduce((rawFields, f) => {\n        if (this.rawAttributes[f] && this.rawAttributes[f].field && this.rawAttributes[f].field !== f) {\n          rawFields[this.rawAttributes[f].field] = fields[f];\n        } else {\n          rawFields[f] = fields[f];\n        }\n        return rawFields;\n      }, {});\n    }\n\n    this._injectScope(options);\n    this._optionsMustContainWhere(options);\n\n    options = Utils.defaults({}, options, {\n      by: 1,\n      where: {},\n      increment: true\n    });\n    const isSubtraction = !options.increment;\n\n    Utils.mapOptionFieldNames(options, this);\n\n    const where = { ...options.where };\n\n    // A plain object whose keys are the fields to be incremented and whose values are\n    // the amounts to be incremented by.\n    let incrementAmountsByField = {};\n    if (Array.isArray(fields)) {\n      incrementAmountsByField = {};\n      for (const field of fields) {\n        incrementAmountsByField[field] = options.by;\n      }\n    } else {\n      // If the `fields` argument is not an array, then we assume it already has the\n      // form necessary to be placed directly in the `incrementAmountsByField` variable.\n      incrementAmountsByField = fields;\n    }\n\n    // If optimistic locking is enabled, we can take advantage that this is an\n    // increment/decrement operation and send it here as well. We put `-1` for\n    // decrementing because it will be subtracted, getting `-(-1)` which is `+1`\n    if (this._versionAttribute) {\n      incrementAmountsByField[this._versionAttribute] = isSubtraction ? -1 : 1;\n    }\n\n    const extraAttributesToBeUpdated = {};\n\n    const updatedAtAttr = this._timestampAttributes.updatedAt;\n    if (!options.silent && updatedAtAttr && !incrementAmountsByField[updatedAtAttr]) {\n      const attrName = this.rawAttributes[updatedAtAttr].field || updatedAtAttr;\n      extraAttributesToBeUpdated[attrName] = this._getDefaultTimestamp(updatedAtAttr) || Utils.now(this.sequelize.options.dialect);\n    }\n\n    const tableName = this.getTableName(options);\n    let affectedRows;\n    if (isSubtraction) {\n      affectedRows = await this.queryInterface.decrement(\n        this, tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options\n      );\n    } else {\n      affectedRows = await this.queryInterface.increment(\n        this, tableName, where, incrementAmountsByField, extraAttributesToBeUpdated, options\n      );\n    }\n\n    if (options.returning) {\n      return [affectedRows, affectedRows.length];\n    }\n\n    return [affectedRows];\n  }\n\n  /**\n   * Decrement the value of one or more columns. This is done in the database, which means it does not use the values currently stored on the Instance. The decrement is done using a\n   * ```sql SET column = column - X WHERE foo = 'bar'``` query. To get the correct value after a decrement into the Instance you should do a reload.\n   *\n   * @example <caption>decrement number by 1</caption>\n   * Model.decrement('number', { where: { foo: 'bar' });\n   *\n   * @example <caption>decrement number and count by 2</caption>\n   * Model.decrement(['number', 'count'], { by: 2, where: { foo: 'bar' } });\n   *\n   * @example <caption>decrement answer by 42, and decrement tries by -1</caption>\n   * // `by` is ignored, since each column has its own value\n   * Model.decrement({ answer: 42, tries: -1}, { by: 2, where: { foo: 'bar' } });\n   *\n   * @param {string|Array|object} fields If a string is provided, that column is incremented by the value of `by` given in options. If an array is provided, the same is true for each column. If and object is provided, each column is incremented by the value given.\n   * @param {object} options decrement options, similar to increment\n   *\n   * @see\n   * {@link Model.increment}\n   * @see\n   * {@link Model#reload}\n   * @since 4.36.0\n   *\n   * @returns {Promise<Model[],?number>} returns an array of affected rows and affected count with `options.returning` true, whenever supported by dialect\n   */\n  static async decrement(fields, options) {\n    return this.increment(fields, {\n      by: 1,\n      ...options,\n      increment: false\n    });\n  }\n\n  static _optionsMustContainWhere(options) {\n    assert(options && options.where, 'Missing where attribute in the options parameter');\n    assert(_.isPlainObject(options.where) || Array.isArray(options.where) || options.where instanceof Utils.SequelizeMethod,\n      'Expected plain object, array or sequelize method in the options.where parameter');\n  }\n\n  /**\n   * Get an object representing the query for this instance, use with `options.where`\n   *\n   * @param {boolean} [checkVersion=false] include version attribute in where hash\n   *\n   * @returns {object}\n   */\n  where(checkVersion) {\n    const where = this.constructor.primaryKeyAttributes.reduce((result, attribute) => {\n      result[attribute] = this.get(attribute, { raw: true });\n      return result;\n    }, {});\n\n    if (_.size(where) === 0) {\n      return this.constructor.options.whereCollection;\n    }\n    const versionAttr = this.constructor._versionAttribute;\n    if (checkVersion && versionAttr) {\n      where[versionAttr] = this.get(versionAttr, { raw: true });\n    }\n    return Utils.mapWhereFieldNames(where, this.constructor);\n  }\n\n  toString() {\n    return `[object SequelizeInstance:${this.constructor.name}]`;\n  }\n\n  /**\n   * Get the value of the underlying data value\n   *\n   * @param {string} key key to look in instance data store\n   *\n   * @returns {any}\n   */\n  getDataValue(key) {\n    return this.dataValues[key];\n  }\n\n  /**\n   * Update the underlying data value\n   *\n   * @param {string} key key to set in instance data store\n   * @param {any} value new value for given key\n   *\n   */\n  setDataValue(key, value) {\n    const originalValue = this._previousDataValues[key];\n\n    if (!_.isEqual(value, originalValue)) {\n      this.changed(key, true);\n    }\n\n    this.dataValues[key] = value;\n  }\n\n  /**\n   * If no key is given, returns all values of the instance, also invoking virtual getters.\n   *\n   * If key is given and a field or virtual getter is present for the key it will call that getter - else it will return the value for key.\n   *\n   * @param {string}  [key] key to get value of\n   * @param {object}  [options] get options\n   * @param {boolean} [options.plain=false] If set to true, included instances will be returned as plain objects\n   * @param {boolean} [options.raw=false] If set to true, field and virtual setters will be ignored\n   *\n   * @returns {object|any}\n   */\n  get(key, options) {\n    if (options === undefined && typeof key === 'object') {\n      options = key;\n      key = undefined;\n    }\n\n    options = options || {};\n\n    if (key) {\n      if (Object.prototype.hasOwnProperty.call(this._customGetters, key) && !options.raw) {\n        return this._customGetters[key].call(this, key, options);\n      }\n\n      if (options.plain && this._options.include && this._options.includeNames.includes(key)) {\n        if (Array.isArray(this.dataValues[key])) {\n          return this.dataValues[key].map(instance => instance.get(options));\n        }\n        if (this.dataValues[key] instanceof Model) {\n          return this.dataValues[key].get(options);\n        }\n        return this.dataValues[key];\n      }\n\n      return this.dataValues[key];\n    }\n\n    if (\n      this._hasCustomGetters\n      || options.plain && this._options.include\n      || options.clone\n    ) {\n      const values = {};\n      let _key;\n\n      if (this._hasCustomGetters) {\n        for (_key in this._customGetters) {\n          if (\n            this._options.attributes\n            && !this._options.attributes.includes(_key)\n          ) {\n            continue;\n          }\n\n          if (Object.prototype.hasOwnProperty.call(this._customGetters, _key)) {\n            values[_key] = this.get(_key, options);\n          }\n        }\n      }\n\n      for (_key in this.dataValues) {\n        if (\n          !Object.prototype.hasOwnProperty.call(values, _key)\n          && Object.prototype.hasOwnProperty.call(this.dataValues, _key)\n        ) {\n          values[_key] = this.get(_key, options);\n        }\n      }\n\n      return values;\n    }\n\n    return this.dataValues;\n  }\n\n  /**\n   * Set is used to update values on the instance (the sequelize representation of the instance that is, remember that nothing will be persisted before you actually call `save`).\n   * In its most basic form `set` will update a value stored in the underlying `dataValues` object. However, if a custom setter function is defined for the key, that function\n   * will be called instead. To bypass the setter, you can pass `raw: true` in the options object.\n   *\n   * If set is called with an object, it will loop over the object, and call set recursively for each key, value pair. If you set raw to true, the underlying dataValues will either be\n   * set directly to the object passed, or used to extend dataValues, if dataValues already contain values.\n   *\n   * When set is called, the previous value of the field is stored and sets a changed flag(see `changed`).\n   *\n   * Set can also be used to build instances for associations, if you have values for those.\n   * When using set with associations you need to make sure the property key matches the alias of the association\n   * while also making sure that the proper include options have been set (from .build() or .findOne())\n   *\n   * If called with a dot.separated key on a JSON/JSONB attribute it will set the value nested and flag the entire object as changed.\n   *\n   * @see\n   * {@link Model.findAll} for more information about includes\n   *\n   * @param {string|object} key key to set, it can be string or object. When string it will set that key, for object it will loop over all object properties nd set them.\n   * @param {any} value value to set\n   * @param {object} [options] set options\n   * @param {boolean} [options.raw=false] If set to true, field and virtual setters will be ignored\n   * @param {boolean} [options.reset=false] Clear all previously set data values\n   *\n   * @returns {Model}\n   */\n  set(key, value, options) {\n    let values;\n    let originalValue;\n\n    if (typeof key === 'object' && key !== null) {\n      values = key;\n      options = value || {};\n\n      if (options.reset) {\n        this.dataValues = {};\n        for (const key in values) {\n          this.changed(key, false);\n        }\n      }\n\n      // If raw, and we're not dealing with includes or special attributes, just set it straight on the dataValues object\n      if (options.raw && !(this._options && this._options.include) && !(options && options.attributes) && !this.constructor._hasDateAttributes && !this.constructor._hasBooleanAttributes) {\n        if (Object.keys(this.dataValues).length) {\n          Object.assign(this.dataValues, values);\n        } else {\n          this.dataValues = values;\n        }\n        // If raw, .changed() shouldn't be true\n        this._previousDataValues = { ...this.dataValues };\n      } else {\n        // Loop and call set\n        if (options.attributes) {\n          const setKeys = data => {\n            for (const k of data) {\n              if (values[k] === undefined) {\n                continue;\n              }\n              this.set(k, values[k], options);\n            }\n          };\n          setKeys(options.attributes);\n          if (this.constructor._hasVirtualAttributes) {\n            setKeys(this.constructor._virtualAttributes);\n          }\n          if (this._options.includeNames) {\n            setKeys(this._options.includeNames);\n          }\n        } else {\n          for (const key in values) {\n            this.set(key, values[key], options);\n          }\n        }\n\n        if (options.raw) {\n          // If raw, .changed() shouldn't be true\n          this._previousDataValues = { ...this.dataValues };\n        }\n      }\n      return this;\n    }\n    if (!options)\n      options = {};\n    if (!options.raw) {\n      originalValue = this.dataValues[key];\n    }\n\n    // If not raw, and there's a custom setter\n    if (!options.raw && this._customSetters[key]) {\n      this._customSetters[key].call(this, value, key);\n      // custom setter should have changed value, get that changed value\n      // TODO: v5 make setters return new value instead of changing internal store\n      const newValue = this.dataValues[key];\n      if (!_.isEqual(newValue, originalValue)) {\n        this._previousDataValues[key] = originalValue;\n        this.changed(key, true);\n      }\n    } else {\n      // Check if we have included models, and if this key matches the include model names/aliases\n      if (this._options && this._options.include && this._options.includeNames.includes(key)) {\n        // Pass it on to the include handler\n        this._setInclude(key, value, options);\n        return this;\n      }\n      // Bunch of stuff we won't do when it's raw\n      if (!options.raw) {\n        // If attribute is not in model definition, return\n        if (!this._isAttribute(key)) {\n          if (key.includes('.') && this.constructor._jsonAttributes.has(key.split('.')[0])) {\n            const previousNestedValue = Dottie.get(this.dataValues, key);\n            if (!_.isEqual(previousNestedValue, value)) {\n              Dottie.set(this.dataValues, key, value);\n              this.changed(key.split('.')[0], true);\n            }\n          }\n          return this;\n        }\n\n        // If attempting to set primary key and primary key is already defined, return\n        if (this.constructor._hasPrimaryKeys && originalValue && this.constructor._isPrimaryKey(key)) {\n          return this;\n        }\n\n        // If attempting to set read only attributes, return\n        if (!this.isNewRecord && this.constructor._hasReadOnlyAttributes && this.constructor._readOnlyAttributes.has(key)) {\n          return this;\n        }\n      }\n\n      // If there's a data type sanitizer\n      if (\n        !(value instanceof Utils.SequelizeMethod)\n        && Object.prototype.hasOwnProperty.call(this.constructor._dataTypeSanitizers, key)\n      ) {\n        value = this.constructor._dataTypeSanitizers[key].call(this, value, options);\n      }\n\n      // Set when the value has changed and not raw\n      if (\n        !options.raw &&\n        (\n          // True when sequelize method\n          value instanceof Utils.SequelizeMethod ||\n          // Check for data type type comparators\n          !(value instanceof Utils.SequelizeMethod) && this.constructor._dataTypeChanges[key] && this.constructor._dataTypeChanges[key].call(this, value, originalValue, options) || // Check default\n          !this.constructor._dataTypeChanges[key] && !_.isEqual(value, originalValue)\n        )\n      ) {\n        this._previousDataValues[key] = originalValue;\n        this.changed(key, true);\n      }\n\n      // set data value\n      this.dataValues[key] = value;\n    }\n    return this;\n  }\n\n  setAttributes(updates) {\n    return this.set(updates);\n  }\n\n  /**\n   * If changed is called with a string it will return a boolean indicating whether the value of that key in `dataValues` is different from the value in `_previousDataValues`.\n   *\n   * If changed is called without an argument, it will return an array of keys that have changed.\n   *\n   * If changed is called without an argument and no keys have changed, it will return `false`.\n   *\n   * Please note that this function will return `false` when a property from a nested (for example JSON) property\n   * was edited manually, you must call `changed('key', true)` manually in these cases.\n   * Writing an entirely new object (eg. deep cloned) will be detected.\n   *\n   * @example\n   * ```\n   * const mdl = await MyModel.findOne();\n   * mdl.myJsonField.a = 1;\n   * console.log(mdl.changed()) => false\n   * mdl.save(); // this will not save anything\n   * mdl.changed('myJsonField', true);\n   * console.log(mdl.changed()) => ['myJsonField']\n   * mdl.save(); // will save\n   * ```\n   *\n   * @param {string} [key] key to check or change status of\n   * @param {any} [value] value to set\n   *\n   * @returns {boolean|Array}\n   */\n  changed(key, value) {\n    if (key === undefined) {\n      if (this._changed.size > 0) {\n        return Array.from(this._changed);\n      }\n      return false;\n    }\n    if (value === true) {\n      this._changed.add(key);\n      return this;\n    }\n    if (value === false) {\n      this._changed.delete(key);\n      return this;\n    }\n    return this._changed.has(key);\n  }\n\n  /**\n   * Returns the previous value for key from `_previousDataValues`.\n   *\n   * If called without a key, returns the previous values for all values which have changed\n   *\n   * @param {string} [key] key to get previous value of\n   *\n   * @returns {any|Array<any>}\n   */\n  previous(key) {\n    if (key) {\n      return this._previousDataValues[key];\n    }\n\n    return _.pickBy(this._previousDataValues, (value, key) => this.changed(key));\n  }\n\n  _setInclude(key, value, options) {\n    if (!Array.isArray(value)) value = [value];\n    if (value[0] instanceof Model) {\n      value = value.map(instance => instance.dataValues);\n    }\n\n    const include = this._options.includeMap[key];\n    const association = include.association;\n    const accessor = key;\n    const primaryKeyAttribute = include.model.primaryKeyAttribute;\n    const childOptions = {\n      isNewRecord: this.isNewRecord,\n      include: include.include,\n      includeNames: include.includeNames,\n      includeMap: include.includeMap,\n      includeValidated: true,\n      raw: options.raw,\n      attributes: include.originalAttributes\n    };\n    let isEmpty;\n\n    if (include.originalAttributes === undefined || include.originalAttributes.length) {\n      if (association.isSingleAssociation) {\n        if (Array.isArray(value)) {\n          value = value[0];\n        }\n        isEmpty = value && value[primaryKeyAttribute] === null || value === null;\n        this[accessor] = this.dataValues[accessor] = isEmpty ? null : include.model.build(value, childOptions);\n      } else {\n        isEmpty = value[0] && value[0][primaryKeyAttribute] === null;\n        this[accessor] = this.dataValues[accessor] = isEmpty ? [] : include.model.bulkBuild(value, childOptions);\n      }\n    }\n  }\n\n  /**\n   * Validates this instance, and if the validation passes, persists it to the database.\n   *\n   * Returns a Promise that resolves to the saved instance (or rejects with a `Sequelize.ValidationError`, which will have a property for each of the fields for which the validation failed, with the error message for that field).\n   *\n   * This method is optimized to perform an UPDATE only into the fields that changed. If nothing has changed, no SQL query will be performed.\n   *\n   * This method is not aware of eager loaded associations. In other words, if some other model instance (child) was eager loaded with this instance (parent), and you change something in the child, calling `save()` will simply ignore the change that happened on the child.\n   *\n   * @param {object}      [options] save options\n   * @param {string[]}    [options.fields] An optional array of strings, representing database columns. If fields is provided, only those columns will be validated and saved.\n   * @param {boolean}     [options.silent=false] If true, the updatedAt timestamp will not be updated.\n   * @param {boolean}     [options.validate=true] If false, validations won't be run.\n   * @param {boolean}     [options.hooks=true] Run before and after create / update + validate hooks\n   * @param {Function}    [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   * @param {string}      [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   * @param {boolean}     [options.returning] Append RETURNING * to get back auto generated values (Postgres only)\n   *\n   * @returns {Promise<Model>}\n   */\n  async save(options) {\n    if (arguments.length > 1) {\n      throw new Error('The second argument was removed in favor of the options object.');\n    }\n\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    options = _.defaults(options, {\n      hooks: true,\n      validate: true\n    });\n\n    if (!options.fields) {\n      if (this.isNewRecord) {\n        options.fields = Object.keys(this.constructor.rawAttributes);\n      } else {\n        options.fields = _.intersection(this.changed(), Object.keys(this.constructor.rawAttributes));\n      }\n\n      options.defaultFields = options.fields;\n    }\n\n    if (options.returning === undefined) {\n      if (options.association) {\n        options.returning = false;\n      } else if (this.isNewRecord) {\n        options.returning = true;\n      }\n    }\n\n    const primaryKeyName = this.constructor.primaryKeyAttribute;\n    const primaryKeyAttribute = primaryKeyName && this.constructor.rawAttributes[primaryKeyName];\n    const createdAtAttr = this.constructor._timestampAttributes.createdAt;\n    const versionAttr = this.constructor._versionAttribute;\n    const hook = this.isNewRecord ? 'Create' : 'Update';\n    const wasNewRecord = this.isNewRecord;\n    const now = Utils.now(this.sequelize.options.dialect);\n    let updatedAtAttr = this.constructor._timestampAttributes.updatedAt;\n\n    if (updatedAtAttr && options.fields.length > 0 && !options.fields.includes(updatedAtAttr)) {\n      options.fields.push(updatedAtAttr);\n    }\n    if (versionAttr && options.fields.length > 0 && !options.fields.includes(versionAttr)) {\n      options.fields.push(versionAttr);\n    }\n\n    if (options.silent === true && !(this.isNewRecord && this.get(updatedAtAttr, { raw: true }))) {\n      // UpdateAtAttr might have been added as a result of Object.keys(Model.rawAttributes). In that case we have to remove it again\n      _.remove(options.fields, val => val === updatedAtAttr);\n      updatedAtAttr = false;\n    }\n\n    if (this.isNewRecord === true) {\n      if (createdAtAttr && !options.fields.includes(createdAtAttr)) {\n        options.fields.push(createdAtAttr);\n      }\n\n      if (primaryKeyAttribute && primaryKeyAttribute.defaultValue && !options.fields.includes(primaryKeyName)) {\n        options.fields.unshift(primaryKeyName);\n      }\n    }\n\n    if (this.isNewRecord === false) {\n      if (primaryKeyName && this.get(primaryKeyName, { raw: true }) === undefined) {\n        throw new Error('You attempted to save an instance with no primary key, this is not allowed since it would result in a global update');\n      }\n    }\n\n    if (updatedAtAttr && !options.silent && options.fields.includes(updatedAtAttr)) {\n      this.dataValues[updatedAtAttr] = this.constructor._getDefaultTimestamp(updatedAtAttr) || now;\n    }\n\n    if (this.isNewRecord && createdAtAttr && !this.dataValues[createdAtAttr]) {\n      this.dataValues[createdAtAttr] = this.constructor._getDefaultTimestamp(createdAtAttr) || now;\n    }\n    // Db2 does not allow NULL values for unique columns.\n    // Add dummy values if not provided by test case or user.\n    if (this.sequelize.options.dialect === 'db2' && this.isNewRecord) {\n      this.uniqno = this.sequelize.dialect.queryGenerator.addUniqueFields(\n        this.dataValues, this.constructor.rawAttributes, this.uniqno);\n    }\n    // Validate\n    if (options.validate) {\n      await this.validate(options);\n    }\n    // Run before hook\n    if (options.hooks) {\n      const beforeHookValues = _.pick(this.dataValues, options.fields);\n      let ignoreChanged = _.difference(this.changed(), options.fields); // In case of update where it's only supposed to update the passed values and the hook values\n      let hookChanged;\n      let afterHookValues;\n\n      if (updatedAtAttr && options.fields.includes(updatedAtAttr)) {\n        ignoreChanged = _.without(ignoreChanged, updatedAtAttr);\n      }\n\n      await this.constructor.runHooks(`before${hook}`, this, options);\n      if (options.defaultFields && !this.isNewRecord) {\n        afterHookValues = _.pick(this.dataValues, _.difference(this.changed(), ignoreChanged));\n\n        hookChanged = [];\n        for (const key of Object.keys(afterHookValues)) {\n          if (afterHookValues[key] !== beforeHookValues[key]) {\n            hookChanged.push(key);\n          }\n        }\n\n        options.fields = _.uniq(options.fields.concat(hookChanged));\n      }\n\n      if (hookChanged) {\n        if (options.validate) {\n          // Validate again\n\n          options.skip = _.difference(Object.keys(this.constructor.rawAttributes), hookChanged);\n          await this.validate(options);\n          delete options.skip;\n        }\n      }\n    }\n    if (options.fields.length && this.isNewRecord && this._options.include && this._options.include.length) {\n      await Promise.all(this._options.include.filter(include => include.association instanceof BelongsTo).map(async include => {\n        const instance = this.get(include.as);\n        if (!instance) return;\n\n        const includeOptions = _(Utils.cloneDeep(include))\n          .omit(['association'])\n          .defaults({\n            transaction: options.transaction,\n            logging: options.logging,\n            parentRecord: this\n          }).value();\n\n        await instance.save(includeOptions);\n\n        await this[include.association.accessors.set](instance, { save: false, logging: options.logging });\n      }));\n    }\n    const realFields = options.fields.filter(field => !this.constructor._virtualAttributes.has(field));\n    if (!realFields.length) return this;\n    if (!this.changed() && !this.isNewRecord) return this;\n\n    const versionFieldName = _.get(this.constructor.rawAttributes[versionAttr], 'field') || versionAttr;\n    const values = Utils.mapValueFieldNames(this.dataValues, options.fields, this.constructor);\n    let query = null;\n    let args = [];\n    let where;\n\n    if (this.isNewRecord) {\n      query = 'insert';\n      args = [this, this.constructor.getTableName(options), values, options];\n    } else {\n      where = this.where(true);\n      if (versionAttr) {\n        values[versionFieldName] = parseInt(values[versionFieldName], 10) + 1;\n      }\n      query = 'update';\n      args = [this, this.constructor.getTableName(options), values, where, options];\n    }\n\n    const [result, rowsUpdated] = await this.constructor.queryInterface[query](...args);\n    if (versionAttr) {\n      // Check to see that a row was updated, otherwise it's an optimistic locking error.\n      if (rowsUpdated < 1) {\n        throw new sequelizeErrors.OptimisticLockError({\n          modelName: this.constructor.name,\n          values,\n          where\n        });\n      } else {\n        result.dataValues[versionAttr] = values[versionFieldName];\n      }\n    }\n\n    // Transfer database generated values (defaults, autoincrement, etc)\n    for (const attr of Object.keys(this.constructor.rawAttributes)) {\n      if (this.constructor.rawAttributes[attr].field &&\n          values[this.constructor.rawAttributes[attr].field] !== undefined &&\n          this.constructor.rawAttributes[attr].field !== attr\n      ) {\n        values[attr] = values[this.constructor.rawAttributes[attr].field];\n        delete values[this.constructor.rawAttributes[attr].field];\n      }\n    }\n    Object.assign(values, result.dataValues);\n\n    Object.assign(result.dataValues, values);\n    if (wasNewRecord && this._options.include && this._options.include.length) {\n      await Promise.all(\n        this._options.include.filter(include => !(include.association instanceof BelongsTo ||\n          include.parent && include.parent.association instanceof BelongsToMany)).map(async include => {\n          let instances = this.get(include.as);\n\n          if (!instances) return;\n          if (!Array.isArray(instances)) instances = [instances];\n\n          const includeOptions = _(Utils.cloneDeep(include))\n            .omit(['association'])\n            .defaults({\n              transaction: options.transaction,\n              logging: options.logging,\n              parentRecord: this\n            }).value();\n\n          // Instances will be updated in place so we can safely treat HasOne like a HasMany\n          await Promise.all(instances.map(async instance => {\n            if (include.association instanceof BelongsToMany) {\n              await instance.save(includeOptions);\n              const values0 = {\n                [include.association.foreignKey]: this.get(this.constructor.primaryKeyAttribute, { raw: true }),\n                [include.association.otherKey]: instance.get(instance.constructor.primaryKeyAttribute, { raw: true }),\n                // Include values defined in the association\n                ...include.association.through.scope\n              };\n\n              if (instance[include.association.through.model.name]) {\n                for (const attr of Object.keys(include.association.through.model.rawAttributes)) {\n                  if (include.association.through.model.rawAttributes[attr]._autoGenerated ||\n                    attr === include.association.foreignKey ||\n                    attr === include.association.otherKey ||\n                    typeof instance[include.association.through.model.name][attr] === 'undefined') {\n                    continue;\n                  }\n                  values0[attr] = instance[include.association.through.model.name][attr];\n                }\n              }\n\n              await include.association.throughModel.create(values0, includeOptions);\n            } else {\n              instance.set(include.association.foreignKey, this.get(include.association.sourceKey || this.constructor.primaryKeyAttribute, { raw: true }), { raw: true });\n              Object.assign(instance, include.association.scope);\n              await instance.save(includeOptions);\n            }\n          }));\n        })\n      );\n    }\n    // Run after hook\n    if (options.hooks) {\n      await this.constructor.runHooks(`after${hook}`, result, options);\n    }\n    for (const field of options.fields) {\n      result._previousDataValues[field] = result.dataValues[field];\n      this.changed(field, false);\n    }\n    this.isNewRecord = false;\n\n    return result;\n  }\n\n  /**\n   * Refresh the current instance in-place, i.e. update the object with current data from the DB and return the same object.\n   * This is different from doing a `find(Instance.id)`, because that would create and return a new instance. With this method,\n   * all references to the Instance are updated with the new data and no new objects are created.\n   *\n   * @see\n   * {@link Model.findAll}\n   *\n   * @param {object} [options] Options that are passed on to `Model.find`\n   * @param {Function} [options.logging=false] A function that gets executed while running the query to log the sql.\n   *\n   * @returns {Promise<Model>}\n   */\n  async reload(options) {\n    options = Utils.defaults({\n      where: this.where()\n    }, options, {\n      include: this._options.include || undefined\n    });\n\n    const reloaded = await this.constructor.findOne(options);\n    if (!reloaded) {\n      throw new sequelizeErrors.InstanceError(\n        'Instance could not be reloaded because it does not exist anymore (find call returned null)'\n      );\n    }\n    // update the internal options of the instance\n    this._options = reloaded._options;\n    // re-set instance values\n    this.set(reloaded.dataValues, {\n      raw: true,\n      reset: true && !options.attributes\n    });\n\n    return this;\n  }\n\n  /**\n  * Validate the attributes of this instance according to validation rules set in the model definition.\n  *\n  * The promise fulfills if and only if validation successful; otherwise it rejects an Error instance containing { field name : [error msgs] } entries.\n  *\n  * @param {object} [options] Options that are passed to the validator\n  * @param {Array} [options.skip] An array of strings. All properties that are in this array will not be validated\n  * @param {Array} [options.fields] An array of strings. Only the properties that are in this array will be validated\n  * @param {boolean} [options.hooks=true] Run before and after validate hooks\n  *\n  * @returns {Promise}\n  */\n  async validate(options) {\n    return new InstanceValidator(this, options).validate();\n  }\n\n  /**\n   * This is the same as calling `set` and then calling `save` but it only saves the\n   * exact values passed to it, making it more atomic and safer.\n   *\n   * @see\n   * {@link Model#set}\n   * @see\n   * {@link Model#save}\n   *\n   * @param {object} values See `set`\n   * @param {object} options See `save`\n   *\n   * @returns {Promise<Model>}\n   */\n  async update(values, options) {\n    // Clone values so it doesn't get modified for caller scope and ignore undefined values\n    values = _.omitBy(values, value => value === undefined);\n\n    const changedBefore = this.changed() || [];\n\n    options = options || {};\n    if (Array.isArray(options)) options = { fields: options };\n\n    options = Utils.cloneDeep(options);\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    const setOptions = Utils.cloneDeep(options);\n    setOptions.attributes = options.fields;\n    this.set(values, setOptions);\n\n    // Now we need to figure out which fields were actually affected by the setter.\n    const sideEffects = _.without(this.changed(), ...changedBefore);\n    const fields = _.union(Object.keys(values), sideEffects);\n\n    if (!options.fields) {\n      options.fields = _.intersection(fields, this.changed());\n      options.defaultFields = options.fields;\n    }\n\n    return await this.save(options);\n  }\n\n  /**\n   * Destroy the row corresponding to this instance. Depending on your setting for paranoid, the row will either be completely deleted, or have its deletedAt timestamp set to the current time.\n   *\n   * @param {object}      [options={}] destroy options\n   * @param {boolean}     [options.force=false] If set to true, paranoid models will actually be deleted\n   * @param {Function}    [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   * @param {string}      [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   *\n   * @returns {Promise}\n   */\n  async destroy(options) {\n    options = {\n      hooks: true,\n      force: false,\n      ...options\n    };\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    // Run before hook\n    if (options.hooks) {\n      await this.constructor.runHooks('beforeDestroy', this, options);\n    }\n    const where = this.where(true);\n\n    let result;\n    if (this.constructor._timestampAttributes.deletedAt && options.force === false) {\n      const attributeName = this.constructor._timestampAttributes.deletedAt;\n      const attribute = this.constructor.rawAttributes[attributeName];\n      const defaultValue = Object.prototype.hasOwnProperty.call(attribute, 'defaultValue')\n        ? attribute.defaultValue\n        : null;\n      const currentValue = this.getDataValue(attributeName);\n      const undefinedOrNull = currentValue == null && defaultValue == null;\n      if (undefinedOrNull || _.isEqual(currentValue, defaultValue)) {\n        // only update timestamp if it wasn't already set\n        this.setDataValue(attributeName, new Date());\n      }\n\n      result = await this.save({ ...options, hooks: false });\n    } else {\n      result = await this.constructor.queryInterface.delete(this, this.constructor.getTableName(options), where, { type: QueryTypes.DELETE, limit: null, ...options });\n    }\n    // Run after hook\n    if (options.hooks) {\n      await this.constructor.runHooks('afterDestroy', this, options);\n    }\n    return result;\n  }\n\n  /**\n   * Helper method to determine if a instance is \"soft deleted\".  This is\n   * particularly useful if the implementer renamed the `deletedAt` attribute\n   * to something different.  This method requires `paranoid` to be enabled.\n   *\n   * @returns {boolean}\n   */\n  isSoftDeleted() {\n    if (!this.constructor._timestampAttributes.deletedAt) {\n      throw new Error('Model is not paranoid');\n    }\n\n    const deletedAtAttribute = this.constructor.rawAttributes[this.constructor._timestampAttributes.deletedAt];\n    const defaultValue = Object.prototype.hasOwnProperty.call(deletedAtAttribute, 'defaultValue') ? deletedAtAttribute.defaultValue : null;\n    const deletedAt = this.get(this.constructor._timestampAttributes.deletedAt) || null;\n    const isSet = deletedAt !== defaultValue;\n\n    return isSet;\n  }\n\n  /**\n   * Restore the row corresponding to this instance. Only available for paranoid models.\n   *\n   * @param {object}      [options={}] restore options\n   * @param {Function}    [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   *\n   * @returns {Promise}\n   */\n  async restore(options) {\n    if (!this.constructor._timestampAttributes.deletedAt) throw new Error('Model is not paranoid');\n\n    options = {\n      hooks: true,\n      force: false,\n      ...options\n    };\n\n    // Add CLS transaction\n    if (options.transaction === undefined && this.sequelize.constructor._cls) {\n      const t = this.sequelize.constructor._cls.get('transaction');\n      if (t) {\n        options.transaction = t;\n      }\n    }\n\n    // Run before hook\n    if (options.hooks) {\n      await this.constructor.runHooks('beforeRestore', this, options);\n    }\n    const deletedAtCol = this.constructor._timestampAttributes.deletedAt;\n    const deletedAtAttribute = this.constructor.rawAttributes[deletedAtCol];\n    const deletedAtDefaultValue = Object.prototype.hasOwnProperty.call(deletedAtAttribute, 'defaultValue') ? deletedAtAttribute.defaultValue : null;\n\n    this.setDataValue(deletedAtCol, deletedAtDefaultValue);\n    const result = await this.save({ ...options, hooks: false, omitNull: false });\n    // Run after hook\n    if (options.hooks) {\n      await this.constructor.runHooks('afterRestore', this, options);\n      return result;\n    }\n    return result;\n  }\n\n  /**\n   * Increment the value of one or more columns. This is done in the database, which means it does not use the values currently stored on the Instance. The increment is done using a\n   * ```sql\n   * SET column = column + X\n   * ```\n   * query. The updated instance will be returned by default in Postgres. However, in other dialects, you will need to do a reload to get the new values.\n   *\n   * @example\n   * instance.increment('number') // increment number by 1\n   *\n   * instance.increment(['number', 'count'], { by: 2 }) // increment number and count by 2\n   *\n   * // increment answer by 42, and tries by 1.\n   * // `by` is ignored, since each column has its own value\n   * instance.increment({ answer: 42, tries: 1}, { by: 2 })\n   *\n   * @see\n   * {@link Model#reload}\n   *\n   * @param {string|Array|object} fields If a string is provided, that column is incremented by the value of `by` given in options. If an array is provided, the same is true for each column. If and object is provided, each column is incremented by the value given.\n   * @param {object} [options] options\n   * @param {number} [options.by=1] The number to increment by\n   * @param {boolean} [options.silent=false] If true, the updatedAt timestamp will not be updated.\n   * @param {Function} [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   * @param {string} [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   * @param {boolean} [options.returning=true] Append RETURNING * to get back auto generated values (Postgres only)\n   *\n   * @returns {Promise<Model>}\n   * @since 4.0.0\n   */\n  async increment(fields, options) {\n    const identifier = this.where();\n\n    options = Utils.cloneDeep(options);\n    options.where = { ...options.where, ...identifier };\n    options.instance = this;\n\n    await this.constructor.increment(fields, options);\n\n    return this;\n  }\n\n  /**\n   * Decrement the value of one or more columns. This is done in the database, which means it does not use the values currently stored on the Instance. The decrement is done using a\n   * ```sql\n   * SET column = column - X\n   * ```\n   * query. The updated instance will be returned by default in Postgres. However, in other dialects, you will need to do a reload to get the new values.\n   *\n   * @example\n   * instance.decrement('number') // decrement number by 1\n   *\n   * instance.decrement(['number', 'count'], { by: 2 }) // decrement number and count by 2\n   *\n   * // decrement answer by 42, and tries by 1.\n   * // `by` is ignored, since each column has its own value\n   * instance.decrement({ answer: 42, tries: 1}, { by: 2 })\n   *\n   * @see\n   * {@link Model#reload}\n   * @param {string|Array|object} fields If a string is provided, that column is decremented by the value of `by` given in options. If an array is provided, the same is true for each column. If and object is provided, each column is decremented by the value given\n   * @param {object}      [options] decrement options\n   * @param {number}      [options.by=1] The number to decrement by\n   * @param {boolean}     [options.silent=false] If true, the updatedAt timestamp will not be updated.\n   * @param {Function}    [options.logging=false] A function that gets executed while running the query to log the sql.\n   * @param {Transaction} [options.transaction] Transaction to run query under\n   * @param {string}      [options.searchPath=DEFAULT] An optional parameter to specify the schema search_path (Postgres only)\n   * @param {boolean}     [options.returning=true] Append RETURNING * to get back auto generated values (Postgres only)\n   *\n   * @returns {Promise}\n   */\n  async decrement(fields, options) {\n    return this.increment(fields, {\n      by: 1,\n      ...options,\n      increment: false\n    });\n  }\n\n  /**\n   * Check whether this and `other` Instance refer to the same row\n   *\n   * @param {Model} other Other instance to compare against\n   *\n   * @returns {boolean}\n   */\n  equals(other) {\n    if (!other || !other.constructor) {\n      return false;\n    }\n\n    if (!(other instanceof this.constructor)) {\n      return false;\n    }\n\n    return this.constructor.primaryKeyAttributes.every(attribute => this.get(attribute, { raw: true }) === other.get(attribute, { raw: true }));\n  }\n\n  /**\n   * Check if this is equal to one of `others` by calling equals\n   *\n   * @param {Array<Model>} others An array of instances to check against\n   *\n   * @returns {boolean}\n   */\n  equalsOneOf(others) {\n    return others.some(other => this.equals(other));\n  }\n\n  setValidators(attribute, validators) {\n    this.validators[attribute] = validators;\n  }\n\n  /**\n   * Convert the instance to a JSON representation.\n   * Proxies to calling `get` with no keys.\n   * This means get all values gotten from the DB, and apply all custom getters.\n   *\n   * @see\n   * {@link Model#get}\n   *\n   * @returns {object}\n   */\n  toJSON() {\n    return _.cloneDeep(\n      this.get({\n        plain: true\n      })\n    );\n  }\n\n  /**\n   * Creates a 1:m association between this (the source) and the provided target.\n   * The foreign key is added on the target.\n   *\n   * @param {Model}               target Target model\n   * @param {object}              [options] hasMany association options\n   * @param {boolean}             [options.hooks=false] Set to true to run before-/afterDestroy hooks when an associated model is deleted because of a cascade. For example if `User.hasOne(Profile, {onDelete: 'cascade', hooks:true})`, the before-/afterDestroy hooks for profile will be called when a user is deleted. Otherwise the profile will be deleted without invoking any hooks\n   * @param {string|object}       [options.as] The alias of this model. If you provide a string, it should be plural, and will be singularized using node.inflection. If you want to control the singular version yourself, provide an object with `plural` and `singular` keys. See also the `name` option passed to `sequelize.define`. If you create multiple associations between the same tables, you should provide an alias to be able to distinguish between them. If you provide an alias when creating the association, you should provide the same alias when eager loading and when getting associated models. Defaults to the pluralized name of target\n   * @param {string|object}       [options.foreignKey] The name of the foreign key in the target table or an object representing the type definition for the foreign column (see `Sequelize.define` for syntax). When using an object, you can add a `name` property to set the name of the column. Defaults to the name of source + primary key of source\n   * @param {string}              [options.sourceKey] The name of the field to use as the key for the association in the source table. Defaults to the primary key of the source table\n   * @param {object}              [options.scope] A key/value set that will be used for association create and find defaults on the target. (sqlite not supported for N:M)\n   * @param {string}              [options.onDelete='SET&nbsp;NULL|CASCADE'] SET NULL if foreignKey allows nulls, CASCADE if otherwise\n   * @param {string}              [options.onUpdate='CASCADE'] Set `ON UPDATE`\n   * @param {boolean}             [options.constraints=true] Should on update and on delete constraints be enabled on the foreign key.\n   *\n   * @returns {HasMany}\n   *\n   * @example\n   * User.hasMany(Profile) // This will add userId to the profile table\n   */\n  static hasMany(target, options) {} // eslint-disable-line\n\n  /**\n   * Create an N:M association with a join table. Defining `through` is required.\n   *\n   * @param {Model}               target Target model\n   * @param {object}              options belongsToMany association options\n   * @param {boolean}             [options.hooks=false] Set to true to run before-/afterDestroy hooks when an associated model is deleted because of a cascade. For example if `User.hasOne(Profile, {onDelete: 'cascade', hooks:true})`, the before-/afterDestroy hooks for profile will be called when a user is deleted. Otherwise the profile will be deleted without invoking any hooks\n   * @param {Model|string|object} options.through The name of the table that is used to join source and target in n:m associations. Can also be a sequelize model if you want to define the junction table yourself and add extra attributes to it.\n   * @param {Model}               [options.through.model] The model used to join both sides of the N:M association.\n   * @param {object}              [options.through.scope] A key/value set that will be used for association create and find defaults on the through model. (Remember to add the attributes to the through model)\n   * @param {boolean}             [options.through.unique=true] If true a unique key will be generated from the foreign keys used (might want to turn this off and create specific unique keys when using scopes)\n   * @param {boolean}             [options.through.paranoid=false] If true the generated join table will be paranoid\n   * @param {string|object}       [options.as] The alias of this association. If you provide a string, it should be plural, and will be singularized using node.inflection. If you want to control the singular version yourself, provide an object with `plural` and `singular` keys. See also the `name` option passed to `sequelize.define`. If you create multiple associations between the same tables, you should provide an alias to be able to distinguish between them. If you provide an alias when creating the association, you should provide the same alias when eager loading and when getting associated models. Defaults to the pluralized name of target\n   * @param {string|object}       [options.foreignKey] The name of the foreign key in the join table (representing the source model) or an object representing the type definition for the foreign column (see `Sequelize.define` for syntax). When using an object, you can add a `name` property to set the name of the column. Defaults to the name of source + primary key of source\n   * @param {string|object}       [options.otherKey] The name of the foreign key in the join table (representing the target model) or an object representing the type definition for the other column (see `Sequelize.define` for syntax). When using an object, you can add a `name` property to set the name of the column. Defaults to the name of target + primary key of target\n   * @param {object}              [options.scope] A key/value set that will be used for association create and find defaults on the target. (sqlite not supported for N:M)\n   * @param {boolean}             [options.timestamps=sequelize.options.timestamps] Should the join model have timestamps\n   * @param {string}              [options.onDelete='SET&nbsp;NULL|CASCADE'] Cascade if this is a n:m, and set null if it is a 1:m\n   * @param {string}              [options.onUpdate='CASCADE'] Sets `ON UPDATE`\n   * @param {boolean}             [options.constraints=true] Should on update and on delete constraints be enabled on the foreign key.\n   *\n   * @returns {BelongsToMany}\n   *\n   * @example\n   * // Automagically generated join model\n   * User.belongsToMany(Project, { through: 'UserProjects' })\n   * Project.belongsToMany(User, { through: 'UserProjects' })\n   *\n   * // Join model with additional attributes\n   * const UserProjects = sequelize.define('UserProjects', {\n   *   started: Sequelize.BOOLEAN\n   * })\n   * User.belongsToMany(Project, { through: UserProjects })\n   * Project.belongsToMany(User, { through: UserProjects })\n   */\n  static belongsToMany(target, options) {} // eslint-disable-line\n\n  /**\n   * Creates an association between this (the source) and the provided target. The foreign key is added on the target.\n   *\n   * @param {Model}           target Target model\n   * @param {object}          [options] hasOne association options\n   * @param {boolean}         [options.hooks=false] Set to true to run before-/afterDestroy hooks when an associated model is deleted because of a cascade. For example if `User.hasOne(Profile, {onDelete: 'cascade', hooks:true})`, the before-/afterDestroy hooks for profile will be called when a user is deleted. Otherwise the profile will be deleted without invoking any hooks\n   * @param {string}          [options.as] The alias of this model, in singular form. See also the `name` option passed to `sequelize.define`. If you create multiple associations between the same tables, you should provide an alias to be able to distinguish between them. If you provide an alias when creating the association, you should provide the same alias when eager loading and when getting associated models. Defaults to the singularized name of target\n   * @param {string|object}   [options.foreignKey] The name of the foreign key attribute in the target model or an object representing the type definition for the foreign column (see `Sequelize.define` for syntax). When using an object, you can add a `name` property to set the name of the column. Defaults to the name of source + primary key of source\n   * @param {string}          [options.sourceKey] The name of the attribute to use as the key for the association in the source table. Defaults to the primary key of the source table\n   * @param {string}          [options.onDelete='SET&nbsp;NULL|CASCADE'] SET NULL if foreignKey allows nulls, CASCADE if otherwise\n   * @param {string}          [options.onUpdate='CASCADE'] Sets 'ON UPDATE'\n   * @param {boolean}         [options.constraints=true] Should on update and on delete constraints be enabled on the foreign key.\n   * @param {string}          [options.uniqueKey] The custom name for unique constraint.\n   *\n   * @returns {HasOne}\n   *\n   * @example\n   * User.hasOne(Profile) // This will add userId to the profile table\n   */\n  static hasOne(target, options) {} // eslint-disable-line\n\n  /**\n   * Creates an association between this (the source) and the provided target. The foreign key is added on the source.\n   *\n   * @param {Model}           target The target model\n   * @param {object}          [options] belongsTo association options\n   * @param {boolean}         [options.hooks=false] Set to true to run before-/afterDestroy hooks when an associated model is deleted because of a cascade. For example if `User.hasOne(Profile, {onDelete: 'cascade', hooks:true})`, the before-/afterDestroy hooks for profile will be called when a user is deleted. Otherwise the profile will be deleted without invoking any hooks\n   * @param {string}          [options.as] The alias of this model, in singular form. See also the `name` option passed to `sequelize.define`. If you create multiple associations between the same tables, you should provide an alias to be able to distinguish between them. If you provide an alias when creating the association, you should provide the same alias when eager loading and when getting associated models. Defaults to the singularized name of target\n   * @param {string|object}   [options.foreignKey] The name of the foreign key attribute in the source table or an object representing the type definition for the foreign column (see `Sequelize.define` for syntax). When using an object, you can add a `name` property to set the name of the column. Defaults to the name of target + primary key of target\n   * @param {string}          [options.targetKey] The name of the attribute to use as the key for the association in the target table. Defaults to the primary key of the target table\n   * @param {string}          [options.onDelete='SET&nbsp;NULL|NO&nbsp;ACTION'] SET NULL if foreignKey allows nulls, NO ACTION if otherwise\n   * @param {string}          [options.onUpdate='CASCADE'] Sets 'ON UPDATE'\n   * @param {boolean}         [options.constraints=true] Should on update and on delete constraints be enabled on the foreign key.\n   *\n   * @returns {BelongsTo}\n   *\n   * @example\n   * Profile.belongsTo(User) // This will add userId to the profile table\n   */\n  static belongsTo(target, options) {} // eslint-disable-line\n}\n\n/**\n * Unpacks an object that only contains a single Op.and key to the value of Op.and\n *\n * Internal method used by {@link combineWheresWithAnd}\n *\n * @param {WhereOptions} where The object to unpack\n * @example `{ [Op.and]: [a, b] }` becomes `[a, b]`\n * @example `{ [Op.and]: { key: val } }` becomes `{ key: val }`\n * @example `{ [Op.or]: [a, b] }` remains as `{ [Op.or]: [a, b] }`\n * @example `{ [Op.and]: [a, b], key: c }` remains as `{ [Op.and]: [a, b], key: c }`\n * @private\n */\nfunction unpackAnd(where) {\n  if (!_.isObject(where)) {\n    return where;\n  }\n\n  const keys = Utils.getComplexKeys(where);\n\n  // object is empty, remove it.\n  if (keys.length === 0) {\n    return;\n  }\n\n  // we have more than just Op.and, keep as-is\n  if (keys.length !== 1 || keys[0] !== Op.and) {\n    return where;\n  }\n\n  const andParts = where[Op.and];\n\n  return andParts;\n}\n\nfunction combineWheresWithAnd(whereA, whereB) {\n  const unpackedA = unpackAnd(whereA);\n\n  if (unpackedA === undefined) {\n    return whereB;\n  }\n\n  const unpackedB = unpackAnd(whereB);\n\n  if (unpackedB === undefined) {\n    return whereA;\n  }\n\n  return {\n    [Op.and]: _.flatten([unpackedA, unpackedB])\n  };\n}\n\nObject.assign(Model, associationsMixin);\nHooks.applyTo(Model, true);\n\nmodule.exports = Model;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,SAAS,QAAQ;AACvB,MAAM,IAAI,QAAQ;AAClB,MAAM,SAAS,QAAQ;AAEvB,MAAM,QAAQ,QAAQ;AACtB,MAAM,EAAE,WAAW,QAAQ;AAC3B,MAAM,YAAY,QAAQ;AAC1B,MAAM,gBAAgB,QAAQ;AAC9B,MAAM,oBAAoB,QAAQ;AAClC,MAAM,aAAa,QAAQ;AAC3B,MAAM,kBAAkB,QAAQ;AAChC,MAAM,cAAc,QAAQ;AAC5B,MAAM,UAAU,QAAQ;AACxB,MAAM,YAAY,QAAQ;AAC1B,MAAM,QAAQ,QAAQ;AACtB,MAAM,oBAAoB,QAAQ;AAClC,MAAM,KAAK,QAAQ;AACnB,MAAM,EAAE,wBAAwB,QAAQ;AAMxC,MAAM,qBAAqB,oBAAI,IAAI;AAAA,EAAC;AAAA,EAAS;AAAA,EAAc;AAAA,EAAY;AAAA,EAAW;AAAA,EAAS;AAAA,EAAS;AAAA,EAClG;AAAA,EAAe;AAAA,EAAQ;AAAA,EAAO;AAAA,EAAW;AAAA,EAAa;AAAA,EAAU;AAAA,EAAc;AAAA,EAAiB;AAAA,EAC/F;AAAA,EAAS;AAAA,EAAS;AAAA,EAAW;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAS;AAAA,EAC9F;AAAA;AAGF,MAAM,sBAAsB,CAAC,WAAW,cAAc,sBAAsB,SAAS,SAAS,SAAS,UAAU,SAAS,SAAS;AAqBnI,YAAY;AAAA,aACC,iBAAiB;AAC1B,WAAO,KAAK,UAAU;AAAA;AAAA,aAGb,iBAAiB;AAC1B,WAAO,KAAK,eAAe;AAAA;AAAA,MAazB,YAAY;AACd,WAAO,KAAK,YAAY;AAAA;AAAA,EAY1B,YAAY,SAAS,IAAI,UAAU,IAAI;AACrC,QAAI,CAAC,KAAK,YAAY,+BAA+B;AACnD,WAAK,YAAY,gCAAgC;AAMjD,iBAAW,MAAM;AACf,cAAM,wBAAwB;AAC9B,mBAAW,OAAO,OAAO,KAAK,KAAK,YAAY,yBAAyB;AACtE,cAAI,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM;AACnD,kCAAsB,KAAK;AAAA;AAAA;AAI/B,YAAI,sBAAsB,SAAS,GAAG;AACpC,iBAAO,KAAK,SAAS,KAAK,UAAU,KAAK,YAAY,4DAA4D,sBAAsB,IAAI,UAAQ,KAAK,UAAU,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA,SAI/K;AAAA;AAGL,cAAU;AAAA,MACR,aAAa;AAAA,MACb,SAAS,KAAK,YAAY;AAAA,MAC1B,kBAAkB,KAAK,YAAY;AAAA,OAChC;AAGL,QAAI,QAAQ,YAAY;AACtB,cAAQ,aAAa,QAAQ,WAAW,IAAI,eAAa,MAAM,QAAQ,aAAa,UAAU,KAAK;AAAA;AAGrG,QAAI,CAAC,QAAQ,kBAAkB;AAC7B,WAAK,YAAY,iBAAiB,SAAS,KAAK;AAChD,UAAI,QAAQ,SAAS;AACnB,aAAK,YAAY,kBAAkB;AACnC,aAAK,YAAY,0BAA0B;AAAA;AAAA;AAI/C,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,SAAK,SAAS;AACd,SAAK,WAAW,oBAAI;AACpB,SAAK,WAAW;AAQhB,SAAK,cAAc,QAAQ;AAE3B,SAAK,YAAY,QAAQ;AAAA;AAAA,EAG3B,YAAY,QAAQ,SAAS;AAC3B,QAAI;AACJ,QAAI;AAEJ,aAAS,mBAAK;AAEd,QAAI,QAAQ,aAAa;AACvB,iBAAW;AAEX,UAAI,KAAK,YAAY,mBAAmB;AACtC,mBAAW,EAAE,UAAU,KAAK,YAAY,gBAAgB,aAAW;AACjE,gBAAM,QAAQ;AACd,iBAAO,SAAS,iBAAiB,MAAM,kBAAkB,QAAQ,EAAE,UAAU;AAAA;AAAA;AAOjF,UAAI,KAAK,YAAY,qBAAqB,QAAQ;AAChD,aAAK,YAAY,qBAAqB,QAAQ,yBAAuB;AACnE,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,sBAAsB;AACxE,qBAAS,uBAAuB;AAAA;AAAA;AAAA;AAKtC,UAAI,KAAK,YAAY,qBAAqB,aAAa,SAAS,KAAK,YAAY,qBAAqB,YAAY;AAChH,aAAK,WAAW,KAAK,YAAY,qBAAqB,aAAa,MAAM,eAAe,SAAS,KAAK,YAAY,qBAAqB,YAAY,KAAK,UAAU,QAAQ;AAC1K,eAAO,SAAS,KAAK,YAAY,qBAAqB;AAAA;AAGxD,UAAI,KAAK,YAAY,qBAAqB,aAAa,SAAS,KAAK,YAAY,qBAAqB,YAAY;AAChH,aAAK,WAAW,KAAK,YAAY,qBAAqB,aAAa,MAAM,eAAe,SAAS,KAAK,YAAY,qBAAqB,YAAY,KAAK,UAAU,QAAQ;AAC1K,eAAO,SAAS,KAAK,YAAY,qBAAqB;AAAA;AAGxD,UAAI,KAAK,YAAY,qBAAqB,aAAa,SAAS,KAAK,YAAY,qBAAqB,YAAY;AAChH,aAAK,WAAW,KAAK,YAAY,qBAAqB,aAAa,MAAM,eAAe,SAAS,KAAK,YAAY,qBAAqB,YAAY,KAAK,UAAU,QAAQ;AAC1K,eAAO,SAAS,KAAK,YAAY,qBAAqB;AAAA;AAGxD,WAAK,OAAO,UAAU;AACpB,YAAI,OAAO,SAAS,QAAW;AAC7B,eAAK,IAAI,KAAK,MAAM,eAAe,SAAS,MAAM,KAAK,UAAU,QAAQ,UAAU,EAAE,KAAK;AAC1F,iBAAO,OAAO;AAAA;AAAA;AAAA;AAKpB,SAAK,IAAI,QAAQ;AAAA;AAAA,SAIZ,gBAAgB,OAAO,UAAU,IAAI;AAI1C,QAAI,QAAQ,SAAS;AACnB,iBAAW,WAAW,QAAQ,SAAS;AACrC,aAAK,gBAAgB,QAAQ,OAAO;AAAA;AAAA;AAKxC,QAAI,EAAE,IAAI,SAAS,qCAAqC;AACtD,YAAM,eAAe,EAAE,IAAI,SAAS;AACpC,UAAI,cAAc;AAChB,gBAAQ,aAAa,UAAU,KAAK,gBAAgB,cAAc,QAAQ,aAAa;AAAA;AAAA;AAI3F,QAAI,CAAC,MAAM,QAAQ,cAAc,CAAC,MAAM,QAAQ,YAAY,QAAQ,aAAa,OAAO;AAEtF,aAAO;AAAA;AAGT,UAAM,eAAe,MAAM,qBAAqB;AAChD,UAAM,qBAAqB,MAAM,cAAc;AAC/C,UAAM,kBAAkB;AAExB,QAAI,wBAAwB,OAAO,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,mBAAmB,eAAe;AAEzI,4BAAwB,yBAAyB;AAAA,OAC9C,GAAG,KAAK;AAAA;AAGX,oBAAgB,mBAAmB,SAAS,gBAAgB;AAE5D,QAAI,MAAM,aAAa,QAAQ,QAAQ;AACrC,cAAQ,QAAQ;AAAA,WACX;AACL,cAAQ,QAAQ,GAAG,GAAG,MAAM,CAAC,iBAAiB,QAAQ;AAAA;AAGxD,WAAO;AAAA;AAAA,SAGF,wBAAwB;AAC7B,UAAM,OAAO;AACb,QAAI,OAAO;AAIX,QAAI,CAAC,EAAE,KAAK,KAAK,eAAe,eAAe;AAC7C,UAAI,QAAQ,KAAK,eAAe;AAE9B,cAAM,IAAI,MAAM,wDAAwD,KAAK;AAAA;AAG/E,aAAO;AAAA,QACL,IAAI;AAAA,UACF,MAAM,IAAI,UAAU;AAAA,UACpB,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,gBAAgB;AAAA;AAAA;AAAA;AAKtB,QAAI,KAAK,qBAAqB,WAAW;AACvC,WAAK,KAAK,qBAAqB,aAAa;AAAA,QAC1C,MAAM,UAAU;AAAA,QAChB,WAAW;AAAA,QACX,gBAAgB;AAAA;AAAA;AAIpB,QAAI,KAAK,qBAAqB,WAAW;AACvC,WAAK,KAAK,qBAAqB,aAAa;AAAA,QAC1C,MAAM,UAAU;AAAA,QAChB,WAAW;AAAA,QACX,gBAAgB;AAAA;AAAA;AAIpB,QAAI,KAAK,qBAAqB,WAAW;AACvC,WAAK,KAAK,qBAAqB,aAAa;AAAA,QAC1C,MAAM,UAAU;AAAA,QAChB,gBAAgB;AAAA;AAAA;AAIpB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,KAAK,qBAAqB;AAAA,QAC7B,MAAM,UAAU;AAAA,QAChB,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA;AAAA;AAIpB,UAAM,mBAAmB,kCACpB,OACA,KAAK;AAEV,MAAE,KAAK,MAAM,CAAC,OAAO,SAAS;AAC5B,UAAI,iBAAiB,UAAU,QAAW;AACxC,yBAAiB,QAAQ;AAAA;AAAA;AAI7B,SAAK,gBAAgB;AAErB,QAAI,CAAC,OAAO,KAAK,KAAK,aAAa,QAAQ;AACzC,WAAK,YAAY,KAAK,KAAK,cAAc;AAAA;AAAA;AAAA,SAStC,gBAAgB;AACrB,WAAO,KAAK;AAAA;AAAA,SAGP,8BAA8B;AACnC,SAAK,yBAAyB;AAE9B,eAAW,QAAQ,KAAK,eAAe;AACrC,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,eAAe,OAAO;AAClE,cAAM,aAAa,KAAK,cAAc;AACtC,YAAI,cAAc,WAAW,eAAe;AAC1C,cAAI,KAAK,wBAAwB;AAC/B,kBAAM,IAAI,MAAM;AAAA;AAElB,eAAK,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,SAM/B,iBAAiB,SAAS,MAAM;AACrC,QAAI,CAAC,QAAQ;AAAS;AAGtB,QAAI,CAAC,MAAM,QAAQ,QAAQ,UAAU;AACnC,cAAQ,UAAU,CAAC,QAAQ;AAAA,eAClB,CAAC,QAAQ,QAAQ,QAAQ;AAClC,aAAO,QAAQ;AACf;AAAA;AAIF,YAAQ,UAAU,QAAQ,QAAQ,IAAI,aAAW,KAAK,gBAAgB,SAAS;AAAA;AAAA,SAG1E,4BAA4B,SAAS,MAAM;AAChD,QAAI,QAAQ,OAAO,YAAY,UAAU;AACvC,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,cAAc,UAAU;AACrE,cAAM,IAAI,MAAM,2BAA2B,8BAA8B,KAAK;AAAA;AAEhF,aAAO,KAAK,aAAa;AAAA;AAE3B,WAAO;AAAA;AAAA,SAGF,gBAAgB,SAAS,MAAM;AACpC,QAAI,SAAS;AACX,UAAI;AAEJ,UAAI,QAAQ;AAAS,eAAO;AAE5B,gBAAU,KAAK,4BAA4B,SAAS;AAEpD,UAAI,mBAAmB,aAAa;AAClC,YAAI,QAAQ,QAAQ,OAAO,SAAS,KAAK,MAAM;AAC7C,kBAAQ,QAAQ;AAAA,eACX;AACL,kBAAQ,QAAQ;AAAA;AAGlB,eAAO,EAAE,OAAO,aAAa,SAAS,IAAI,QAAQ;AAAA;AAGpD,UAAI,QAAQ,aAAa,QAAQ,qBAAqB,OAAO;AAC3D,eAAO,EAAE,OAAO;AAAA;AAGlB,UAAI,EAAE,cAAc,UAAU;AAC5B,YAAI,QAAQ,aAAa;AACvB,kBAAQ,cAAc,KAAK,4BAA4B,QAAQ,aAAa;AAE5E,cAAI,QAAQ,QAAQ,YAAY,OAAO,SAAS,KAAK,MAAM;AACzD,oBAAQ,QAAQ,YAAY;AAAA,iBACvB;AACL,oBAAQ,QAAQ,YAAY;AAAA;AAG9B,cAAI,CAAC,QAAQ;AAAO,oBAAQ,QAAQ;AACpC,cAAI,CAAC,QAAQ;AAAI,oBAAQ,KAAK,QAAQ,YAAY;AAElD,eAAK,iBAAiB,SAAS;AAC/B,iBAAO;AAAA;AAGT,YAAI,QAAQ,OAAO;AACjB,eAAK,iBAAiB,SAAS,QAAQ;AACvC,iBAAO;AAAA;AAGT,YAAI,QAAQ,KAAK;AACf,eAAK,iBAAiB;AACtB,iBAAO;AAAA;AAAA;AAAA;AAKb,UAAM,IAAI,MAAM;AAAA;AAAA,SAGX,yBAAyB,UAAU,SAAS;AAEjD,QAAI,MAAM,QAAQ;AAClB,WAAO,QAAQ;AAEf,QAAI,QAAQ,MAAM;AAChB,UAAI,CAAC,MAAM,QAAQ,MAAM;AACvB,cAAM,CAAC;AAAA;AAGT,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,KAAK,CAAC,aAAa;AAAA,QACnB,KAAK,CAAC,UAAU;AAAA,QAChB,MAAM,CAAC;AAAA;AAGT,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,OAAO,IAAI;AACjB,YAAI,SAAS,OAAO;AAClB,gBAAM;AACN;AAAA;AAGF,cAAM,QAAQ,WAAW;AACzB,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,gBAAgB,kBAAkB,gBAAgB;AAAA;AAG9D,YAAI,UAAU,MAAM;AAElB,cAAI,OAAO,GAAG;AACd;AACA,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,CAAC,IAAI,SAAS,MAAM,KAAK;AAC3B,kBAAI,QAAQ,MAAM;AAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAQV,UAAM,SAAS,QAAQ;AACvB,QAAI,QAAQ;AACV,aAAO,QAAQ;AAEf,UAAI,CAAC,QAAQ,SAAS;AACpB,gBAAQ,UAAU;AAAA,iBACT,CAAC,MAAM,QAAQ,QAAQ,UAAU;AAC1C,gBAAQ,UAAU,CAAC,QAAQ;AAAA;AAAA;AAI/B,UAAM,OAAO;AACb,IAAC,yBAAwB,QAAQ,WAAU;AACzC,QAAE,QAAQ,OAAO,cAAc,iBAAe;AAC5C,YAAI,QAAQ,QAAQ,CAAC,IAAI,SAAS,YAAY,kBAAkB;AAC9D;AAAA;AAIF,cAAM,QAAQ,YAAY;AAC1B,cAAM,KAAK,YAAY,QAAQ;AAE/B,cAAM,YAAY,EAAE;AACpB,YAAI,IAAI;AAEN,oBAAU,KAAK;AAAA;AAGjB,YAAI,EAAE,KAAK,WAAU,YAAY;AAC/B;AAAA;AAIF,YAAI,UAAU,KAAK,SAAS,QAAQ;AAClC;AAAA;AAEF,aAAK,KAAK;AAGV,cAAM,cAAc,MAAM,UAAU;AACpC,oBAAY,QAAQ;AACpB,YAAI,IAAI;AACN,sBAAY,KAAK;AAAA;AAEnB,kBAAS,KAAK;AAGd,YAAI,QAAQ;AACV,yBAAe,OAAO,YAAY;AAClC,cAAI,YAAY,QAAQ,WAAW;AAAG,mBAAO,YAAY;AAAA;AAAA;AAG7D,WAAK;AAAA,OACJ,MAAM;AAAA;AAAA,SAGJ,0BAA0B,SAAS,YAAY;AACpD,QAAI,CAAC,QAAQ;AAAO,cAAQ,QAAQ;AAEpC,iBAAa,cAAc;AAC3B,YAAQ,eAAe;AACvB,YAAQ,aAAa;AAGrB,YAAQ,uBAAuB;AAC/B,YAAQ,sBAAsB;AAE9B,QAAI,CAAC,QAAQ,QAAQ;AACnB,cAAQ,WAAW,QAAQ;AAC3B,cAAQ,WAAW,QAAQ;AAAA;AAG7B,YAAQ,UAAU,QAAQ,QAAQ,IAAI,aAAW;AAC/C,gBAAU,KAAK,gBAAgB;AAC/B,cAAQ,SAAS;AACjB,cAAQ,WAAW,QAAQ;AAE3B,WAAK,yBAAyB,KAAK,QAAQ,OAAO,SAAS,YAAY;AAEvE,UAAI,QAAQ,gBAAgB,QAAW;AACrC,gBAAQ,cAAc,QAAQ,YAAY;AAAA;AAG5C,cAAQ,iBAAiB,QAAQ,kBAAkB,QAAQ;AAC3D,cAAQ,cAAc,QAAQ,eAAe,QAAQ;AAErD,cAAQ,iBAAiB,QAAQ,kBAAkB,QAAQ;AAC3D,cAAQ,cAAc,QAAQ,eAAe,QAAQ;AAErD,cAAQ,WAAW,QAAQ,YAAY,QAAQ,YAAY,CAAC,CAAC,QAAQ;AACrE,aAAO;AAAA;AAGT,eAAW,WAAW,QAAQ,SAAS;AACrC,cAAQ,iBAAiB,QAAQ,kBAAkB,CAAC,CAAC,QAAQ;AAC7D,cAAQ,oBAAoB,QAAQ,qBAAqB,CAAC,CAAC,QAAQ;AAEnE,UAAI,QAAQ,aAAa,SAAS,QAAQ,kBAAkB,QAAQ,UAAU;AAC5E,YAAI,QAAQ,aAAa;AACvB,kBAAQ,WAAW,QAAQ,YAAY;AACvC,kBAAQ,iBAAiB,QAAQ;AAAA,eAC5B;AACL,kBAAQ,WAAW,QAAQ;AAC3B,kBAAQ,iBAAiB;AAAA;AAAA,aAEtB;AACL,gBAAQ,WAAW,QAAQ,YAAY;AACvC,YAAI,QAAQ,aAAa;AACvB,kBAAQ,iBAAiB,QAAQ;AAAA,eAC5B;AACL,kBAAQ,iBAAiB;AACzB,kBAAQ,WAAW,QAAQ,YAAY,QAAQ,qBAAqB,QAAQ,eAAe,CAAC,QAAQ;AAAA;AAAA;AAIxG,cAAQ,WAAW,QAAQ,MAAM;AACjC,cAAQ,aAAa,KAAK,QAAQ;AAGlC,UAAI,QAAQ,aAAa,QAAQ,SAAS,QAAQ,aAAa,UAAa,QAAQ,UAAU;AAC5F,YAAI,QAAQ,UAAU;AACpB,kBAAQ,WAAW,QAAQ;AAAA,mBAClB,QAAQ,gBAAgB;AACjC,kBAAQ,WAAW;AAAA;AAAA;AAKvB,cAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,mBAAmB,CAAC,CAAC,QAAQ;AAC1F,cAAQ,qBAAqB,QAAQ,sBAAsB,QAAQ,sBAAsB,CAAC,CAAC,QAAQ;AAEnG,UAAI,QAAQ,YAAY,sBAAsB,QAAQ,qBAAqB;AACzE,gBAAQ,sBAAsB;AAAA;AAEhC,UAAI,QAAQ,YAAY,uBAAuB,QAAQ,sBAAsB;AAC3E,gBAAQ,uBAAuB;AAAA;AAAA;AAInC,QAAI,QAAQ,aAAa,QAAQ,SAAS,QAAQ,aAAa,QAAW;AACxE,cAAQ,WAAW;AAAA;AAErB,WAAO;AAAA;AAAA,SAGF,yBAAyB,SAAS,YAAY,SAAS;AAC5D,eAAW,QAAQ,MAAM,kBAAkB;AAE3C,QAAI,QAAQ,cAAc,CAAC,QAAQ,KAAK;AACtC,cAAQ,MAAM,kBAAkB;AAEhC,cAAQ,qBAAqB,QAAQ,MAAM,kCAAkC,QAAQ;AAErF,gBAAU,MAAM,iBAAiB,SAAS,QAAQ;AAElD,UAAI,QAAQ,WAAW,QAAQ;AAC7B,UAAE,KAAK,QAAQ,MAAM,aAAa,CAAC,MAAM,QAAQ;AAE/C,cAAI,CAAC,QAAQ,WAAW,KAAK,iBAAe;AAC1C,gBAAI,KAAK,UAAU,KAAK;AACtB,qBAAO,MAAM,QAAQ,gBAAgB,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO;AAAA;AAE3F,mBAAO,gBAAgB;AAAA,cACrB;AACF,oBAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,WAI5B;AACL,gBAAU,MAAM,iBAAiB,SAAS,QAAQ;AAAA;AAIpD,QAAI,QAAQ,SAAS;AACnB,UAAI,CAAC,QAAQ,YAAY;AACvB,gBAAQ,aAAa,OAAO,KAAK,QAAQ,MAAM;AAAA;AAEjD,aAAO,MAAM,iBAAiB,SAAS,QAAQ;AAAA;AAIjD,UAAM,cAAc,QAAQ,eAAe,KAAK,wBAAwB,QAAQ,OAAO,QAAQ;AAE/F,YAAQ,cAAc;AACtB,YAAQ,KAAK,YAAY;AAGzB,QAAI,QAAQ,YAAY,WAAW,OAAO,QAAQ,YAAY,QAAQ,WAAW,QAAQ,YAAY,QAAQ,OAAO;AAClH,UAAI,CAAC,QAAQ;AAAS,gBAAQ,UAAU;AACxC,YAAM,UAAU,QAAQ,YAAY;AAEpC,cAAQ,UAAU,EAAE,SAAS,QAAQ,WAAW,IAAI;AAAA,QAClD,OAAO,QAAQ;AAAA,QACf,IAAI,QAAQ,MAAM;AAAA,QAClB,aAAa;AAAA,UACX,qBAAqB;AAAA;AAAA,QAEvB,SAAS;AAAA,QACT,QAAQ;AAAA;AAIV,UAAI,QAAQ,OAAO;AACjB,gBAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,GAAG,GAAG,MAAM,CAAC,QAAQ,QAAQ,OAAO,QAAQ,WAAW,QAAQ;AAAA;AAGjH,cAAQ,QAAQ,KAAK,QAAQ;AAC7B,iBAAW,QAAQ,aAAa;AAAA;AAIlC,QAAI;AACJ,QAAI,QAAQ,MAAM,WAAW,MAAM;AAEjC,cAAQ,QAAQ;AAAA,WACX;AAEL,cAAQ,QAAQ,YAAY,OAAO,SAAS,QAAQ,MAAM,OAAO,QAAQ,YAAY,SAAS,QAAQ,YAAY;AAAA;AAGpH,UAAM,aAAa;AAGnB,QAAI,CAAC,QAAQ,YAAY;AACvB,cAAQ,aAAa,OAAO,KAAK,QAAQ,MAAM;AAAA;AAGjD,cAAU,MAAM,iBAAiB,SAAS,QAAQ;AAElD,QAAI,QAAQ,aAAa,QAAW;AAClC,cAAQ,WAAW,CAAC,CAAC,QAAQ;AAAA;AAG/B,QAAI,QAAQ,YAAY,OAAO;AAC7B,cAAQ,QAAQ,QAAQ,QAAQ,GAAG,GAAG,MAAM,CAAC,QAAQ,OAAO,QAAQ,YAAY,WAAW,QAAQ,YAAY;AAAA;AAGjH,QAAI,QAAQ,SAAS,QAAQ,aAAa,QAAW;AACnD,cAAQ,WAAW;AAAA;AAGrB,QAAI,QAAQ,aAAa,MAAM;AAC7B,UAAI,CAAE,SAAQ,uBAAuB,UAAU;AAC7C,cAAM,IAAI,MAAM;AAAA;AAGlB,cAAQ,cAAc;AAEtB,UACE,QAAQ,cACL,QAAQ,WAAW,UACnB,CAAC,EAAE,aAAa,QAAQ,YAAY,GAAG,SAAS,YAAY,YAC/D;AACA,gBAAQ,WAAW,KAAK,YAAY;AAAA;AAGtC,UACE,QAAQ,cACL,QAAQ,WAAW,UACnB,CAAC,EAAE,aAAa,QAAQ,YAAY,GAAG,SAAS,YAAY,aAC/D;AACA,gBAAQ,WAAW,KAAK,YAAY;AAAA;AAAA;AAKxC,QAAI,OAAO,UAAU,eAAe,KAAK,SAAS,YAAY;AAC5D,WAAK,0BAA0B,KAAK,QAAQ,OAAO,SAAS;AAAA;AAG9D,WAAO;AAAA;AAAA,SAGF,wBAAwB,aAAa,aAAa;AACvD,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,cAAc;AAClB,QAAI,aAAa,WAAW,GAAG;AAC7B,YAAM,IAAI,gBAAgB,kBAAkB,GAAG,YAAY,6BAA6B,KAAK;AAAA;AAE/F,QAAI,aAAa,WAAW,GAAG;AAC7B,oBAAc,KAAK,uBAAuB,aAAa;AACvD,UAAI,aAAa;AACf,eAAO;AAAA;AAET,UAAI,aAAa;AACf,cAAM,kBAAkB,KAAK,gBAAgB,aAAa,IAAI,kBAAe,aAAY;AACzF,cAAM,IAAI,gBAAgB,kBAAkB,GAAG,YAAY,yBAAyB,KAAK,kDAC1D,kFAAkF,gBAAgB,KAAK;AAAA;AAExI,YAAM,IAAI,gBAAgB,kBAAkB,GAAG,YAAY,yBAAyB,KAAK;AAAA;AAG3F,kBAAc,KAAK,uBAAuB,aAAa;AACvD,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,gBAAgB,kBAAkB,GAAG,YAAY,yBAAyB,KAAK;AAAA;AAG3F,WAAO;AAAA;AAAA,SAIF,kBAAkB,SAAS;AAChC,UAAM,WAAW,QAAQ;AACzB,QAAI,CAAC,UAAU;AACb;AAAA;AAGF,aAAS,QAAQ,GAAG,QAAQ,SAAS,QAAQ,SAAS;AACpD,YAAM,UAAU,SAAS;AAEzB,UAAI,QAAQ,KAAK;AACf,iBAAS,OAAO,OAAO;AACvB;AAEA,aAAK,yBAAyB,UAAU;AAAA;AAAA;AAI5C,aAAS,QAAQ,aAAW;AAC1B,WAAK,kBAAkB,KAAK,QAAQ,OAAO;AAAA;AAAA;AAAA,SAIxC,cAAc,OAAO;AAC1B,QAAI,CAAC,MAAM,QAAQ;AACjB,YAAM,IAAI,MAAM;AAAA;AAGlB,YAAQ,EAAE,SAAS,OAAO;AAAA,MACxB,MAAM;AAAA,MACN,QAAQ;AAAA;AAGV,QAAI,MAAM,QAAQ,MAAM,KAAK,kBAAkB,UAAU;AACvD,YAAM,SAAS;AACf,aAAO,MAAM;AAAA;AAGf,WAAO;AAAA;AAAA,SAIF,cAAc,SAAS;AAC5B,QAAI,CAAC,QAAQ;AAAS;AAEtB,YAAQ,UAAU,EAAE,QAAQ,SACzB,QAAQ,aAAW,GAAG,QAAQ,SAAS,QAAQ,MAAM,QAAQ,QAAQ,MACrE,IAAI,cAAY,KAAK,eAAe,GAAG,WACvC;AAAA;AAAA,SAGE,cAAc,MAAM;AACzB,MAAE,WAAW,GAAG;AAChB,SAAK,iBAAiB,KAAK,IAAI;AAC/B,SAAK,cAAc,KAAK;AACxB,WAAO,KAAK;AAAA;AAAA,SAGP,eAAe,UAAU,UAAU,KAAK;AAC7C,QAAI,MAAM,QAAQ,aAAa,MAAM,QAAQ,WAAW;AACtD,aAAO,EAAE,MAAM,UAAU;AAAA;AAG3B,QAAI,CAAC,SAAS,UAAU,SAAS,MAAM;AACrC,UAAI,KAAK,WAAW,KAAK,QAAQ,uBAAuB,OAAO;AAC7D,eAAO,qBAAqB,UAAU;AAAA;AAGxC,UAAI,oBAAoB,MAAM,iBAAiB;AAC7C,mBAAW,GAAG,GAAG,MAAM;AAAA;AAGzB,UAAI,EAAE,cAAc,aAAa,EAAE,cAAc,WAAW;AAC1D,eAAO,OAAO,OAAO,UAAU;AAAA;AAAA,eAExB,QAAQ,gBAAgB,EAAE,cAAc,aAAa,EAAE,cAAc,WAAW;AACzF,aAAO,EAAE,WAAW,UAAU,UAAU,CAAC,WAAU,cAAa;AAC9D,YAAI,MAAM,QAAQ,cAAa,MAAM,QAAQ,YAAW;AACtD,iBAAO,EAAE,MAAM,WAAU;AAAA;AAAA;AAAA;AAO/B,QAAI,UAAU;AACZ,aAAO,MAAM,UAAU,UAAU;AAAA;AAEnC,WAAO,aAAa,SAAY,WAAW;AAAA;AAAA,SAGtC,kBAAkB,MAAM;AAC7B,WAAO,KAAK,WAAW,GAAG,MAAM,KAAK,eAAe,KAAK;AAAA;AAAA,SAGpD,iBAAiB,QAAQ,MAAM;AACpC,WAAO,KAAK,WAAW,QAAQ,MAAM,CAAC,UAAU,UAAU,QAAQ;AAChE,aAAO,KAAK,eAAe,UAAU,UAAU;AAAA;AAAA;AAAA,SAgG5C,KAAK,YAAY,UAAU,IAAI;AACpC,QAAI,CAAC,QAAQ,WAAW;AACtB,YAAM,IAAI,MAAM;AAAA;AAGlB,SAAK,YAAY,QAAQ;AAEzB,UAAM,gBAAgB,KAAK,UAAU;AAErC,cAAU,MAAM,MAAM,EAAE,UAAU,cAAc,SAAS;AAEzD,QAAI,CAAC,QAAQ,WAAW;AACtB,cAAQ,YAAY,KAAK;AAAA;AAG3B,cAAU,MAAM,MAAM;AAAA,MACpB,MAAM;AAAA,QACJ,QAAQ,MAAM,UAAU,QAAQ;AAAA,QAChC,UAAU,MAAM,YAAY,QAAQ;AAAA;AAAA,MAEtC,SAAS;AAAA,MACT,UAAU,cAAc;AAAA,MACxB,QAAQ,cAAc;AAAA,OACrB;AAEH,SAAK,UAAU,SAAS,gBAAgB,YAAY;AAEpD,QAAI,QAAQ,cAAc,KAAK,MAAM;AACnC,aAAO,eAAe,MAAM,QAAQ,EAAE,OAAO,QAAQ;AAAA;AAEvD,WAAO,QAAQ;AAEf,SAAK,UAAU;AAAA,MACb,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,UAAU;AAAA,MACV,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,QAAQ;AAAA,MACR,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,oBAAoB;AAAA,OACjB;AAIL,QAAI,KAAK,UAAU,UAAU,KAAK,OAAO;AACvC,WAAK,UAAU,aAAa,YAAY,KAAK,UAAU,aAAa,SAAS,KAAK;AAAA;AAGpF,SAAK,eAAe;AACpB,SAAK,YAAY,QAAQ;AAEzB,SAAK,cAAc,KAAK,QAAQ;AAEhC,QAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,WAAK,YAAY,KAAK,QAAQ,kBAAkB,KAAK,OAAO,MAAM,cAAc,MAAM,UAAU,KAAK,OAAO,KAAK;AAAA,WAC5G;AACL,WAAK,YAAY,KAAK,QAAQ;AAAA;AAGhC,SAAK,UAAU,KAAK,QAAQ;AAC5B,SAAK,mBAAmB,KAAK,QAAQ;AAGrC,MAAE,KAAK,QAAQ,UAAU,CAAC,WAAW,kBAAkB;AACrD,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,gBAAgB;AACnE,cAAM,IAAI,MAAM,6EAA6E,KAAK,gCAAgC;AAAA;AAGpI,UAAI,OAAO,cAAc,YAAY;AACnC,cAAM,IAAI,MAAM,4DAA4D,KAAK,oCAAoC;AAAA;AAAA;AAIzH,QAAI,CAAC,EAAE,SAAS,CAAC,OAAO,cAAc,KAAK,WAAW,KAAK,QAAQ,qBAAqB;AACtF,YAAM,IAAI,MAAM,iBAAiB,KAAK,WAAW,KAAK,QAAQ;AAAA;AAIhE,SAAK,gBAAgB,EAAE,UAAU,YAAY,CAAC,WAAW,SAAS;AAChE,kBAAY,KAAK,UAAU,mBAAmB;AAE9C,UAAI,UAAU,SAAS,QAAW;AAChC,cAAM,IAAI,MAAM,wCAAwC,KAAK,QAAQ;AAAA;AAGvE,UAAI,UAAU,cAAc,SAAS,EAAE,IAAI,WAAW,qBAAqB;AACzE,cAAM,IAAI,MAAM,2BAA2B,KAAK,QAAQ;AAAA;AAG1D,UAAI,EAAE,IAAI,WAAW,yCAAyC,OAAO;AACnE,kBAAU,WAAW,QAAQ,UAAU,WAAW,MAAM;AAAA;AAG1D,aAAO;AAAA;AAGT,UAAM,YAAY,KAAK;AACvB,SAAK,WAAW,KAAK,QAAQ,QAC1B,IAAI,WAAS,MAAM,UAAU,KAAK,cAAc,QAAQ;AAE3D,SAAK,cAAc;AACnB,SAAK,sBAAsB,oBAAI;AAC/B,SAAK,uBAAuB;AAG5B,QAAI,KAAK,QAAQ,YAAY;AAC3B,iBAAW,OAAO,CAAC,aAAa,aAAa,cAAc;AACzD,YAAI,CAAC,CAAC,aAAa,UAAU,WAAW,SAAS,OAAO,KAAK,QAAQ,OAAO;AAC1E,gBAAM,IAAI,MAAM,cAAc,kDAAkD,OAAO,KAAK,QAAQ;AAAA;AAEtG,YAAI,KAAK,QAAQ,SAAS,IAAI;AAC5B,gBAAM,IAAI,MAAM,cAAc;AAAA;AAAA;AAIlC,UAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,aAAK,qBAAqB,YACxB,OAAO,KAAK,QAAQ,cAAc,WAAW,KAAK,QAAQ,YAAY;AACxE,aAAK,oBAAoB,IAAI,KAAK,qBAAqB;AAAA;AAEzD,UAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,aAAK,qBAAqB,YACxB,OAAO,KAAK,QAAQ,cAAc,WAAW,KAAK,QAAQ,YAAY;AACxE,aAAK,oBAAoB,IAAI,KAAK,qBAAqB;AAAA;AAEzD,UAAI,KAAK,QAAQ,YAAY,KAAK,QAAQ,cAAc,OAAO;AAC7D,aAAK,qBAAqB,YACxB,OAAO,KAAK,QAAQ,cAAc,WAAW,KAAK,QAAQ,YAAY;AACxE,aAAK,oBAAoB,IAAI,KAAK,qBAAqB;AAAA;AAAA;AAK3D,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,oBAAoB,OAAO,KAAK,QAAQ,YAAY,WAAW,KAAK,QAAQ,UAAU;AAC3F,WAAK,oBAAoB,IAAI,KAAK;AAAA;AAGpC,SAAK,yBAAyB,KAAK,oBAAoB,OAAO;AAG9D,SAAK;AACL,SAAK;AACL,SAAK;AAEL,SAAK,SAAS,KAAK,QAAQ;AAC3B,SAAK,cAAc,CAAC;AAEpB,SAAK,UAAU,aAAa,SAAS;AACrC,SAAK,UAAU,SAAS,eAAe;AAEvC,WAAO;AAAA;AAAA,SAGF,oBAAoB;AACzB,UAAM,wBAAwB;AAE9B,SAAK,UAAU,iBAAiB;AAChC,SAAK,UAAU,iBAAiB;AAEhC,KAAC,OAAO,OAAO,QAAQ,UAAQ;AAC7B,YAAM,MAAM,GAAG;AACf,YAAM,QAAQ,mBAAK,KAAK,QAAQ;AAChC,YAAM,UAAU,SAAS,QAAQ,KAAK,UAAU,iBAAiB,KAAK,UAAU;AAEhF,QAAE,KAAK,OAAO,CAAC,QAAQ,cAAc;AACnC,gBAAQ,aAAa;AAErB,YAAI,SAAS,OAAO;AAClB,gBAAM,aAAa,WAAW;AAC5B,mBAAO,KAAK,IAAI;AAAA;AAAA;AAGpB,YAAI,SAAS,OAAO;AAClB,gBAAM,aAAa,SAAS,OAAO;AACjC,mBAAO,KAAK,IAAI,WAAW;AAAA;AAAA;AAAA;AAKjC,QAAE,KAAK,KAAK,eAAe,CAAC,SAAS,cAAc;AACjD,YAAI,OAAO,UAAU,eAAe,KAAK,SAAS,OAAO;AACvD,kBAAQ,aAAa,QAAQ;AAAA;AAG/B,YAAI,SAAS,OAAO;AAClB,gBAAM,aAAa,WAAW;AAC5B,mBAAO,KAAK,IAAI;AAAA;AAAA;AAGpB,YAAI,SAAS,OAAO;AAClB,gBAAM,aAAa,SAAS,OAAO;AACjC,mBAAO,KAAK,IAAI,WAAW;AAAA;AAAA;AAAA;AAKjC,QAAE,KAAK,OAAO,CAAC,KAAK,SAAS;AAC3B,YAAI,CAAC,sBAAsB,OAAO;AAChC,gCAAsB,QAAQ;AAAA,YAC5B,cAAc;AAAA;AAAA;AAGlB,8BAAsB,MAAM,QAAQ;AAAA;AAAA;AAIxC,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAE3B,SAAK,wBAAwB;AAC7B,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,oBAAI;AAC3B,SAAK,qBAAqB,oBAAI;AAC9B,SAAK,iBAAiB;AACtB,SAAK,UAAU,aAAa;AAE5B,SAAK,wBAAwB;AAE7B,SAAK,cAAc;AACnB,SAAK,aAAa;AAElB,MAAE,KAAK,KAAK,eAAe,CAAC,YAAY,SAAS;AAC/C,iBAAW,OAAO,KAAK,UAAU,kBAAkB,WAAW;AAE9D,iBAAW,QAAQ;AACnB,iBAAW,YAAY;AACvB,iBAAW,kBAAkB;AAE7B,UAAI,WAAW,UAAU,QAAW;AAClC,mBAAW,QAAQ,MAAM,cAAc,MAAM,KAAK;AAAA;AAGpD,UAAI,WAAW,eAAe,MAAM;AAClC,aAAK,YAAY,QAAQ;AAAA;AAG3B,WAAK,sBAAsB,WAAW,SAAS;AAE/C,UAAI,WAAW,KAAK,WAAW;AAC7B,aAAK,oBAAoB,QAAQ,WAAW,KAAK;AAAA;AAGnD,UAAI,WAAW,KAAK,YAAY;AAC9B,aAAK,iBAAiB,QAAQ,WAAW,KAAK;AAAA;AAGhD,UAAI,WAAW,gBAAgB,UAAU,SAAS;AAChD,aAAK,wBAAwB;AAAA,iBACpB,WAAW,gBAAgB,UAAU,QAAQ,WAAW,gBAAgB,UAAU,UAAU;AACrG,aAAK,qBAAqB;AAAA,iBACjB,WAAW,gBAAgB,UAAU,MAAM;AACpD,aAAK,gBAAgB,IAAI;AAAA,iBAChB,WAAW,gBAAgB,UAAU,SAAS;AACvD,aAAK,mBAAmB,IAAI;AAAA;AAG9B,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,iBAAiB;AACpE,aAAK,eAAe,QAAQ,MAAM,MAAM,eAAe,WAAW,cAAc,KAAK,UAAU,QAAQ;AAAA;AAGzG,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,aAAa,WAAW,QAAQ;AACnF,YAAI;AACJ,YACE,OAAO,WAAW,WAAW,YAC7B,OAAO,UAAU,eAAe,KAAK,WAAW,QAAQ,SACxD;AACA,oBAAU,WAAW,OAAO;AAAA,mBACnB,OAAO,WAAW,WAAW,UAAU;AAChD,oBAAU,WAAW;AAAA,eAChB;AACL,oBAAU,GAAG,KAAK,aAAa;AAAA;AAGjC,cAAM,MAAM,KAAK,WAAW,YAAY,EAAE,QAAQ;AAElD,YAAI,OAAO,KAAK,WAAW;AAC3B,YAAI,MAAM,IAAI,OAAO,WAAW,OAAO,OAAO;AAC9C,YAAI,OAAO,WAAW;AACtB,YAAI,SAAS;AACb,YAAI,cAAc,WAAW,WAAW;AAExC,aAAK,WAAW,WAAW;AAAA;AAG7B,UAAI,OAAO,UAAU,eAAe,KAAK,YAAY,aAAa;AAChE,aAAK,UAAU,WAAW,QAAQ,WAAW;AAAA;AAG/C,UAAI,WAAW,UAAU,QAAQ,WAAW,gBAAgB,UAAU,OAAO;AAC3E,aAAK,SAAS,KACZ,MAAM,UACJ,KAAK,cAAc;AAAA,UACjB,QAAQ,CAAC,WAAW,SAAS;AAAA,UAC7B,OAAO;AAAA,YAET,KAAK;AAIT,eAAO,WAAW;AAAA;AAAA;AAKtB,SAAK,oBAAoB,EAAE,OAAO,KAAK,uBAAuB,CAAC,KAAK,OAAO,QAAQ;AACjF,UAAI,QAAQ,MAAM,WAAW;AAC3B,YAAI,OAAO,MAAM;AAAA;AAEnB,aAAO;AAAA,OACN;AAEH,SAAK,qBAAqB,CAAC,CAAC,KAAK,gBAAgB;AAEjD,SAAK,wBAAwB,CAAC,CAAC,KAAK,mBAAmB;AAEvD,SAAK,oBAAoB,CAAC,EAAE,QAAQ,KAAK;AAEzC,SAAK,kBAAkB,EAAE,OAAO,KAAK,eAAe,CAAC,IAAI,QAAQ,KAAK,mBAAmB,IAAI;AAE7F,SAAK,UAAU,oBAAoB,OAAO,KAAK,KAAK,UAAU,gBAAgB;AAC9E,SAAK,UAAU,oBAAoB,OAAO,KAAK,KAAK,UAAU,gBAAgB;AAE9E,eAAW,OAAO,OAAO,KAAK,wBAAwB;AACpD,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,MAAM;AAC9D,aAAK,UAAU,IAAI,wDAAwD;AAC3E;AAAA;AAEF,aAAO,eAAe,KAAK,WAAW,KAAK,sBAAsB;AAAA;AAGnE,SAAK,UAAU,gBAAgB,KAAK;AACpC,SAAK,UAAU,eAAe,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,UAAU,eAAe;AAGxG,SAAK,uBAAuB,OAAO,KAAK,KAAK;AAC7C,SAAK,sBAAsB,KAAK,qBAAqB;AACrD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,kBAAkB,KAAK,cAAc,KAAK,qBAAqB,SAAS,KAAK;AAAA;AAGpF,SAAK,kBAAkB,KAAK,qBAAqB,SAAS;AAC1D,SAAK,gBAAgB,SAAO,KAAK,qBAAqB,SAAS;AAE/D,SAAK,yBAAyB;AAAA;AAAA,SAQzB,gBAAgB,WAAW;AAChC,WAAO,KAAK,cAAc;AAC1B,SAAK;AAAA;AAAA,eAaM,KAAK,SAAS;AACzB,cAAU,kCAAK,KAAK,UAAY;AAChC,YAAQ,QAAQ,QAAQ,UAAU,SAAY,OAAO,CAAC,CAAC,QAAQ;AAE/D,UAAM,aAAa,KAAK;AACxB,UAAM,gBAAgB,KAAK;AAE3B,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,cAAc;AAAA;AAGpC,UAAM,YAAY,KAAK,aAAa;AAEpC,QAAI;AACJ,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,KAAK;AAChB,oBAAc;AAAA,WACT;AACL,oBAAc,MAAM,KAAK,eAAe,YAAY,WAAW;AAAA;AAGjE,QAAI,CAAC,aAAa;AAChB,YAAM,KAAK,eAAe,YAAY,WAAW,YAAY,SAAS;AAAA,WACjE;AAEL,YAAM,KAAK,eAAe,YAAY,WAAW,YAAY,SAAS;AAAA;AAGxE,QAAI,eAAe,QAAQ,OAAO;AAChC,YAAM,aAAa,MAAM,QAAQ,IAAI;AAAA,QACnC,KAAK,eAAe,cAAc,WAAW;AAAA,QAC7C,KAAK,eAAe,gCAAgC,WAAW;AAAA;AAGjE,YAAM,UAAU,WAAW;AAE3B,YAAM,uBAAuB,WAAW;AACxC,YAAM,qBAAqB;AAE3B,iBAAW,cAAc,YAAY;AACnC,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY;AAAa;AACnE,YAAI,CAAC,QAAQ,eAAe,CAAC,QAAQ,WAAW,YAAY,QAAQ;AAClE,gBAAM,KAAK,eAAe,UAAU,WAAW,WAAW,YAAY,SAAS,YAAY,WAAW,aAAa;AAAA;AAAA;AAIvH,UAAI,QAAQ,UAAU,QAAQ,OAAO,QAAQ,UAAU,YAAY,QAAQ,MAAM,SAAS,OAAO;AAC/F,mBAAW,cAAc,SAAS;AAChC,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS;AAAa;AAChE,gBAAM,mBAAmB,cAAc;AACvC,cAAI,CAAC,kBAAkB;AACrB,kBAAM,KAAK,eAAe,aAAa,WAAW,YAAY;AAC9D;AAAA;AAEF,cAAI,iBAAiB;AAAY;AAEjC,gBAAM,aAAa,iBAAiB;AACpC,cAAI,iBAAiB,YAAY;AAC/B,kBAAM,WAAW,KAAK,UAAU,OAAO;AACvC,kBAAM,SAAS,KAAK,UAAU,OAAO;AAErC,uBAAW,uBAAuB,sBAAsB;AACtD,oBAAM,iBAAiB,oBAAoB;AAC3C,kBAAI,CAAC,CAAC,kBACD,oBAAoB,iBAAiB,YACpC,UAAS,oBAAoB,gBAAgB,SAAS,SACvD,oBAAoB,wBAAwB,WAAW,SACvD,oBAAoB,yBAAyB,WAAW,OACvD,UAAS,oBAAoB,0BAA0B,SAAS,SACjE,CAAC,mBAAmB,iBAAiB;AAExC,sBAAM,KAAK,eAAe,iBAAiB,WAAW,gBAAgB;AACtE,mCAAmB,kBAAkB;AAAA;AAAA;AAAA;AAK3C,gBAAM,KAAK,eAAe,aAAa,WAAW,YAAY,kBAAkB;AAAA;AAAA;AAAA;AAKtF,UAAM,kBAAkB,MAAM,KAAK,eAAe,UAAU,WAAW;AACvE,UAAM,iBAAiB,KAAK,SAAS,OAAO,WAC1C,CAAC,gBAAgB,KAAK,WAAS,MAAM,SAAS,MAAM,OACpD,KAAK,CAAC,QAAQ,WAAW;AACzB,UAAI,KAAK,UAAU,QAAQ,YAAY,YAAY;AAEjD,YAAI,OAAO,iBAAiB;AAAM,iBAAO;AACzC,YAAI,OAAO,iBAAiB;AAAM,iBAAO;AAAA;AAG3C,aAAO;AAAA;AAGT,eAAW,SAAS,gBAAgB;AAClC,YAAM,KAAK,eAAe,SAAS,WAAW,kCAAK,UAAY;AAAA;AAGjE,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,aAAa;AAAA;AAGnC,WAAO;AAAA;AAAA,eAaI,KAAK,SAAS;AACzB,WAAO,MAAM,KAAK,eAAe,UAAU,KAAK,aAAa,UAAU;AAAA;AAAA,eAG5D,WAAW,QAAQ;AAC9B,WAAO,MAAM,KAAK,eAAe,WAAW;AAAA;AAAA,SAwBvC,OAAO,QAAQ,SAAS;AAE7B,UAAM,QAAQ,cAAc,KAAK;AAAA;AACjC,WAAO,eAAe,OAAO,QAAQ,EAAE,OAAO,KAAK;AAEnD,UAAM,UAAU;AAEhB,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,UAAU;AAC/B,cAAM,mBAAmB;AAAA,iBAChB,QAAQ,iBAAiB;AAClC,cAAM,mBAAmB,QAAQ;AAAA;AAAA;AAIrC,WAAO;AAAA;AAAA,SASF,eAAe;AACpB,WAAO,KAAK,eAAe,UAAU;AAAA;AAAA,SAQhC,WAAW;AAChB,WAAO,KAAK;AAAA;AAAA,SAaP,SAAS,MAAM,OAAO,SAAS;AACpC,cAAU,iBAAE,UAAU,SAAU;AAEhC,QAAK,UAAS,kBAAkB,OAAO,KAAK,KAAK,QAAQ,cAAc,SAAS,KAAK,QAAQ,KAAK,QAAQ,WAAW,QAAQ,aAAa,OAAO;AAC/I,YAAM,IAAI,MAAM,aAAa;AAAA;AAG/B,QAAI,SAAS,gBAAgB;AAC3B,WAAK,QAAQ,eAAe,KAAK,SAAS;AAAA,WACrC;AACL,WAAK,QAAQ,OAAO,QAAQ;AAAA;AAAA;AAAA,SAiDzB,MAAM,QAAQ;AACnB,UAAM,OAAO,cAAc,KAAK;AAAA;AAChC,QAAI;AACJ,QAAI;AAEJ,WAAO,eAAe,MAAM,QAAQ,EAAE,OAAO,KAAK;AAElD,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,SAAS;AAEd,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA;AAGT,UAAM,UAAU,EAAE,QAAQ;AAE1B,eAAW,WAAU,SAAS;AAC5B,cAAQ;AACR,kBAAY;AAEZ,UAAI,EAAE,cAAc,UAAS;AAC3B,YAAI,QAAO,QAAQ;AACjB,cAAI,MAAM,QAAQ,QAAO,WAAW,CAAC,CAAC,KAAK,QAAQ,OAAO,QAAO,OAAO,KAAK;AAC3E,wBAAY,QAAO,OAAO;AAC1B,oBAAQ,KAAK,QAAQ,OAAO,WAAW,MAAM,MAAM,QAAO,OAAO,MAAM;AAAA,qBAEhE,KAAK,QAAQ,OAAO,QAAO,SAAS;AAC3C,wBAAY,QAAO;AACnB,oBAAQ,KAAK,QAAQ,OAAO,WAAW,MAAM;AAAA;AAAA,eAE1C;AACL,kBAAQ;AAAA;AAAA,iBAED,YAAW,kBAAkB,EAAE,cAAc,KAAK,QAAQ,eAAe;AAClF,gBAAQ,KAAK,QAAQ;AAAA,aAChB;AACL,oBAAY;AACZ,gBAAQ,KAAK,QAAQ,OAAO;AAC5B,YAAI,OAAO,UAAU,YAAY;AAC/B,kBAAQ;AAAA;AAAA;AAIZ,UAAI,OAAO;AACT,aAAK,iBAAiB,OAAO;AAE7B,aAAK,eAAe,KAAK,QAAQ,MAAM,UAAU;AACjD,aAAK,YAAY,KAAK,YAAY,YAAY;AAAA,aACzC;AACL,cAAM,IAAI,gBAAgB,oBAAoB,iBAAiB;AAAA;AAAA;AAInE,WAAO;AAAA;AAAA,eA4GI,QAAQ,SAAS;AAC5B,QAAI,YAAY,UAAa,CAAC,EAAE,cAAc,UAAU;AACtD,YAAM,IAAI,gBAAgB,WAAW;AAAA;AAGvC,QAAI,YAAY,UAAa,QAAQ,YAAY;AAC/C,UAAI,CAAC,MAAM,QAAQ,QAAQ,eAAe,CAAC,EAAE,cAAc,QAAQ,aAAa;AAC9E,cAAM,IAAI,gBAAgB,WAAW;AAAA;AAAA;AAIzC,SAAK,qBAAqB,SAAS,OAAO,KAAK,KAAK;AAEpD,UAAM,aAAa;AAEnB,eAAW,KAAK,aAAa,YAAY;AACzC,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,MAAE,SAAS,SAAS,EAAE,OAAO;AAG7B,YAAQ,gBAAgB,OAAO,UAAU,eAAe,KAAK,SAAS,mBAClE,QAAQ,gBACR,KAAK,QAAQ;AAEjB,SAAK,aAAa;AAElB,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,cAAc;AAAA;AAEpC,SAAK,iBAAiB,SAAS;AAC/B,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAEvB,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,mCAAmC;AAAA;AAEzD,YAAQ,qBAAqB,KAAK,kCAAkC,QAAQ;AAE5E,QAAI,QAAQ,SAAS;AACnB,cAAQ,UAAU;AAElB,WAAK,0BAA0B,SAAS;AAGxC,UACE,QAAQ,cACL,CAAC,QAAQ,OACT,KAAK,uBACL,CAAC,QAAQ,WAAW,SAAS,KAAK,wBACjC,EAAC,QAAQ,SAAS,CAAC,QAAQ,wBAAwB,QAAQ,sBAC/D;AACA,gBAAQ,aAAa,CAAC,KAAK,qBAAqB,OAAO,QAAQ;AAAA;AAAA;AAInE,QAAI,CAAC,QAAQ,YAAY;AACvB,cAAQ,aAAa,OAAO,KAAK,KAAK;AACtC,cAAQ,qBAAqB,KAAK,kCAAkC,QAAQ;AAAA;AAI9E,SAAK,QAAQ,kBAAkB,QAAQ,SAAS;AAEhD,UAAM,iBAAiB,SAAS;AAEhC,cAAU,KAAK,gBAAgB,MAAM;AAErC,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,0BAA0B;AAAA;AAEhD,UAAM,gBAAgB,iCAAK,UAAL,EAAc,YAAY,OAAO,KAAK;AAC5D,UAAM,UAAU,MAAM,KAAK,eAAe,OAAO,MAAM,KAAK,aAAa,gBAAgB;AACzF,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,aAAa,SAAS;AAAA;AAI5C,QAAI,EAAE,QAAQ,YAAY,QAAQ,eAAe;AAC/C,UAAI,OAAO,QAAQ,kBAAkB,YAAY;AAC/C,cAAM,IAAI,QAAQ;AAAA;AAEpB,UAAI,OAAO,QAAQ,kBAAkB,UAAU;AAC7C,cAAM,QAAQ;AAAA;AAEhB,YAAM,IAAI,gBAAgB;AAAA;AAG5B,WAAO,MAAM,MAAM,cAAc,SAAS;AAAA;AAAA,SAGrC,qBAAqB,SAAS,kBAAkB;AACrD,QAAI,CAAC,EAAE,cAAc,UAAU;AAC7B;AAAA;AAGF,UAAM,sBAAsB,OAAO,KAAK,SAAS,OAAO,OAAK,CAAC,mBAAmB,IAAI;AACrF,UAAM,4BAA4B,EAAE,aAAa,qBAAqB;AACtE,QAAI,CAAC,QAAQ,SAAS,0BAA0B,SAAS,GAAG;AAC1D,aAAO,KAAK,qBAAqB,0BAA0B,KAAK,qDAAqD,KAAK;AAAA;AAAA;AAAA,SAIvH,kCAAkC,YAAY;AACnD,QAAI,CAAC,KAAK;AAAuB,aAAO;AACxC,QAAI,CAAC,cAAc,CAAC,MAAM,QAAQ;AAAa,aAAO;AAEtD,eAAW,aAAa,YAAY;AAClC,UACE,KAAK,mBAAmB,IAAI,cACzB,KAAK,cAAc,WAAW,KAAK,QACtC;AACA,qBAAa,WAAW,OAAO,KAAK,cAAc,WAAW,KAAK;AAAA;AAAA;AAItE,iBAAa,EAAE,KAAK;AAEpB,WAAO;AAAA;AAAA,eAGI,cAAc,SAAS,SAAS;AAC3C,QAAI,CAAC,QAAQ,WAAW,QAAQ,OAAO,CAAC;AAAS,aAAO;AAExD,UAAM,WAAW;AACjB,QAAI,QAAQ;AAAO,gBAAU,CAAC;AAE9B,QAAI,CAAC,QAAQ;AAAQ,aAAO;AAE5B,UAAM,QAAQ,IAAI,QAAQ,QAAQ,IAAI,OAAM,YAAW;AACrD,UAAI,CAAC,QAAQ,UAAU;AACrB,eAAO,MAAM,MAAM,cACjB,QAAQ,OAAO,CAAC,MAAM,WAAW;AAC/B,cAAI,eAAe,OAAO,IAAI,QAAQ,YAAY;AAGlD,cAAI,CAAC;AAAc,mBAAO;AAG1B,cAAI,CAAC,MAAM,QAAQ;AAAe,2BAAe,CAAC;AAElD,mBAAS,IAAI,GAAG,MAAM,aAAa,QAAQ,MAAM,KAAK,EAAE,GAAG;AACzD,iBAAK,KAAK,aAAa;AAAA;AAEzB,iBAAO;AAAA,WACN,KACH,iCAEK,EAAE,KAAK,SAAS,WAAW,cAAc,SAAS,SAAS,SAAS,UAAU,SAAS,WAF5F;AAAA,UAGE,SAAS,QAAQ,WAAW;AAAA;AAAA;AAKlC,YAAM,MAAM,MAAM,QAAQ,YAAY,IAAI,SAAS,kCAE9C,EAAE,KAAK,SAAS,uBAChB,EAAE,KAAK,SAAS,CAAC,UAAU,eAAe,MAAM;AAGrD,iBAAW,UAAU,SAAS;AAC5B,eAAO,IACL,QAAQ,YAAY,IACpB,IAAI,OAAO,IAAI,QAAQ,YAAY,aACnC,EAAE,KAAK;AAAA;AAAA;AAKb,WAAO;AAAA;AAAA,eAgBI,SAAS,OAAO,SAAS;AAEpC,QAAI,CAAC,MAAM,QAAW,SAAS,QAAQ;AACrC,aAAO;AAAA;AAGT,cAAU,MAAM,UAAU,YAAY;AAEtC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,SAAS,QAAQ;AACjH,cAAQ,QAAQ;AAAA,SACb,KAAK,sBAAsB;AAAA;AAAA,WAEzB;AACL,YAAM,IAAI,MAAM,2CAA2C;AAAA;AAK7D,WAAO,MAAM,KAAK,QAAQ;AAAA;AAAA,eAef,QAAQ,SAAS;AAC5B,QAAI,YAAY,UAAa,CAAC,EAAE,cAAc,UAAU;AACtD,YAAM,IAAI,MAAM;AAAA;AAElB,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,QAAI,QAAQ,UAAU,QAAW;AAC/B,YAAM,sBAAsB,EAAE,MAAM,KAAK,YAAY,SAAS,OAAO,OAAK,EAAE,OAAO,WAAW,GAAG,IAAI,UAAU;AAG/G,UAAI,CAAC,QAAQ,SAAS,CAAC,EAAE,KAAK,QAAQ,OAAO,CAAC,OAAO,QAClD,SAAQ,KAAK,uBAAuB,oBAAoB,SAAS,SAC/D,OAAM,YAAY,UAAU,OAAO,SAAS,UAC9C;AACD,gBAAQ,QAAQ;AAAA;AAAA;AAMpB,WAAO,MAAM,KAAK,QAAQ,EAAE,SAAS,SAAS;AAAA,MAC5C,OAAO;AAAA;AAAA;AAAA,eAoBE,UAAU,WAAW,mBAAmB,SAAS;AAC5D,cAAU,MAAM,UAAU;AAG1B,UAAM,iBAAiB,QAAQ;AAC/B,SAAK,aAAa;AAClB,YAAQ,aAAa;AACrB,SAAK,iBAAiB,SAAS;AAE/B,QAAI,QAAQ,SAAS;AACnB,WAAK,kBAAkB;AACvB,WAAK,0BAA0B;AAAA;AAGjC,UAAM,cAAc,KAAK,cAAc;AACvC,UAAM,QAAQ,eAAe,YAAY,SAAS;AAClD,QAAI,kBAAkB,KAAK,UAAU,IAAI;AAEzC,QAAI,QAAQ,UAAU;AACpB,wBAAkB,KAAK,UAAU,GAAG,YAAY;AAAA;AAGlD,QAAI,EAAE,UAAU;AAChB,QAAI,MAAM,QAAQ,UAAU,MAAM,QAAQ,MAAM,KAAK;AACnD;AACA,cAAQ,EAAE,QAAQ;AAAA;AAEpB,YAAQ,aAAa,EAAE,QACrB,QAAQ,YACR,OACA,CAAC,CAAC,KAAK,UAAU,GAAG,mBAAmB,kBAAkB,qBACzD,OAAK,MAAM,QAAQ,KAAK,EAAE,KAAK;AAGjC,QAAI,CAAC,QAAQ,UAAU;AACrB,UAAI,aAAa;AACf,gBAAQ,WAAW,YAAY;AAAA,aAC1B;AAEL,gBAAQ,WAAW,IAAI,UAAU;AAAA;AAAA,WAE9B;AACL,cAAQ,WAAW,KAAK,UAAU,kBAAkB,QAAQ;AAAA;AAG9D,UAAM,oBAAoB,SAAS;AACnC,cAAU,KAAK,gBAAgB,MAAM;AAErC,UAAM,QAAQ,MAAM,KAAK,eAAe,UAAU,KAAK,aAAa,UAAU,SAAS,mBAAmB;AAC1G,WAAO;AAAA;AAAA,eAuBI,MAAM,SAAS;AAC1B,cAAU,MAAM,UAAU;AAC1B,cAAU,EAAE,SAAS,SAAS,EAAE,OAAO;AAGvC,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,YAAQ,MAAM;AACd,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,eAAe;AAAA;AAErC,QAAI,MAAM,QAAQ,OAAO;AACzB,QAAI,QAAQ,SAAS;AACnB,YAAM,GAAG,KAAK,QAAQ,QAAQ,OAAO,KAAK;AAAA;AAE5C,QAAI,QAAQ,YAAY,QAAQ,KAAK;AACnC,YAAM,KAAK;AAAA;AAEb,YAAQ,QAAQ,CAAC,QAAQ;AACzB,YAAQ,WAAW,IAAI,UAAU;AACjC,YAAQ,0BAA0B;AAIlC,YAAQ,QAAQ;AAChB,YAAQ,SAAS;AACjB,YAAQ,QAAQ;AAEhB,UAAM,SAAS,MAAM,KAAK,UAAU,KAAK,SAAS;AAIlD,QAAI,MAAM,QAAQ,SAAS;AACzB,aAAO,OAAO,IAAI,UAAS,iCACtB,OADsB;AAAA,QAEzB,OAAO,OAAO,KAAK;AAAA;AAAA;AAIvB,WAAO;AAAA;AAAA,eAqCI,gBAAgB,SAAS;AACpC,QAAI,YAAY,UAAa,CAAC,EAAE,cAAc,UAAU;AACtD,YAAM,IAAI,MAAM;AAAA;AAGlB,UAAM,eAAe,MAAM,UAAU;AAErC,QAAI,aAAa,YAAY;AAC3B,mBAAa,aAAa;AAAA;AAG5B,UAAM,CAAC,OAAO,QAAQ,MAAM,QAAQ,IAAI;AAAA,MACtC,KAAK,MAAM;AAAA,MACX,KAAK,QAAQ;AAAA;AAGf,WAAO;AAAA,MACL;AAAA,MACA,MAAM,UAAU,IAAI,KAAK;AAAA;AAAA;AAAA,eAehB,IAAI,OAAO,SAAS;AAC/B,WAAO,MAAM,KAAK,UAAU,OAAO,OAAO;AAAA;AAAA,eAc/B,IAAI,OAAO,SAAS;AAC/B,WAAO,MAAM,KAAK,UAAU,OAAO,OAAO;AAAA;AAAA,eAc/B,IAAI,OAAO,SAAS;AAC/B,WAAO,MAAM,KAAK,UAAU,OAAO,OAAO;AAAA;AAAA,SAcrC,MAAM,QAAQ,SAAS;AAC5B,QAAI,MAAM,QAAQ,SAAS;AACzB,aAAO,KAAK,UAAU,QAAQ;AAAA;AAGhC,WAAO,IAAI,KAAK,QAAQ;AAAA;AAAA,SAGnB,UAAU,WAAW,SAAS;AACnC,cAAU,iBAAE,aAAa,QAAS;AAElC,QAAI,CAAC,QAAQ,kBAAkB;AAC7B,WAAK,iBAAiB,SAAS;AAC/B,UAAI,QAAQ,SAAS;AACnB,aAAK,kBAAkB;AACvB,aAAK,0BAA0B;AAAA;AAAA;AAInC,QAAI,QAAQ,YAAY;AACtB,cAAQ,aAAa,QAAQ,WAAW,IAAI,eAAa,MAAM,QAAQ,aAAa,UAAU,KAAK;AAAA;AAGrG,WAAO,UAAU,IAAI,YAAU,KAAK,MAAM,QAAQ;AAAA;AAAA,eA6BvC,OAAO,QAAQ,SAAS;AACnC,cAAU,MAAM,UAAU,WAAW;AAErC,WAAO,MAAM,KAAK,MAAM,QAAQ;AAAA,MAC9B,aAAa;AAAA,MACb,YAAY,QAAQ;AAAA,MACpB,SAAS,QAAQ;AAAA,MACjB,KAAK,QAAQ;AAAA,MACb,QAAQ,QAAQ;AAAA,OACf,KAAK;AAAA;AAAA,eAcG,YAAY,SAAS;AAChC,QAAI,CAAC,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS,GAAG;AACtD,YAAM,IAAI,MACR;AAAA;AAKJ,QAAI;AAEJ,QAAI,WAAW,MAAM,KAAK,QAAQ;AAClC,QAAI,aAAa,MAAM;AACrB,eAAS,mBAAK,QAAQ;AACtB,UAAI,EAAE,cAAc,QAAQ,QAAQ;AAClC,iBAAS,MAAM,SAAS,QAAQ,QAAQ;AAAA;AAG1C,iBAAW,KAAK,MAAM,QAAQ;AAE9B,aAAO,CAAC,UAAU;AAAA;AAGpB,WAAO,CAAC,UAAU;AAAA;AAAA,eAqBP,aAAa,SAAS;AACjC,QAAI,CAAC,WAAW,CAAC,QAAQ,SAAS,UAAU,SAAS,GAAG;AACtD,YAAM,IAAI,MACR;AAAA;AAKJ,cAAU,mBAAK;AAEf,QAAI,QAAQ,UAAU;AACpB,YAAM,WAAW,OAAO,KAAK,QAAQ;AACrC,YAAM,kBAAkB,SAAS,OAAO,UAAQ,CAAC,KAAK,cAAc;AAEpE,UAAI,gBAAgB,QAAQ;AAC1B,eAAO,KAAK,uBAAuB;AAAA;AAAA;AAIvC,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,UAAM,sBAAsB,CAAC,QAAQ;AACrC,QAAI;AACJ,QAAI;AAEJ,QAAI;AACF,YAAM,IAAI,MAAM,KAAK,UAAU,YAAY;AAC3C,oBAAc;AACd,cAAQ,cAAc;AAEtB,YAAM,QAAQ,MAAM,KAAK,QAAQ,MAAM,SAAS,EAAE,eAAe;AACjE,UAAI,UAAU,MAAM;AAClB,eAAO,CAAC,OAAO;AAAA;AAGjB,eAAS,mBAAK,QAAQ;AACtB,UAAI,EAAE,cAAc,QAAQ,QAAQ;AAClC,iBAAS,MAAM,SAAS,QAAQ,QAAQ;AAAA;AAG1C,cAAQ,YAAY;AACpB,cAAQ,YAAY;AAEpB,UAAI;AACF,cAAM,UAAU,MAAM,KAAK,OAAO,QAAQ;AAC1C,YAAI,QAAQ,IAAI,KAAK,qBAAqB,EAAE,KAAK,YAAY,MAAM;AAEjE,gBAAM,IAAI,gBAAgB;AAAA;AAG5B,eAAO,CAAC,SAAS;AAAA,eACV,KAAP;AACA,YAAI,CAAE,gBAAe,gBAAgB;AAAwB,gBAAM;AACnE,cAAM,iBAAiB,MAAM,kBAAkB,QAAQ;AACvD,cAAM,qBAAqB,OAAO,KAAK,gBAAgB,IAAI,UAAQ,EAAE,KAAK,KAAK,MAAM;AACrF,cAAM,cAAc,mBAAmB,IAAI,UAAQ,EAAE,IAAI,KAAK,eAAe,GAAG,cAAc;AAC9F,cAAM,gBAAgB,QAAQ,YAAY,OAAO,KAAK,QAAQ,UAC3D,OAAO,UAAQ,KAAK,cAAc,OAClC,IAAI,UAAQ,KAAK,cAAc,MAAM,SAAS;AAEjD,cAAM,eAAe,OAAO,KAAK,IAAI;AACrC,cAAM,2BAA2B,MAAM,WAAW,cAAc;AAChE,YAAI,iBAAiB,CAAC,4BAA4B,MAAM,WAAW,cAAc,gBAAgB;AAC/F,gBAAM;AAAA;AAGR,YAAI,0BAA0B;AAC5B,YAAE,KAAK,IAAI,QAAQ,CAAC,OAAO,QAAQ;AACjC,kBAAM,OAAO,KAAK,sBAAsB,KAAK;AAC7C,gBAAI,MAAM,eAAe,QAAQ,MAAM,MAAM,YAAY;AACvD,oBAAM,IAAI,MAAM,GAAG,KAAK,qCAAqC,+DAA+D,QAAQ,MAAM,cAAc;AAAA;AAAA;AAAA;AAM9J,cAAM,eAAe,MAAM,KAAK,QAAQ,MAAM,SAAS;AAAA,UACrD,aAAa,sBAAsB,OAAO;AAAA,WACzC;AAIH,YAAI,iBAAiB;AAAM,gBAAM;AAEjC,eAAO,CAAC,cAAc;AAAA;AAAA,cAExB;AACA,UAAI,uBAAuB,aAAa;AACtC,cAAM,YAAY;AAAA;AAAA;AAAA;AAAA,eAkBX,eAAe,SAAS;AACnC,QAAI,CAAC,WAAW,CAAC,QAAQ,OAAO;AAC9B,YAAM,IAAI,MACR;AAAA;AAIJ,QAAI,SAAS,mBAAK,QAAQ;AAC1B,QAAI,EAAE,cAAc,QAAQ,QAAQ;AAClC,eAAS,MAAM,SAAS,QAAQ,QAAQ;AAAA;AAI1C,UAAM,QAAQ,MAAM,KAAK,QAAQ;AACjC,QAAI;AAAO,aAAO,CAAC,OAAO;AAE1B,QAAI;AACF,YAAM,gBAAgB,mBAAK;AAG3B,UAAI,KAAK,UAAU,QAAQ,YAAY,cAAc,QAAQ,aAAa;AACxE,sBAAc,mBAAmB;AAAA;AAGnC,YAAM,UAAU,MAAM,KAAK,OAAO,QAAQ;AAC1C,aAAO,CAAC,SAAS;AAAA,aACV,KAAP;AACA,UAAI,CAAE,gBAAe,gBAAgB,yBAAyB,eAAe,gBAAgB,mBAAmB;AAC9G,cAAM;AAAA;AAGR,YAAM,aAAa,MAAM,KAAK,QAAQ;AACtC,aAAO,CAAC,YAAY;AAAA;AAAA;AAAA,eA8BX,OAAO,QAAQ,SAAS;AACnC,cAAU;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,OACP,MAAM,UAAU;AAIrB,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,UAAM,gBAAgB,KAAK,qBAAqB;AAChD,UAAM,gBAAgB,KAAK,qBAAqB;AAChD,UAAM,aAAa,KAAK,mBAAmB,UAAU,KAAK,uBAAuB;AACjF,UAAM,WAAW,KAAK,MAAM;AAE5B,YAAQ,QAAQ;AAChB,YAAQ,WAAW;AAEnB,UAAM,UAAU,MAAM,KAAK,SAAS;AACpC,QAAI,CAAC,QAAQ,QAAQ;AACnB,cAAQ,SAAS;AAAA;AAGnB,QAAI,QAAQ,UAAU;AACpB,YAAM,SAAS,SAAS;AAAA;AAG1B,UAAM,oBAAoB,EAAE,KAAK,SAAS,YAAY;AACtD,UAAM,eAAe,MAAM,mBAAmB,SAAS,YAAY,OAAO,KAAK,SAAS,gBAAgB;AACxG,UAAM,eAAe,MAAM,mBAAmB,mBAAmB,QAAQ,QAAQ;AACjF,UAAM,MAAM,MAAM,IAAI,KAAK,UAAU,QAAQ;AAG7C,QAAI,iBAAiB,CAAC,aAAa,gBAAgB;AACjD,YAAM,QAAQ,KAAK,cAAc,eAAe,SAAS;AACzD,mBAAa,SAAS,KAAK,qBAAqB,kBAAkB;AAAA;AAEpE,QAAI,iBAAiB,CAAC,aAAa,gBAAgB;AACjD,YAAM,QAAQ,KAAK,cAAc,eAAe,SAAS;AACzD,mBAAa,SAAS,aAAa,SAAS,KAAK,qBAAqB,kBAAkB;AAAA;AAK1F,QAAI,KAAK,UAAU,QAAQ,YAAY,OAAO;AAC5C,WAAK,SAAS,KAAK,UAAU,QAAQ,eAAe,gBAClD,cAAc,KAAK,eAAe,KAAK;AAAA;AAK3C,QAAI,CAAC,cAAc,KAAK,uBAAuB,CAAC,KAAK,cAAc,KAAK,qBAAqB,cAAc;AACzG,aAAO,aAAa,KAAK;AACzB,aAAO,aAAa,KAAK;AAAA;AAG3B,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,gBAAgB,QAAQ;AAAA;AAE9C,UAAM,SAAS,MAAM,KAAK,eAAe,OAAO,KAAK,aAAa,UAAU,cAAc,cAAc,SAAS,SAAS;AAE1H,UAAM,CAAC,UAAU;AACjB,WAAO,cAAc;AAErB,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,eAAe,QAAQ;AAC3C,aAAO;AAAA;AAET,WAAO;AAAA;AAAA,eA6BI,WAAW,SAAS,UAAU,IAAI;AAC7C,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO;AAAA;AAGT,UAAM,UAAU,KAAK,UAAU,QAAQ;AACvC,UAAM,MAAM,MAAM,IAAI,KAAK,UAAU,QAAQ;AAC7C,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,YAAQ,QAAQ;AAEhB,QAAI,CAAC,QAAQ,kBAAkB;AAC7B,WAAK,iBAAiB,SAAS;AAC/B,UAAI,QAAQ,SAAS;AACnB,aAAK,kBAAkB;AACvB,aAAK,0BAA0B;AAAA;AAAA;AAInC,UAAM,YAAY,QAAQ,IAAI,YAAU,KAAK,MAAM,QAAQ,EAAE,aAAa,MAAM,SAAS,QAAQ;AAEjG,UAAM,sBAAsB,OAAO,YAAW,aAAY;AACxD,iBAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,SACf;AAGL,UAAI,SAAQ,cAAc,QAAW;AACnC,YAAI,SAAQ,aAAa;AACvB,mBAAQ,YAAY;AAAA,eACf;AACL,mBAAQ,YAAY;AAAA;AAAA;AAGxB,UAAI,SAAQ,oBAAoB,CAAC,KAAK,UAAU,QAAQ,SAAS,QAAQ,oBACrE,CAAC,KAAK,UAAU,QAAQ,SAAS,QAAQ,qBAAqB;AAChE,cAAM,IAAI,MAAM,GAAG;AAAA;AAErB,UAAI,SAAQ,qBAAsB,aAAY,WAAW,YAAY,aAAa,YAAY,YAAY,YAAY,aAAa;AACjI,cAAM,IAAI,MAAM,GAAG;AAAA;AAGrB,YAAM,QAAQ,SAAQ;AAEtB,eAAQ,SAAS,SAAQ,UAAU,OAAO,KAAK,MAAM;AACrD,YAAM,gBAAgB,MAAM,qBAAqB;AACjD,YAAM,gBAAgB,MAAM,qBAAqB;AAEjD,UAAI,SAAQ,sBAAsB,QAAW;AAC3C,YAAI,MAAM,QAAQ,SAAQ,sBAAsB,SAAQ,kBAAkB,QAAQ;AAChF,mBAAQ,oBAAoB,EAAE,aAC5B,EAAE,QAAQ,OAAO,KAAK,MAAM,kBAAkB,gBAC9C,SAAQ;AAAA,eAEL;AACL,gBAAM,IAAI,MAAM;AAAA;AAAA;AAKpB,UAAI,SAAQ,OAAO;AACjB,cAAM,MAAM,SAAS,oBAAoB,YAAW;AAAA;AAGtD,UAAI,SAAQ,UAAU;AACpB,cAAM,SAAS;AACf,cAAM,kBAAkB,mBAAK;AAC7B,wBAAgB,QAAQ,SAAQ;AAEhC,cAAM,QAAQ,IAAI,WAAU,IAAI,OAAM,aAAY;AAChD,cAAI;AACF,kBAAM,SAAS,SAAS;AAAA,mBACjB,KAAP;AACA,mBAAO,KAAK,IAAI,gBAAgB,gBAAgB,KAAK;AAAA;AAAA;AAIzD,eAAO,SAAQ;AACf,YAAI,OAAO,QAAQ;AACjB,gBAAM,IAAI,gBAAgB,eAAe;AAAA;AAAA;AAG7C,UAAI,SAAQ,iBAAiB;AAC3B,cAAM,QAAQ,IAAI,WAAU,IAAI,OAAM,aAAY;AAChD,gBAAM,oBAAoB,iCACrB,WADqB;AAAA,YAExB,UAAU;AAAA,YACV,OAAO;AAAA;AAET,iBAAO,kBAAkB;AACzB,iBAAO,kBAAkB;AACzB,iBAAO,kBAAkB;AAEzB,gBAAM,SAAS,KAAK;AAAA;AAAA,aAEjB;AACL,YAAI,SAAQ,WAAW,SAAQ,QAAQ,QAAQ;AAC7C,gBAAM,QAAQ,IAAI,SAAQ,QAAQ,OAAO,aAAW,QAAQ,uBAAuB,WAAW,IAAI,OAAM,YAAW;AACjH,kBAAM,uBAAuB;AAC7B,kBAAM,wCAAwC;AAE9C,uBAAW,YAAY,YAAW;AAChC,oBAAM,sBAAsB,SAAS,IAAI,QAAQ;AACjD,kBAAI,qBAAqB;AACvB,qCAAqB,KAAK;AAC1B,sDAAsC,KAAK;AAAA;AAAA;AAI/C,gBAAI,CAAC,qBAAqB,QAAQ;AAChC;AAAA;AAGF,kBAAM,iBAAiB,EAAE,MAAM,UAAU,UACtC,KAAK,CAAC,gBACN,SAAS;AAAA,cACR,aAAa,SAAQ;AAAA,cACrB,SAAS,SAAQ;AAAA,eAChB;AAEL,kBAAM,8BAA8B,MAAM,oBAAoB,sBAAsB;AACpF,uBAAW,OAAO,6BAA6B;AAC7C,oBAAM,sBAAsB,4BAA4B;AACxD,oBAAM,WAAW,sCAAsC;AAEvD,oBAAM,QAAQ,YAAY,IAAI,UAAU,qBAAqB,EAAE,MAAM,OAAO,SAAS,SAAQ;AAAA;AAAA;AAAA;AAOnG,kBAAU,WAAU,IAAI,cAAY;AAClC,gBAAM,SAAS,SAAS;AAGxB,cAAI,iBAAiB,CAAC,OAAO,gBAAgB;AAC3C,mBAAO,iBAAiB;AACxB,gBAAI,CAAC,SAAQ,OAAO,SAAS,gBAAgB;AAC3C,uBAAQ,OAAO,KAAK;AAAA;AAAA;AAGxB,cAAI,iBAAiB,CAAC,OAAO,gBAAgB;AAC3C,mBAAO,iBAAiB;AACxB,gBAAI,CAAC,SAAQ,OAAO,SAAS,gBAAgB;AAC3C,uBAAQ,OAAO,KAAK;AAAA;AAAA;AAIxB,gBAAM,MAAM,MAAM,mBAAmB,QAAQ,SAAQ,QAAQ;AAC7D,qBAAW,OAAO,MAAM,oBAAoB;AAC1C,mBAAO,IAAI;AAAA;AAEb,iBAAO;AAAA;AAIT,cAAM,wBAAwB;AAC9B,mBAAW,QAAQ,MAAM,iBAAiB;AACxC,gCAAsB,MAAM,cAAc,MAAM,SAAS,QAAQ,MAAM,cAAc;AAAA;AAIvF,YAAI,SAAQ,mBAAmB;AAC7B,mBAAQ,oBAAoB,SAAQ,kBAAkB,IAAI,UAAQ,MAAM,cAAc,MAAM,SAAS;AAErG,cAAI,SAAQ,oBAAoB;AAC9B,qBAAQ,aAAa,SAAQ,mBAAmB,IAC9C,cAAY,MAAM,cAAc,UAAU,SAAS;AAAA,iBAEhD;AACL,kBAAM,aAAa;AAEnB,uBAAW,KAAK,MAAM,UAAU;AAC9B,kBAAI,EAAE,UAAU,CAAC,EAAE,OAAO;AACxB,2BAAW,KAAK,GAAG,EAAE;AAAA;AAAA;AAIzB,kBAAM,iBAAiB,OAAO,OAAO,MAAM,YAAY,KAAK,OAAK,EAAE,OAAO,SAAS;AAEnF,gBAAI,kBAAkB,eAAe,QAAQ;AAC3C,yBAAW,KAAK,GAAG,eAAe;AAAA;AAGpC,qBAAQ,aAAa,WAAW,SAAS,IACrC,aACA,OAAO,OAAO,MAAM,aAAa,IAAI,OAAK,EAAE;AAAA;AAAA;AAKpD,YAAI,SAAQ,aAAa,MAAM,QAAQ,SAAQ,YAAY;AACzD,mBAAQ,YAAY,SAAQ,UAAU,IAAI,UAAQ,EAAE,IAAI,MAAM,cAAc,OAAO,SAAS;AAAA;AAG9F,cAAM,UAAU,MAAM,MAAM,eAAe,WAAW,MAAM,aAAa,WAAU,SAAS,UAAS;AACrG,YAAI,MAAM,QAAQ,UAAU;AAC1B,kBAAQ,QAAQ,CAAC,QAAQ,MAAM;AAC7B,kBAAM,WAAW,WAAU;AAE3B,uBAAW,OAAO,QAAQ;AACxB,kBAAI,CAAC,YAAY,QAAQ,MAAM,uBAC7B,SAAS,IAAI,MAAM,wBACnB,CAAC,SAAS,WAAW,UAAU,SAAS,UAAU;AAIlD;AAAA;AAEF,kBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,MAAM;AACrD,sBAAM,SAAS,OAAO;AAEtB,sBAAM,OAAO,EAAE,KAAK,MAAM,eAAe,eAAa,UAAU,cAAc,OAAO,UAAU,UAAU;AAEzG,yBAAS,WAAW,QAAQ,KAAK,aAAa,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAO/D,UAAI,SAAQ,WAAW,SAAQ,QAAQ,QAAQ;AAC7C,cAAM,QAAQ,IAAI,SAAQ,QAAQ,OAAO,aAAW,CAAE,SAAQ,uBAAuB,aACnF,QAAQ,UAAU,QAAQ,OAAO,uBAAuB,gBAAgB,IAAI,OAAM,YAAW;AAC7F,gBAAM,uBAAuB;AAC7B,gBAAM,wCAAwC;AAE9C,qBAAW,YAAY,YAAW;AAChC,gBAAI,aAAa,SAAS,IAAI,QAAQ;AACtC,gBAAI,CAAC,MAAM,QAAQ;AAAa,2BAAa,CAAC;AAE9C,uBAAW,uBAAuB,YAAY;AAC5C,kBAAI,qBAAqB;AACvB,oBAAI,CAAE,SAAQ,uBAAuB,gBAAgB;AACnD,sCAAoB,IAAI,QAAQ,YAAY,YAAY,SAAS,IAAI,QAAQ,YAAY,aAAa,SAAS,YAAY,qBAAqB,EAAE,KAAK,SAAS,EAAE,KAAK;AACvK,yBAAO,OAAO,qBAAqB,QAAQ,YAAY;AAAA;AAEzD,qCAAqB,KAAK;AAC1B,sDAAsC,KAAK;AAAA;AAAA;AAAA;AAKjD,cAAI,CAAC,qBAAqB,QAAQ;AAChC;AAAA;AAGF,gBAAM,iBAAiB,EAAE,MAAM,UAAU,UACtC,KAAK,CAAC,gBACN,SAAS;AAAA,YACR,aAAa,SAAQ;AAAA,YACrB,SAAS,SAAQ;AAAA,aAChB;AAEL,gBAAM,8BAA8B,MAAM,oBAAoB,sBAAsB;AACpF,cAAI,QAAQ,uBAAuB,eAAe;AAChD,kBAAM,YAAY;AAElB,uBAAW,OAAO,6BAA6B;AAC7C,oBAAM,sBAAsB,4BAA4B;AACxD,oBAAM,WAAW,sCAAsC;AAEvD,oBAAM,SAAS;AAAA,iBACZ,QAAQ,YAAY,aAAa,SAAS,IAAI,SAAS,YAAY,qBAAqB,EAAE,KAAK;AAAA,iBAC/F,QAAQ,YAAY,WAAW,oBAAoB,IAAI,oBAAoB,YAAY,qBAAqB,EAAE,KAAK;AAAA,iBAEjH,QAAQ,YAAY,QAAQ;AAEjC,kBAAI,oBAAoB,QAAQ,YAAY,QAAQ,MAAM,OAAO;AAC/D,2BAAW,QAAQ,OAAO,KAAK,QAAQ,YAAY,QAAQ,MAAM,gBAAgB;AAC/E,sBAAI,QAAQ,YAAY,QAAQ,MAAM,cAAc,MAAM,kBACxD,SAAS,QAAQ,YAAY,cAC7B,SAAS,QAAQ,YAAY,YAC7B,OAAO,oBAAoB,QAAQ,YAAY,QAAQ,MAAM,MAAM,UAAU,aAAa;AAC1F;AAAA;AAEF,yBAAO,QAAQ,oBAAoB,QAAQ,YAAY,QAAQ,MAAM,MAAM;AAAA;AAAA;AAI/E,wBAAU,KAAK;AAAA;AAGjB,kBAAM,iBAAiB,EAAE,MAAM,UAAU,UACtC,KAAK,CAAC,eAAe,eACrB,SAAS;AAAA,cACR,aAAa,SAAQ;AAAA,cACrB,SAAS,SAAQ;AAAA,eAChB;AACL,2BAAe,QAAQ,QAAQ,YAAY;AAC3C,kBAAM,mBAAmB,QAAQ,YAAY,aAAa,UAAU,WAAW;AAE/E,kBAAM,oBAAoB,kBAAkB;AAAA;AAAA;AAAA;AAMlD,iBAAU,QAAQ,cAAY;AAC5B,mBAAW,QAAQ,MAAM,eAAe;AACtC,cAAI,MAAM,cAAc,MAAM,SAC1B,SAAS,WAAW,MAAM,cAAc,MAAM,WAAW,UACzD,MAAM,cAAc,MAAM,UAAU,MACtC;AACA,qBAAS,WAAW,QAAQ,SAAS,WAAW,MAAM,cAAc,MAAM;AAC1E,mBAAO,SAAS,WAAW,MAAM,cAAc,MAAM;AAAA;AAEvD,mBAAS,oBAAoB,QAAQ,SAAS,WAAW;AACzD,mBAAS,QAAQ,MAAM;AAAA;AAEzB,iBAAS,cAAc;AAAA;AAIzB,UAAI,SAAQ,OAAO;AACjB,cAAM,MAAM,SAAS,mBAAmB,YAAW;AAAA;AAGrD,aAAO;AAAA;AAGT,WAAO,MAAM,oBAAoB,WAAW;AAAA;AAAA,eAmBjC,SAAS,SAAS;AAC7B,cAAU,MAAM,UAAU,YAAY;AACtC,YAAQ,WAAW;AACnB,WAAO,MAAM,KAAK,QAAQ;AAAA;AAAA,eAqBf,QAAQ,SAAS;AAC5B,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,SAAK,aAAa;AAElB,QAAI,CAAC,WAAW,CAAE,SAAQ,SAAS,QAAQ,WAAW;AACpD,YAAM,IAAI,MAAM;AAAA;AAGlB,QAAI,CAAC,QAAQ,YAAY,CAAC,EAAE,cAAc,QAAQ,UAAU,CAAC,MAAM,QAAQ,QAAQ,UAAU,CAAE,SAAQ,iBAAiB,MAAM,kBAAkB;AAC9I,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,EAAE,SAAS,SAAS;AAAA,MAC5B,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA;AAGnB,YAAQ,OAAO,WAAW;AAE1B,UAAM,oBAAoB,SAAS;AACnC,YAAQ,QAAQ;AAIhB,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,qBAAqB;AAAA;AAE3C,QAAI;AAEJ,QAAI,QAAQ,iBAAiB;AAC3B,kBAAY,MAAM,KAAK,QAAQ,EAAE,OAAO,QAAQ,OAAO,aAAa,QAAQ,aAAa,SAAS,QAAQ,SAAS,WAAW,QAAQ;AAEtI,YAAM,QAAQ,IAAI,UAAU,IAAI,cAAY,KAAK,SAAS,iBAAiB,UAAU;AAAA;AAEvF,QAAI;AAEJ,QAAI,KAAK,qBAAqB,aAAa,CAAC,QAAQ,OAAO;AAEzD,cAAQ,OAAO,WAAW;AAE1B,YAAM,gBAAgB;AACtB,YAAM,qBAAqB,KAAK,cAAc,KAAK,qBAAqB;AACxE,YAAM,QAAQ,KAAK,cAAc,KAAK,qBAAqB,WAAW;AACtE,YAAM,QAAQ;AAAA,SACX,QAAQ,OAAO,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,mBAAmB,eAAe;AAAA;AAIxH,oBAAc,SAAS,MAAM,IAAI,KAAK,UAAU,QAAQ;AACxD,eAAS,MAAM,KAAK,eAAe,WAAW,KAAK,aAAa,UAAU,eAAe,OAAO,OAAO,OAAO,QAAQ,QAAQ,SAAS,KAAK;AAAA,WACvI;AACL,eAAS,MAAM,KAAK,eAAe,WAAW,KAAK,aAAa,UAAU,QAAQ,OAAO,SAAS;AAAA;AAGpG,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,QAAQ,IACZ,UAAU,IAAI,cAAY,KAAK,SAAS,gBAAgB,UAAU;AAAA;AAItE,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,oBAAoB;AAAA;AAE1C,WAAO;AAAA;AAAA,eAiBI,QAAQ,SAAS;AAC5B,QAAI,CAAC,KAAK,qBAAqB;AAAW,YAAM,IAAI,MAAM;AAE1D,cAAU;AAAA,MACR,OAAO;AAAA,MACP,iBAAiB;AAAA,OACd;AAIL,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,YAAQ,OAAO,WAAW;AAC1B,YAAQ,QAAQ;AAEhB,UAAM,oBAAoB,SAAS;AAGnC,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,qBAAqB;AAAA;AAG3C,QAAI;AAEJ,QAAI,QAAQ,iBAAiB;AAC3B,kBAAY,MAAM,KAAK,QAAQ,EAAE,OAAO,QAAQ,OAAO,aAAa,QAAQ,aAAa,SAAS,QAAQ,SAAS,WAAW,QAAQ,WAAW,UAAU;AAE3J,YAAM,QAAQ,IAAI,UAAU,IAAI,cAAY,KAAK,SAAS,iBAAiB,UAAU;AAAA;AAGvF,UAAM,gBAAgB;AACtB,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,qBAAqB,KAAK,cAAc;AAC9C,UAAM,wBAAwB,OAAO,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,mBAAmB,eAAe;AAE3I,kBAAc,mBAAmB,SAAS,gBAAgB;AAC1D,YAAQ,WAAW;AACnB,UAAM,SAAS,MAAM,KAAK,eAAe,WAAW,KAAK,aAAa,UAAU,eAAe,QAAQ,OAAO,SAAS,KAAK;AAE5H,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,QAAQ,IACZ,UAAU,IAAI,cAAY,KAAK,SAAS,gBAAgB,UAAU;AAAA;AAItE,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,SAAS,oBAAoB;AAAA;AAE1C,WAAO;AAAA;AAAA,eA0BI,OAAO,QAAQ,SAAS;AACnC,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,SAAK,aAAa;AAClB,SAAK,yBAAyB;AAE9B,cAAU,KAAK,gBAAgB,MAAM,EAAE,SAAS,SAAS;AAAA,MACvD,UAAU;AAAA,MACV,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,aAAa;AAAA;AAGf,YAAQ,OAAO,WAAW;AAG1B,aAAS,EAAE,OAAO,QAAQ,WAAS,UAAU;AAG7C,QAAI,QAAQ,UAAU,QAAQ,kBAAkB,OAAO;AACrD,iBAAW,OAAO,OAAO,KAAK,SAAS;AACrC,YAAI,CAAC,QAAQ,OAAO,SAAS,MAAM;AACjC,iBAAO,OAAO;AAAA;AAAA;AAAA,WAGb;AACL,YAAM,gBAAgB,KAAK,qBAAqB;AAChD,cAAQ,SAAS,EAAE,aAAa,OAAO,KAAK,SAAS,OAAO,KAAK,KAAK;AACtE,UAAI,iBAAiB,CAAC,QAAQ,OAAO,SAAS,gBAAgB;AAC5D,gBAAQ,OAAO,KAAK;AAAA;AAAA;AAIxB,QAAI,KAAK,qBAAqB,aAAa,CAAC,QAAQ,QAAQ;AAC1D,aAAO,KAAK,qBAAqB,aAAa,KAAK,qBAAqB,KAAK,qBAAqB,cAAc,MAAM,IAAI,KAAK,UAAU,QAAQ;AAAA;AAGnJ,YAAQ,QAAQ;AAEhB,QAAI;AAEJ,QAAI,QAAQ,UAAU;AACpB,YAAM,QAAQ,KAAK,MAAM;AACzB,YAAM,IAAI,KAAK,qBAAqB,WAAW,OAAO,KAAK,qBAAqB,YAAY,EAAE,KAAK;AAEnG,UAAI,QAAQ,aAAa;AACvB,eAAO,OAAO,QAAQ,EAAE,KAAK,MAAM,OAAO,MAAM;AAChD,gBAAQ,SAAS,EAAE,MAAM,QAAQ,QAAQ,OAAO,KAAK;AAAA;AAIvD,cAAQ,OAAO,EAAE,WAAW,OAAO,KAAK,KAAK,gBAAgB,OAAO,KAAK;AACzE,YAAM,aAAa,MAAM,MAAM,SAAS;AACxC,cAAQ,OAAO;AACf,UAAI,cAAc,WAAW,YAAY;AACvC,iBAAS,EAAE,KAAK,WAAW,YAAY,OAAO,KAAK;AAAA;AAAA;AAIvD,QAAI,QAAQ,OAAO;AACjB,cAAQ,aAAa;AACrB,YAAM,KAAK,SAAS,oBAAoB;AACxC,eAAS,QAAQ;AACjB,aAAO,QAAQ;AAAA;AAGjB,gBAAY;AAGZ,QAAI;AACJ,QAAI,qBAAqB;AACzB,QAAI,QAAQ,iBAAiB;AAC3B,kBAAY,MAAM,KAAK,QAAQ;AAAA,QAC7B,OAAO,QAAQ;AAAA,QACf,aAAa,QAAQ;AAAA,QACrB,SAAS,QAAQ;AAAA,QACjB,WAAW,QAAQ;AAAA,QACnB,UAAU,QAAQ;AAAA;AAGpB,UAAI,UAAU,QAAQ;AAGpB,YAAI;AACJ,YAAI,YAAY;AAEhB,oBAAY,MAAM,QAAQ,IAAI,UAAU,IAAI,OAAM,aAAY;AAE5D,iBAAO,OAAO,SAAS,YAAY;AAEnC,YAAE,MAAM,WAAW,CAAC,UAAU,SAAS;AACrC,gBAAI,aAAa,SAAS,oBAAoB,OAAO;AACnD,uBAAS,aAAa,MAAM;AAAA;AAAA;AAKhC,gBAAM,KAAK,SAAS,gBAAgB,UAAU;AAC9C,cAAI,CAAC,WAAW;AACd,kBAAM,oBAAoB;AAC1B,cAAE,MAAM,SAAS,YAAY,CAAC,UAAU,SAAS;AAC/C,kBAAI,aAAa,SAAS,oBAAoB,OAAO;AACnD,kCAAkB,QAAQ;AAAA;AAAA;AAI9B,gBAAI,CAAC,eAAe;AAClB,8BAAgB;AAAA,mBACX;AACL,0BAAY,CAAC,EAAE,QAAQ,eAAe;AAAA;AAAA;AAI1C,iBAAO;AAAA;AAGT,YAAI,CAAC,WAAW;AACd,gBAAM,OAAO,OAAO,KAAK;AAEzB,cAAI,KAAK,QAAQ;AAEf,wBAAY;AACZ,oBAAQ,SAAS,EAAE,MAAM,QAAQ,QAAQ;AAAA;AAAA,eAEtC;AACL,sBAAY,MAAM,QAAQ,IAAI,UAAU,IAAI,OAAM,aAAY;AAC5D,kBAAM,oBAAoB,iCACrB,UADqB;AAAA,cAExB,OAAO;AAAA,cACP,UAAU;AAAA;AAEZ,mBAAO,kBAAkB;AAEzB,mBAAO,SAAS,KAAK;AAAA;AAEvB,+BAAqB;AAAA;AAAA;AAAA;AAI3B,QAAI;AACJ,QAAI,oBAAoB;AACtB,eAAS,CAAC,UAAU,QAAQ;AAAA,eACnB,EAAE,QAAQ,cACf,OAAO,KAAK,WAAW,WAAW,KAAK,UAAU,KAAK,qBAAqB,YAAY;AAE3F,eAAS,CAAC;AAAA,WACL;AACL,kBAAY,MAAM,mBAAmB,WAAW,QAAQ,QAAQ;AAChE,gBAAU,MAAM,oBAAoB,SAAS;AAC7C,cAAQ,aAAa,KAAK,UAAU,KAAK,QAAQ,aAAa;AAE9D,YAAM,eAAe,MAAM,KAAK,eAAe,WAAW,KAAK,aAAa,UAAU,WAAW,QAAQ,OAAO,SAAS,KAAK;AAC9H,UAAI,QAAQ,WAAW;AACrB,iBAAS,CAAC,aAAa,QAAQ;AAC/B,oBAAY;AAAA,aACP;AACL,iBAAS,CAAC;AAAA;AAAA;AAId,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,QAAQ,IAAI,UAAU,IAAI,cAAY,KAAK,SAAS,eAAe,UAAU;AACnF,aAAO,KAAK;AAAA;AAGd,QAAI,QAAQ,OAAO;AACjB,cAAQ,aAAa;AACrB,YAAM,KAAK,SAAS,mBAAmB;AACvC,aAAO,QAAQ;AAAA;AAEjB,WAAO;AAAA;AAAA,eAWI,SAAS,QAAQ,SAAS;AACrC,WAAO,MAAM,KAAK,eAAe,cAAc,KAAK,WAAW,iBAAE,QAAQ,UAAU,KAAK,WAAW,UAAc;AAAA;AAAA,SAG5G,qBAAqB,MAAM;AAChC,QAAI,CAAC,CAAC,KAAK,cAAc,SAAS,CAAC,CAAC,KAAK,cAAc,MAAM,cAAc;AACzE,aAAO,MAAM,eAAe,KAAK,cAAc,MAAM,cAAc,KAAK,UAAU,QAAQ;AAAA;AAE5F,WAAO;AAAA;AAAA,SAGF,kBAAkB,SAAS;AAChC,QAAI,CAAC,EAAE,cAAc,QAAQ,aAAa;AACxC;AAAA;AAEF,QAAI,aAAa,OAAO,KAAK,KAAK;AAElC,QAAI,QAAQ,WAAW,SAAS;AAC9B,mBAAa,WAAW,OAAO,UAAQ,CAAC,QAAQ,WAAW,QAAQ,SAAS;AAAA;AAG9E,QAAI,QAAQ,WAAW,SAAS;AAC9B,mBAAa,WAAW,OAAO,QAAQ,WAAW;AAAA;AAGpD,YAAQ,aAAa;AAAA;AAAA,SAIhB,aAAa,SAAS;AAC3B,UAAM,QAAQ,MAAM,UAAU,KAAK;AACnC,SAAK,iBAAiB,SAAS;AAAA;AAAA,UAGzB,OAAO,IAAI,iCAAiC;AAClD,WAAO,KAAK;AAAA;AAAA,SAGP,SAAS,OAAO;AACrB,WAAO,OAAO,UAAU,eAAe,KAAK,KAAK,cAAc;AAAA;AAAA,eA+BpD,UAAU,QAAQ,SAAS;AACtC,cAAU,WAAW;AACrB,QAAI,OAAO,WAAW;AAAU,eAAS,CAAC;AAC1C,QAAI,MAAM,QAAQ,SAAS;AACzB,eAAS,OAAO,IAAI,OAAK;AACvB,YAAI,KAAK,cAAc,MAAM,KAAK,cAAc,GAAG,SAAS,KAAK,cAAc,GAAG,UAAU,GAAG;AAC7F,iBAAO,KAAK,cAAc,GAAG;AAAA;AAE/B,eAAO;AAAA;AAAA,eAEA,UAAU,OAAO,WAAW,UAAU;AAC/C,eAAS,OAAO,KAAK,QAAQ,OAAO,CAAC,WAAW,MAAM;AACpD,YAAI,KAAK,cAAc,MAAM,KAAK,cAAc,GAAG,SAAS,KAAK,cAAc,GAAG,UAAU,GAAG;AAC7F,oBAAU,KAAK,cAAc,GAAG,SAAS,OAAO;AAAA,eAC3C;AACL,oBAAU,KAAK,OAAO;AAAA;AAExB,eAAO;AAAA,SACN;AAAA;AAGL,SAAK,aAAa;AAClB,SAAK,yBAAyB;AAE9B,cAAU,MAAM,SAAS,IAAI,SAAS;AAAA,MACpC,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,WAAW;AAAA;AAEb,UAAM,gBAAgB,CAAC,QAAQ;AAE/B,UAAM,oBAAoB,SAAS;AAEnC,UAAM,QAAQ,mBAAK,QAAQ;AAI3B,QAAI,0BAA0B;AAC9B,QAAI,MAAM,QAAQ,SAAS;AACzB,gCAA0B;AAC1B,iBAAW,SAAS,QAAQ;AAC1B,gCAAwB,SAAS,QAAQ;AAAA;AAAA,WAEtC;AAGL,gCAA0B;AAAA;AAM5B,QAAI,KAAK,mBAAmB;AAC1B,8BAAwB,KAAK,qBAAqB,gBAAgB,KAAK;AAAA;AAGzE,UAAM,6BAA6B;AAEnC,UAAM,gBAAgB,KAAK,qBAAqB;AAChD,QAAI,CAAC,QAAQ,UAAU,iBAAiB,CAAC,wBAAwB,gBAAgB;AAC/E,YAAM,WAAW,KAAK,cAAc,eAAe,SAAS;AAC5D,iCAA2B,YAAY,KAAK,qBAAqB,kBAAkB,MAAM,IAAI,KAAK,UAAU,QAAQ;AAAA;AAGtH,UAAM,YAAY,KAAK,aAAa;AACpC,QAAI;AACJ,QAAI,eAAe;AACjB,qBAAe,MAAM,KAAK,eAAe,UACvC,MAAM,WAAW,OAAO,yBAAyB,4BAA4B;AAAA,WAE1E;AACL,qBAAe,MAAM,KAAK,eAAe,UACvC,MAAM,WAAW,OAAO,yBAAyB,4BAA4B;AAAA;AAIjF,QAAI,QAAQ,WAAW;AACrB,aAAO,CAAC,cAAc,aAAa;AAAA;AAGrC,WAAO,CAAC;AAAA;AAAA,eA4BG,UAAU,QAAQ,SAAS;AACtC,WAAO,KAAK,UAAU,QAAQ;AAAA,MAC5B,IAAI;AAAA,OACD,UAFyB;AAAA,MAG5B,WAAW;AAAA;AAAA;AAAA,SAIR,yBAAyB,SAAS;AACvC,WAAO,WAAW,QAAQ,OAAO;AACjC,WAAO,EAAE,cAAc,QAAQ,UAAU,MAAM,QAAQ,QAAQ,UAAU,QAAQ,iBAAiB,MAAM,iBACtG;AAAA;AAAA,EAUJ,MAAM,cAAc;AAClB,UAAM,QAAQ,KAAK,YAAY,qBAAqB,OAAO,CAAC,QAAQ,cAAc;AAChF,aAAO,aAAa,KAAK,IAAI,WAAW,EAAE,KAAK;AAC/C,aAAO;AAAA,OACN;AAEH,QAAI,EAAE,KAAK,WAAW,GAAG;AACvB,aAAO,KAAK,YAAY,QAAQ;AAAA;AAElC,UAAM,cAAc,KAAK,YAAY;AACrC,QAAI,gBAAgB,aAAa;AAC/B,YAAM,eAAe,KAAK,IAAI,aAAa,EAAE,KAAK;AAAA;AAEpD,WAAO,MAAM,mBAAmB,OAAO,KAAK;AAAA;AAAA,EAG9C,WAAW;AACT,WAAO,6BAA6B,KAAK,YAAY;AAAA;AAAA,EAUvD,aAAa,KAAK;AAChB,WAAO,KAAK,WAAW;AAAA;AAAA,EAUzB,aAAa,KAAK,OAAO;AACvB,UAAM,gBAAgB,KAAK,oBAAoB;AAE/C,QAAI,CAAC,EAAE,QAAQ,OAAO,gBAAgB;AACpC,WAAK,QAAQ,KAAK;AAAA;AAGpB,SAAK,WAAW,OAAO;AAAA;AAAA,EAezB,IAAI,KAAK,SAAS;AAChB,QAAI,YAAY,UAAa,OAAO,QAAQ,UAAU;AACpD,gBAAU;AACV,YAAM;AAAA;AAGR,cAAU,WAAW;AAErB,QAAI,KAAK;AACP,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,gBAAgB,QAAQ,CAAC,QAAQ,KAAK;AAClF,eAAO,KAAK,eAAe,KAAK,KAAK,MAAM,KAAK;AAAA;AAGlD,UAAI,QAAQ,SAAS,KAAK,SAAS,WAAW,KAAK,SAAS,aAAa,SAAS,MAAM;AACtF,YAAI,MAAM,QAAQ,KAAK,WAAW,OAAO;AACvC,iBAAO,KAAK,WAAW,KAAK,IAAI,cAAY,SAAS,IAAI;AAAA;AAE3D,YAAI,KAAK,WAAW,gBAAgB,OAAO;AACzC,iBAAO,KAAK,WAAW,KAAK,IAAI;AAAA;AAElC,eAAO,KAAK,WAAW;AAAA;AAGzB,aAAO,KAAK,WAAW;AAAA;AAGzB,QACE,KAAK,qBACF,QAAQ,SAAS,KAAK,SAAS,WAC/B,QAAQ,OACX;AACA,YAAM,SAAS;AACf,UAAI;AAEJ,UAAI,KAAK,mBAAmB;AAC1B,aAAK,QAAQ,KAAK,gBAAgB;AAChC,cACE,KAAK,SAAS,cACX,CAAC,KAAK,SAAS,WAAW,SAAS,OACtC;AACA;AAAA;AAGF,cAAI,OAAO,UAAU,eAAe,KAAK,KAAK,gBAAgB,OAAO;AACnE,mBAAO,QAAQ,KAAK,IAAI,MAAM;AAAA;AAAA;AAAA;AAKpC,WAAK,QAAQ,KAAK,YAAY;AAC5B,YACE,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,SAC3C,OAAO,UAAU,eAAe,KAAK,KAAK,YAAY,OACzD;AACA,iBAAO,QAAQ,KAAK,IAAI,MAAM;AAAA;AAAA;AAIlC,aAAO;AAAA;AAGT,WAAO,KAAK;AAAA;AAAA,EA8Bd,IAAI,KAAK,OAAO,SAAS;AACvB,QAAI;AACJ,QAAI;AAEJ,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,eAAS;AACT,gBAAU,SAAS;AAEnB,UAAI,QAAQ,OAAO;AACjB,aAAK,aAAa;AAClB,mBAAW,QAAO,QAAQ;AACxB,eAAK,QAAQ,MAAK;AAAA;AAAA;AAKtB,UAAI,QAAQ,OAAO,CAAE,MAAK,YAAY,KAAK,SAAS,YAAY,CAAE,YAAW,QAAQ,eAAe,CAAC,KAAK,YAAY,sBAAsB,CAAC,KAAK,YAAY,uBAAuB;AACnL,YAAI,OAAO,KAAK,KAAK,YAAY,QAAQ;AACvC,iBAAO,OAAO,KAAK,YAAY;AAAA,eAC1B;AACL,eAAK,aAAa;AAAA;AAGpB,aAAK,sBAAsB,mBAAK,KAAK;AAAA,aAChC;AAEL,YAAI,QAAQ,YAAY;AACtB,gBAAM,UAAU,UAAQ;AACtB,uBAAW,KAAK,MAAM;AACpB,kBAAI,OAAO,OAAO,QAAW;AAC3B;AAAA;AAEF,mBAAK,IAAI,GAAG,OAAO,IAAI;AAAA;AAAA;AAG3B,kBAAQ,QAAQ;AAChB,cAAI,KAAK,YAAY,uBAAuB;AAC1C,oBAAQ,KAAK,YAAY;AAAA;AAE3B,cAAI,KAAK,SAAS,cAAc;AAC9B,oBAAQ,KAAK,SAAS;AAAA;AAAA,eAEnB;AACL,qBAAW,QAAO,QAAQ;AACxB,iBAAK,IAAI,MAAK,OAAO,OAAM;AAAA;AAAA;AAI/B,YAAI,QAAQ,KAAK;AAEf,eAAK,sBAAsB,mBAAK,KAAK;AAAA;AAAA;AAGzC,aAAO;AAAA;AAET,QAAI,CAAC;AACH,gBAAU;AACZ,QAAI,CAAC,QAAQ,KAAK;AAChB,sBAAgB,KAAK,WAAW;AAAA;AAIlC,QAAI,CAAC,QAAQ,OAAO,KAAK,eAAe,MAAM;AAC5C,WAAK,eAAe,KAAK,KAAK,MAAM,OAAO;AAG3C,YAAM,WAAW,KAAK,WAAW;AACjC,UAAI,CAAC,EAAE,QAAQ,UAAU,gBAAgB;AACvC,aAAK,oBAAoB,OAAO;AAChC,aAAK,QAAQ,KAAK;AAAA;AAAA,WAEf;AAEL,UAAI,KAAK,YAAY,KAAK,SAAS,WAAW,KAAK,SAAS,aAAa,SAAS,MAAM;AAEtF,aAAK,YAAY,KAAK,OAAO;AAC7B,eAAO;AAAA;AAGT,UAAI,CAAC,QAAQ,KAAK;AAEhB,YAAI,CAAC,KAAK,aAAa,MAAM;AAC3B,cAAI,IAAI,SAAS,QAAQ,KAAK,YAAY,gBAAgB,IAAI,IAAI,MAAM,KAAK,KAAK;AAChF,kBAAM,sBAAsB,OAAO,IAAI,KAAK,YAAY;AACxD,gBAAI,CAAC,EAAE,QAAQ,qBAAqB,QAAQ;AAC1C,qBAAO,IAAI,KAAK,YAAY,KAAK;AACjC,mBAAK,QAAQ,IAAI,MAAM,KAAK,IAAI;AAAA;AAAA;AAGpC,iBAAO;AAAA;AAIT,YAAI,KAAK,YAAY,mBAAmB,iBAAiB,KAAK,YAAY,cAAc,MAAM;AAC5F,iBAAO;AAAA;AAIT,YAAI,CAAC,KAAK,eAAe,KAAK,YAAY,0BAA0B,KAAK,YAAY,oBAAoB,IAAI,MAAM;AACjH,iBAAO;AAAA;AAAA;AAKX,UACE,CAAE,kBAAiB,MAAM,oBACtB,OAAO,UAAU,eAAe,KAAK,KAAK,YAAY,qBAAqB,MAC9E;AACA,gBAAQ,KAAK,YAAY,oBAAoB,KAAK,KAAK,MAAM,OAAO;AAAA;AAItE,UACE,CAAC,QAAQ,OAGP,kBAAiB,MAAM,mBAEvB,CAAE,kBAAiB,MAAM,oBAAoB,KAAK,YAAY,iBAAiB,QAAQ,KAAK,YAAY,iBAAiB,KAAK,KAAK,MAAM,OAAO,eAAe,YAC/J,CAAC,KAAK,YAAY,iBAAiB,QAAQ,CAAC,EAAE,QAAQ,OAAO,iBAE/D;AACA,aAAK,oBAAoB,OAAO;AAChC,aAAK,QAAQ,KAAK;AAAA;AAIpB,WAAK,WAAW,OAAO;AAAA;AAEzB,WAAO;AAAA;AAAA,EAGT,cAAc,SAAS;AACrB,WAAO,KAAK,IAAI;AAAA;AAAA,EA8BlB,QAAQ,KAAK,OAAO;AAClB,QAAI,QAAQ,QAAW;AACrB,UAAI,KAAK,SAAS,OAAO,GAAG;AAC1B,eAAO,MAAM,KAAK,KAAK;AAAA;AAEzB,aAAO;AAAA;AAET,QAAI,UAAU,MAAM;AAClB,WAAK,SAAS,IAAI;AAClB,aAAO;AAAA;AAET,QAAI,UAAU,OAAO;AACnB,WAAK,SAAS,OAAO;AACrB,aAAO;AAAA;AAET,WAAO,KAAK,SAAS,IAAI;AAAA;AAAA,EAY3B,SAAS,KAAK;AACZ,QAAI,KAAK;AACP,aAAO,KAAK,oBAAoB;AAAA;AAGlC,WAAO,EAAE,OAAO,KAAK,qBAAqB,CAAC,OAAO,SAAQ,KAAK,QAAQ;AAAA;AAAA,EAGzE,YAAY,KAAK,OAAO,SAAS;AAC/B,QAAI,CAAC,MAAM,QAAQ;AAAQ,cAAQ,CAAC;AACpC,QAAI,MAAM,cAAc,OAAO;AAC7B,cAAQ,MAAM,IAAI,cAAY,SAAS;AAAA;AAGzC,UAAM,UAAU,KAAK,SAAS,WAAW;AACzC,UAAM,cAAc,QAAQ;AAC5B,UAAM,WAAW;AACjB,UAAM,sBAAsB,QAAQ,MAAM;AAC1C,UAAM,eAAe;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,SAAS,QAAQ;AAAA,MACjB,cAAc,QAAQ;AAAA,MACtB,YAAY,QAAQ;AAAA,MACpB,kBAAkB;AAAA,MAClB,KAAK,QAAQ;AAAA,MACb,YAAY,QAAQ;AAAA;AAEtB,QAAI;AAEJ,QAAI,QAAQ,uBAAuB,UAAa,QAAQ,mBAAmB,QAAQ;AACjF,UAAI,YAAY,qBAAqB;AACnC,YAAI,MAAM,QAAQ,QAAQ;AACxB,kBAAQ,MAAM;AAAA;AAEhB,kBAAU,SAAS,MAAM,yBAAyB,QAAQ,UAAU;AACpE,aAAK,YAAY,KAAK,WAAW,YAAY,UAAU,OAAO,QAAQ,MAAM,MAAM,OAAO;AAAA,aACpF;AACL,kBAAU,MAAM,MAAM,MAAM,GAAG,yBAAyB;AACxD,aAAK,YAAY,KAAK,WAAW,YAAY,UAAU,KAAK,QAAQ,MAAM,UAAU,OAAO;AAAA;AAAA;AAAA;AAAA,QA0B3F,KAAK,SAAS;AAClB,QAAI,UAAU,SAAS,GAAG;AACxB,YAAM,IAAI,MAAM;AAAA;AAGlB,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,cAAU,EAAE,SAAS,SAAS;AAAA,MAC5B,OAAO;AAAA,MACP,UAAU;AAAA;AAGZ,QAAI,CAAC,QAAQ,QAAQ;AACnB,UAAI,KAAK,aAAa;AACpB,gBAAQ,SAAS,OAAO,KAAK,KAAK,YAAY;AAAA,aACzC;AACL,gBAAQ,SAAS,EAAE,aAAa,KAAK,WAAW,OAAO,KAAK,KAAK,YAAY;AAAA;AAG/E,cAAQ,gBAAgB,QAAQ;AAAA;AAGlC,QAAI,QAAQ,cAAc,QAAW;AACnC,UAAI,QAAQ,aAAa;AACvB,gBAAQ,YAAY;AAAA,iBACX,KAAK,aAAa;AAC3B,gBAAQ,YAAY;AAAA;AAAA;AAIxB,UAAM,iBAAiB,KAAK,YAAY;AACxC,UAAM,sBAAsB,kBAAkB,KAAK,YAAY,cAAc;AAC7E,UAAM,gBAAgB,KAAK,YAAY,qBAAqB;AAC5D,UAAM,cAAc,KAAK,YAAY;AACrC,UAAM,OAAO,KAAK,cAAc,WAAW;AAC3C,UAAM,eAAe,KAAK;AAC1B,UAAM,MAAM,MAAM,IAAI,KAAK,UAAU,QAAQ;AAC7C,QAAI,gBAAgB,KAAK,YAAY,qBAAqB;AAE1D,QAAI,iBAAiB,QAAQ,OAAO,SAAS,KAAK,CAAC,QAAQ,OAAO,SAAS,gBAAgB;AACzF,cAAQ,OAAO,KAAK;AAAA;AAEtB,QAAI,eAAe,QAAQ,OAAO,SAAS,KAAK,CAAC,QAAQ,OAAO,SAAS,cAAc;AACrF,cAAQ,OAAO,KAAK;AAAA;AAGtB,QAAI,QAAQ,WAAW,QAAQ,CAAE,MAAK,eAAe,KAAK,IAAI,eAAe,EAAE,KAAK,UAAU;AAE5F,QAAE,OAAO,QAAQ,QAAQ,SAAO,QAAQ;AACxC,sBAAgB;AAAA;AAGlB,QAAI,KAAK,gBAAgB,MAAM;AAC7B,UAAI,iBAAiB,CAAC,QAAQ,OAAO,SAAS,gBAAgB;AAC5D,gBAAQ,OAAO,KAAK;AAAA;AAGtB,UAAI,uBAAuB,oBAAoB,gBAAgB,CAAC,QAAQ,OAAO,SAAS,iBAAiB;AACvG,gBAAQ,OAAO,QAAQ;AAAA;AAAA;AAI3B,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,kBAAkB,KAAK,IAAI,gBAAgB,EAAE,KAAK,YAAY,QAAW;AAC3E,cAAM,IAAI,MAAM;AAAA;AAAA;AAIpB,QAAI,iBAAiB,CAAC,QAAQ,UAAU,QAAQ,OAAO,SAAS,gBAAgB;AAC9E,WAAK,WAAW,iBAAiB,KAAK,YAAY,qBAAqB,kBAAkB;AAAA;AAG3F,QAAI,KAAK,eAAe,iBAAiB,CAAC,KAAK,WAAW,gBAAgB;AACxE,WAAK,WAAW,iBAAiB,KAAK,YAAY,qBAAqB,kBAAkB;AAAA;AAI3F,QAAI,KAAK,UAAU,QAAQ,YAAY,SAAS,KAAK,aAAa;AAChE,WAAK,SAAS,KAAK,UAAU,QAAQ,eAAe,gBAClD,KAAK,YAAY,KAAK,YAAY,eAAe,KAAK;AAAA;AAG1D,QAAI,QAAQ,UAAU;AACpB,YAAM,KAAK,SAAS;AAAA;AAGtB,QAAI,QAAQ,OAAO;AACjB,YAAM,mBAAmB,EAAE,KAAK,KAAK,YAAY,QAAQ;AACzD,UAAI,gBAAgB,EAAE,WAAW,KAAK,WAAW,QAAQ;AACzD,UAAI;AACJ,UAAI;AAEJ,UAAI,iBAAiB,QAAQ,OAAO,SAAS,gBAAgB;AAC3D,wBAAgB,EAAE,QAAQ,eAAe;AAAA;AAG3C,YAAM,KAAK,YAAY,SAAS,SAAS,QAAQ,MAAM;AACvD,UAAI,QAAQ,iBAAiB,CAAC,KAAK,aAAa;AAC9C,0BAAkB,EAAE,KAAK,KAAK,YAAY,EAAE,WAAW,KAAK,WAAW;AAEvE,sBAAc;AACd,mBAAW,OAAO,OAAO,KAAK,kBAAkB;AAC9C,cAAI,gBAAgB,SAAS,iBAAiB,MAAM;AAClD,wBAAY,KAAK;AAAA;AAAA;AAIrB,gBAAQ,SAAS,EAAE,KAAK,QAAQ,OAAO,OAAO;AAAA;AAGhD,UAAI,aAAa;AACf,YAAI,QAAQ,UAAU;AAGpB,kBAAQ,OAAO,EAAE,WAAW,OAAO,KAAK,KAAK,YAAY,gBAAgB;AACzE,gBAAM,KAAK,SAAS;AACpB,iBAAO,QAAQ;AAAA;AAAA;AAAA;AAIrB,QAAI,QAAQ,OAAO,UAAU,KAAK,eAAe,KAAK,SAAS,WAAW,KAAK,SAAS,QAAQ,QAAQ;AACtG,YAAM,QAAQ,IAAI,KAAK,SAAS,QAAQ,OAAO,aAAW,QAAQ,uBAAuB,WAAW,IAAI,OAAM,YAAW;AACvH,cAAM,WAAW,KAAK,IAAI,QAAQ;AAClC,YAAI,CAAC;AAAU;AAEf,cAAM,iBAAiB,EAAE,MAAM,UAAU,UACtC,KAAK,CAAC,gBACN,SAAS;AAAA,UACR,aAAa,QAAQ;AAAA,UACrB,SAAS,QAAQ;AAAA,UACjB,cAAc;AAAA,WACb;AAEL,cAAM,SAAS,KAAK;AAEpB,cAAM,KAAK,QAAQ,YAAY,UAAU,KAAK,UAAU,EAAE,MAAM,OAAO,SAAS,QAAQ;AAAA;AAAA;AAG5F,UAAM,aAAa,QAAQ,OAAO,OAAO,WAAS,CAAC,KAAK,YAAY,mBAAmB,IAAI;AAC3F,QAAI,CAAC,WAAW;AAAQ,aAAO;AAC/B,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK;AAAa,aAAO;AAEjD,UAAM,mBAAmB,EAAE,IAAI,KAAK,YAAY,cAAc,cAAc,YAAY;AACxF,UAAM,SAAS,MAAM,mBAAmB,KAAK,YAAY,QAAQ,QAAQ,KAAK;AAC9E,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,QAAI;AAEJ,QAAI,KAAK,aAAa;AACpB,cAAQ;AACR,aAAO,CAAC,MAAM,KAAK,YAAY,aAAa,UAAU,QAAQ;AAAA,WACzD;AACL,cAAQ,KAAK,MAAM;AACnB,UAAI,aAAa;AACf,eAAO,oBAAoB,SAAS,OAAO,mBAAmB,MAAM;AAAA;AAEtE,cAAQ;AACR,aAAO,CAAC,MAAM,KAAK,YAAY,aAAa,UAAU,QAAQ,OAAO;AAAA;AAGvE,UAAM,CAAC,QAAQ,eAAe,MAAM,KAAK,YAAY,eAAe,OAAO,GAAG;AAC9E,QAAI,aAAa;AAEf,UAAI,cAAc,GAAG;AACnB,cAAM,IAAI,gBAAgB,oBAAoB;AAAA,UAC5C,WAAW,KAAK,YAAY;AAAA,UAC5B;AAAA,UACA;AAAA;AAAA,aAEG;AACL,eAAO,WAAW,eAAe,OAAO;AAAA;AAAA;AAK5C,eAAW,QAAQ,OAAO,KAAK,KAAK,YAAY,gBAAgB;AAC9D,UAAI,KAAK,YAAY,cAAc,MAAM,SACrC,OAAO,KAAK,YAAY,cAAc,MAAM,WAAW,UACvD,KAAK,YAAY,cAAc,MAAM,UAAU,MACjD;AACA,eAAO,QAAQ,OAAO,KAAK,YAAY,cAAc,MAAM;AAC3D,eAAO,OAAO,KAAK,YAAY,cAAc,MAAM;AAAA;AAAA;AAGvD,WAAO,OAAO,QAAQ,OAAO;AAE7B,WAAO,OAAO,OAAO,YAAY;AACjC,QAAI,gBAAgB,KAAK,SAAS,WAAW,KAAK,SAAS,QAAQ,QAAQ;AACzE,YAAM,QAAQ,IACZ,KAAK,SAAS,QAAQ,OAAO,aAAW,CAAE,SAAQ,uBAAuB,aACvE,QAAQ,UAAU,QAAQ,OAAO,uBAAuB,gBAAgB,IAAI,OAAM,YAAW;AAC7F,YAAI,YAAY,KAAK,IAAI,QAAQ;AAEjC,YAAI,CAAC;AAAW;AAChB,YAAI,CAAC,MAAM,QAAQ;AAAY,sBAAY,CAAC;AAE5C,cAAM,iBAAiB,EAAE,MAAM,UAAU,UACtC,KAAK,CAAC,gBACN,SAAS;AAAA,UACR,aAAa,QAAQ;AAAA,UACrB,SAAS,QAAQ;AAAA,UACjB,cAAc;AAAA,WACb;AAGL,cAAM,QAAQ,IAAI,UAAU,IAAI,OAAM,aAAY;AAChD,cAAI,QAAQ,uBAAuB,eAAe;AAChD,kBAAM,SAAS,KAAK;AACpB,kBAAM,UAAU;AAAA,eACb,QAAQ,YAAY,aAAa,KAAK,IAAI,KAAK,YAAY,qBAAqB,EAAE,KAAK;AAAA,eACvF,QAAQ,YAAY,WAAW,SAAS,IAAI,SAAS,YAAY,qBAAqB,EAAE,KAAK;AAAA,eAE3F,QAAQ,YAAY,QAAQ;AAGjC,gBAAI,SAAS,QAAQ,YAAY,QAAQ,MAAM,OAAO;AACpD,yBAAW,QAAQ,OAAO,KAAK,QAAQ,YAAY,QAAQ,MAAM,gBAAgB;AAC/E,oBAAI,QAAQ,YAAY,QAAQ,MAAM,cAAc,MAAM,kBACxD,SAAS,QAAQ,YAAY,cAC7B,SAAS,QAAQ,YAAY,YAC7B,OAAO,SAAS,QAAQ,YAAY,QAAQ,MAAM,MAAM,UAAU,aAAa;AAC/E;AAAA;AAEF,wBAAQ,QAAQ,SAAS,QAAQ,YAAY,QAAQ,MAAM,MAAM;AAAA;AAAA;AAIrE,kBAAM,QAAQ,YAAY,aAAa,OAAO,SAAS;AAAA,iBAClD;AACL,qBAAS,IAAI,QAAQ,YAAY,YAAY,KAAK,IAAI,QAAQ,YAAY,aAAa,KAAK,YAAY,qBAAqB,EAAE,KAAK,SAAS,EAAE,KAAK;AACpJ,mBAAO,OAAO,UAAU,QAAQ,YAAY;AAC5C,kBAAM,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAO9B,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,YAAY,SAAS,QAAQ,QAAQ,QAAQ;AAAA;AAE1D,eAAW,SAAS,QAAQ,QAAQ;AAClC,aAAO,oBAAoB,SAAS,OAAO,WAAW;AACtD,WAAK,QAAQ,OAAO;AAAA;AAEtB,SAAK,cAAc;AAEnB,WAAO;AAAA;AAAA,QAgBH,OAAO,SAAS;AACpB,cAAU,MAAM,SAAS;AAAA,MACvB,OAAO,KAAK;AAAA,OACX,SAAS;AAAA,MACV,SAAS,KAAK,SAAS,WAAW;AAAA;AAGpC,UAAM,WAAW,MAAM,KAAK,YAAY,QAAQ;AAChD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,gBAAgB,cACxB;AAAA;AAIJ,SAAK,WAAW,SAAS;AAEzB,SAAK,IAAI,SAAS,YAAY;AAAA,MAC5B,KAAK;AAAA,MACL,OAAe,CAAC,QAAQ;AAAA;AAG1B,WAAO;AAAA;AAAA,QAeH,SAAS,SAAS;AACtB,WAAO,IAAI,kBAAkB,MAAM,SAAS;AAAA;AAAA,QAiBxC,OAAO,QAAQ,SAAS;AAE5B,aAAS,EAAE,OAAO,QAAQ,WAAS,UAAU;AAE7C,UAAM,gBAAgB,KAAK,aAAa;AAExC,cAAU,WAAW;AACrB,QAAI,MAAM,QAAQ;AAAU,gBAAU,EAAE,QAAQ;AAEhD,cAAU,MAAM,UAAU;AAG1B,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAI1B,UAAM,aAAa,MAAM,UAAU;AACnC,eAAW,aAAa,QAAQ;AAChC,SAAK,IAAI,QAAQ;AAGjB,UAAM,cAAc,EAAE,QAAQ,KAAK,WAAW,GAAG;AACjD,UAAM,SAAS,EAAE,MAAM,OAAO,KAAK,SAAS;AAE5C,QAAI,CAAC,QAAQ,QAAQ;AACnB,cAAQ,SAAS,EAAE,aAAa,QAAQ,KAAK;AAC7C,cAAQ,gBAAgB,QAAQ;AAAA;AAGlC,WAAO,MAAM,KAAK,KAAK;AAAA;AAAA,QAcnB,QAAQ,SAAS;AACrB,cAAU;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,OACJ;AAIL,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAK1B,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,YAAY,SAAS,iBAAiB,MAAM;AAAA;AAEzD,UAAM,QAAQ,KAAK,MAAM;AAEzB,QAAI;AACJ,QAAI,KAAK,YAAY,qBAAqB,aAAa,QAAQ,UAAU,OAAO;AAC9E,YAAM,gBAAgB,KAAK,YAAY,qBAAqB;AAC5D,YAAM,YAAY,KAAK,YAAY,cAAc;AACjD,YAAM,eAAe,OAAO,UAAU,eAAe,KAAK,WAAW,kBACjE,UAAU,eACV;AACJ,YAAM,eAAe,KAAK,aAAa;AACvC,YAAM,kBAAkB,gBAAgB,QAAQ,gBAAgB;AAChE,UAAI,mBAAmB,EAAE,QAAQ,cAAc,eAAe;AAE5D,aAAK,aAAa,eAAe,IAAI;AAAA;AAGvC,eAAS,MAAM,KAAK,KAAK,iCAAK,UAAL,EAAc,OAAO;AAAA,WACzC;AACL,eAAS,MAAM,KAAK,YAAY,eAAe,OAAO,MAAM,KAAK,YAAY,aAAa,UAAU,OAAO,iBAAE,MAAM,WAAW,QAAQ,OAAO,QAAS;AAAA;AAGxJ,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,YAAY,SAAS,gBAAgB,MAAM;AAAA;AAExD,WAAO;AAAA;AAAA,EAUT,gBAAgB;AACd,QAAI,CAAC,KAAK,YAAY,qBAAqB,WAAW;AACpD,YAAM,IAAI,MAAM;AAAA;AAGlB,UAAM,qBAAqB,KAAK,YAAY,cAAc,KAAK,YAAY,qBAAqB;AAChG,UAAM,eAAe,OAAO,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,mBAAmB,eAAe;AAClI,UAAM,YAAY,KAAK,IAAI,KAAK,YAAY,qBAAqB,cAAc;AAC/E,UAAM,QAAQ,cAAc;AAE5B,WAAO;AAAA;AAAA,QAYH,QAAQ,SAAS;AACrB,QAAI,CAAC,KAAK,YAAY,qBAAqB;AAAW,YAAM,IAAI,MAAM;AAEtE,cAAU;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,OACJ;AAIL,QAAI,QAAQ,gBAAgB,UAAa,KAAK,UAAU,YAAY,MAAM;AACxE,YAAM,IAAI,KAAK,UAAU,YAAY,KAAK,IAAI;AAC9C,UAAI,GAAG;AACL,gBAAQ,cAAc;AAAA;AAAA;AAK1B,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,YAAY,SAAS,iBAAiB,MAAM;AAAA;AAEzD,UAAM,eAAe,KAAK,YAAY,qBAAqB;AAC3D,UAAM,qBAAqB,KAAK,YAAY,cAAc;AAC1D,UAAM,wBAAwB,OAAO,UAAU,eAAe,KAAK,oBAAoB,kBAAkB,mBAAmB,eAAe;AAE3I,SAAK,aAAa,cAAc;AAChC,UAAM,SAAS,MAAM,KAAK,KAAK,iCAAK,UAAL,EAAc,OAAO,OAAO,UAAU;AAErE,QAAI,QAAQ,OAAO;AACjB,YAAM,KAAK,YAAY,SAAS,gBAAgB,MAAM;AACtD,aAAO;AAAA;AAET,WAAO;AAAA;AAAA,QAkCH,UAAU,QAAQ,SAAS;AAC/B,UAAM,aAAa,KAAK;AAExB,cAAU,MAAM,UAAU;AAC1B,YAAQ,QAAQ,kCAAK,QAAQ,QAAU;AACvC,YAAQ,WAAW;AAEnB,UAAM,KAAK,YAAY,UAAU,QAAQ;AAEzC,WAAO;AAAA;AAAA,QAgCH,UAAU,QAAQ,SAAS;AAC/B,WAAO,KAAK,UAAU,QAAQ;AAAA,MAC5B,IAAI;AAAA,OACD,UAFyB;AAAA,MAG5B,WAAW;AAAA;AAAA;AAAA,EAWf,OAAO,OAAO;AACZ,QAAI,CAAC,SAAS,CAAC,MAAM,aAAa;AAChC,aAAO;AAAA;AAGT,QAAI,CAAE,kBAAiB,KAAK,cAAc;AACxC,aAAO;AAAA;AAGT,WAAO,KAAK,YAAY,qBAAqB,MAAM,eAAa,KAAK,IAAI,WAAW,EAAE,KAAK,YAAY,MAAM,IAAI,WAAW,EAAE,KAAK;AAAA;AAAA,EAUrI,YAAY,QAAQ;AAClB,WAAO,OAAO,KAAK,WAAS,KAAK,OAAO;AAAA;AAAA,EAG1C,cAAc,WAAW,YAAY;AACnC,SAAK,WAAW,aAAa;AAAA;AAAA,EAa/B,SAAS;AACP,WAAO,EAAE,UACP,KAAK,IAAI;AAAA,MACP,OAAO;AAAA;AAAA;AAAA,SAyBN,QAAQ,QAAQ,SAAS;AAAA;AAAA,SAoCzB,cAAc,QAAQ,SAAS;AAAA;AAAA,SAqB/B,OAAO,QAAQ,SAAS;AAAA;AAAA,SAoBxB,UAAU,QAAQ,SAAS;AAAA;AAAA;AAepC,mBAAmB,OAAO;AACxB,MAAI,CAAC,EAAE,SAAS,QAAQ;AACtB,WAAO;AAAA;AAGT,QAAM,OAAO,MAAM,eAAe;AAGlC,MAAI,KAAK,WAAW,GAAG;AACrB;AAAA;AAIF,MAAI,KAAK,WAAW,KAAK,KAAK,OAAO,GAAG,KAAK;AAC3C,WAAO;AAAA;AAGT,QAAM,WAAW,MAAM,GAAG;AAE1B,SAAO;AAAA;AAGT,8BAA8B,QAAQ,QAAQ;AAC5C,QAAM,YAAY,UAAU;AAE5B,MAAI,cAAc,QAAW;AAC3B,WAAO;AAAA;AAGT,QAAM,YAAY,UAAU;AAE5B,MAAI,cAAc,QAAW;AAC3B,WAAO;AAAA;AAGT,SAAO;AAAA,KACJ,GAAG,MAAM,EAAE,QAAQ,CAAC,WAAW;AAAA;AAAA;AAIpC,OAAO,OAAO,OAAO;AACrB,MAAM,QAAQ,OAAO;AAErB,OAAO,UAAU;", "names": []}