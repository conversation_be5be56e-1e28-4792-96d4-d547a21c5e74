graph TD
    %% 应用入口
    A[app.js] --> B[utils/dataManager.js]
    
    %% 工具类模块
    B[utils/dataManager.js] <--> C[utils/authManager.js]
    
    %% 个人中心页面
    D[pages/profile/profile.js] --> B
    D --> C
    
    %% 子页面
    D --> E[pages/profile/subpages/vehicles/vehicles.js]
    D --> F[pages/profile/subpages/approvers/approvers.js]
    D --> G[pages/profile/subpages/settings/settings.js]
    D --> H[pages/profile/subpages/userInfo/userInfo.js]
    D --> I[pages/profile/subpages/carHistory/carHistory.js]
    
    %% 数据管理
    E --> B
    F --> B
    G --> B
    H --> B
    I --> B
    
    %% 权限管理
    E --> C
    F --> C
    
    %% 样式文件关系
    E -.-> E1[vehicles.wxml/wxss]
    F -.-> F1[approvers.wxml/wxss]
    G -.-> G1[settings.wxml/wxss]
    H -.-> H1[userInfo.wxml/wxss]
    I -.-> I1[carHistory.wxml/wxss]
    D -.-> D1[profile.wxml/wxss]

    classDef core fill:#f9f,stroke:#333,stroke-width:2px;
    classDef util fill:#bbf,stroke:#333,stroke-width:2px;
    classDef page fill:#bfb,stroke:#333,stroke-width:2px;
    classDef style fill:#ddd,stroke:#333,stroke-width:1px;
    
    class A,D core;
    class B,C util;
    class E,F,G,H,I page;
    class D1,E1,F1,G1,H1,I1 style;