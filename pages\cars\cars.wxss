
.container {
  padding: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 30rpx 0;
  color: #333;
}

.filter-container {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 0;
  position: relative;
}

.filter-item.active {
  color: #1296db;
  font-weight: bold;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1296db;
  border-radius: 2rpx;
}

.vehicle-list {
  padding: 0 10rpx;
}

.vehicle-item {
  background: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.vehicle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #eee;
}

.plate-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status.available {
  background-color: #e8f7ee;
  color: #07c160;
}

.status.inuse {
  background-color: #e6f4ff;
  color: #1296db;
}

.status.maintenance {
  background-color: #fff2e8;
  color: #fa8c16;
}

.vehicle-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.info-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.vehicle-footer {
  text-align: right;
  padding-top: 10rpx;
  border-top: 1rpx solid #eee;
}

.view-detail {
  font-size: 26rpx;
  color: #1296db;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1296db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 错误提示 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-text {
  margin: 20rpx 0;
  font-size: 28rpx;
  color: #ff4d4f;
  text-align: center;
}

.retry-button {
  margin-top: 20rpx;
  padding: 16rpx 40rpx;
  background-color: #1296db;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: none;
}

.retry-button:active {
  opacity: 0.8;
}

/* 空状态优化 */
.empty-list {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 下拉刷新提示 */
.refresh-tip {
  text-align: center;
  padding: 20rpx;
  color: #666;
  font-size: 24rpx;
  background-color: #f8f8f8;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  transform: translateY(-100%);
  transition: transform 0.3s;
}

.refresh-tip.visible {
  transform: translateY(0);
}

/* 列表动画 */
.vehicle-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}