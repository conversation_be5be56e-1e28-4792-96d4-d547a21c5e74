
<!--approvers.wxml-->
<view class="container">
  <!-- 审批人列表 -->
  <view class="approver-list" wx:if="{{!isEditing}}">
    <view class="header">
      <text class="title">审批人列表</text>
      <button class="add-btn" bindtap="addApprover">添加审批人</button>
    </view>
    
    <block wx:if="{{approvers.length > 0}}">
      <view class="approver-item" wx:for="{{approvers}}" wx:key="id">
        <view class="approver-info">
          <view class="name">{{item.name}}</view>
          <view class="details">
            <text class="role">{{item.role}}</text>
            <text class="department">{{item.department}}</text>
          </view>
        </view>
        <view class="actions">
          <button class="action-btn edit" bindtap="editApprover" data-id="{{item.id}}">编辑</button>
          <button class="action-btn delete" bindtap="deleteApprover" data-id="{{item.id}}">删除</button>
        </view>
      </view>
    </block>
    
    <view class="empty-tip" wx:else>
      <text>暂无审批人，请添加</text>
    </view>
  </view>

  <!-- 编辑表单 -->
  <view class="edit-form" wx:if="{{isEditing}}">
    <view class="form-header">
      <text class="title">{{currentApprover.id ? '编辑' : '添加'}}审批人</text>
    </view>
    
    <form bindsubmit="saveApprover">
      <view class="form-group">
        <view class="form-item">
          <text class="label">姓名</text>
          <input class="input" name="name" value="{{currentApprover.name}}" bindinput="inputChange" data-field="name" placeholder="请输入审批人姓名" />
        </view>
        
        <view class="form-item">
          <text class="label">职位</text>
          <input class="input" name="role" value="{{currentApprover.role}}" bindinput="inputChange" data-field="role" placeholder="请输入职位" />
        </view>
        
        <view class="form-item">
          <text class="label">部门</text>
          <input class="input" name="department" value="{{currentApprover.department}}" bindinput="inputChange" data-field="department" placeholder="请输入部门" />
        </view>
      </view>
      
      <view class="form-actions">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="save-btn" form-type="submit">保存</button>
      </view>
    </form>
  </view>
</view>