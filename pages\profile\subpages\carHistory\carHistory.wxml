
<!--carHistory.wxml-->
<view class="container">
  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view class="tab-item {{currentTab === 'pending' ? 'active' : ''}}" bindtap="switchTab" data-tab="pending">待审批</view>
    <view class="tab-item {{currentTab === 'completed' ? 'active' : ''}}" bindtap="switchTab" data-tab="completed">已完成</view>
    <view class="tab-item {{currentTab === 'rejected' ? 'active' : ''}}" bindtap="switchTab" data-tab="rejected">已拒绝</view>
  </view>

  <!-- 记录列表 -->
  <view class="record-list">
    <block wx:if="{{carUsageHistory.length > 0}}">
      <view class="record-item" wx:for="{{carUsageHistory}}" wx:key="id" bindtap="viewDetail" data-id="{{item.id}}">
        <view class="record-header">
          <text class="date">{{item.date}}</text>
          <text class="status {{item.status === '已完成' ? 'completed' : item.status === '已拒绝' ? 'rejected' : 'pending'}}">{{item.status}}</text>
        </view>
        <view class="record-body">
          <view class="record-info">
            <text class="label">用车原因：</text>
            <text class="value">{{item.reason}}</text>
          </view>
          <view class="record-info">
            <text class="label">车辆：</text>
            <text class="value">{{item.vehicle}}</text>
          </view>
          <view class="record-info">
            <text class="label">审批人：</text>
            <text class="value">{{item.approver}}</text>
          </view>
        </view>
      </view>
    </block>
    <view class="empty-tip" wx:else>
      <text>暂无用车记录</text>
    </view>
  </view>
</view>