
// API管理模块 - 负责处理所有与后端API的交互
const BASE_URL = 'http://127.0.0.1/appvue';

// 统一的请求方法
const request = async (path, options = {}) => {
  const url = `${BASE_URL}${path}`;
  const defaultOptions = {
    method: 'GET',
    header: {
      'Content-Type': 'application/json'
    }
  };

  // 合并默认选项和传入的选项
  const requestOptions = {
    ...defaultOptions,
    ...options,
    header: {
      ...defaultOptions.header,
      ...options.header
    }
  };

  try {
    const response = await new Promise((resolve, reject) => {
      wx.request({
        url,
        ...requestOptions,
        success: resolve,
        fail: reject
      });
    });

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response.data;
    } else {
      throw new Error(`请求失败: ${response.statusCode}`);
    }
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// API端点
const api = {
  // 用户相关
  user: {
    // 获取用户信息
    getUserInfo: () => request('/user/info'),
    // 更新用户信息
    updateUserInfo: (data) => request('/user/info', {
      method: 'PUT',
      data
    })
  },

  // 审批人相关
  approvers: {
    // 获取审批人列表
    getApprovers: () => request('/approvers'),
    // 添加审批人
    addApprover: (data) => request('/approvers', {
      method: 'POST',
      data
    }),
    // 更新审批人信息
    updateApprover: (id, data) => request(`/approvers/${id}`, {
      method: 'PUT',
      data
    }),
    // 删除审批人
    deleteApprover: (id) => request(`/approvers/${id}`, {
      method: 'DELETE'
    })
  },

  // 车辆相关
  vehicles: {
    // 获取车辆列表
    getVehicles: () => request('/vehicles'),
    // 获取单个车辆信息
    getVehicle: (id) => request(`/vehicles/${id}`),
    // 添加车辆
    addVehicle: (data) => request('/vehicles', {
      method: 'POST',
      data
    }),
    // 更新车辆信息
    updateVehicle: (id, data) => request(`/vehicles/${id}`, {
      method: 'PUT',
      data
    }),
    // 删除车辆
    deleteVehicle: (id) => request(`/vehicles/${id}`, {
      method: 'DELETE'
    })
  },

  // 用车历史相关
  carUsage: {
    // 获取用车历史列表
    getHistory: () => request('/car-usage/history'),
    // 添加用车记录
    addRecord: (data) => request('/car-usage/record', {
      method: 'POST',
      data
    }),
    // 更新用车记录
    updateRecord: (id, data) => request(`/car-usage/record/${id}`, {
      method: 'PUT',
      data
    })
  },

  // 通知设置相关
  settings: {
    // 获取通知设置
    getNotificationSettings: () => request('/settings/notifications'),
    // 更新通知设置
    updateNotificationSettings: (data) => request('/settings/notifications', {
      method: 'PUT',
      data
    })
  }
};

module.exports = api;