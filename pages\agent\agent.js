// agent.js
const dataManager = require('../../utils/dataManager');

Page({
  data: {
    faqs: []
  },

  onLoad: function() {
    // 从本地数据获取 FAQs
    const faqs = dataManager.getData('faqs');
    if (faqs && faqs.length > 0) {
      this.setData({
        faqs: faqs
      });
    }
  },

  // 添加新的 FAQ
  addFaq: function(faq) {
    dataManager.addRecord('faqs', faq);
    // 重新加载数据
    this.setData({
      faqs: dataManager.getData('faqs')
    });
  },

  // 更新 FAQ
  updateFaq: function(id, newData) {
    dataManager.updateRecord('faqs', id, newData);
    // 重新加载数据
    this.setData({
      faqs: dataManager.getData('faqs')
    });
  },

  // 删除 FAQ
  deleteFaq: function(id) {
    const faq = this.data.faqs.find(item => item.id === id);
    
    if (!faq) {
      wx.showToast({
        title: 'FAQ不存在',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除FAQ: "${faq.question}"吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });
          
          try {
            const success = dataManager.deleteRecord('faqs', id);
            if (success) {
              // 重新加载数据
              this.setData({
                faqs: dataManager.getData('faqs')
              });
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              throw new Error('删除操作失败');
            }
          } catch (err) {
            console.error('删除FAQ失败:', err);
            wx.showToast({
              title: '删除失败: ' + (err.message || '未知错误'),
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
          }
        }
      }
    });
  }
})