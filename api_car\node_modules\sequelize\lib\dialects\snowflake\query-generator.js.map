{"version": 3, "sources": ["../../../src/dialects/snowflake/query-generator.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('lodash');\nconst Utils = require('../../utils');\nconst AbstractQueryGenerator = require('../abstract/query-generator');\nconst util = require('util');\nconst Op = require('../../operators');\n\n\nconst JSON_FUNCTION_REGEX = /^\\s*((?:[a-z]+_){0,2}jsonb?(?:_[a-z]+){0,2})\\([^)]*\\)/i;\nconst JSON_OPERATOR_REGEX = /^\\s*(->>?|@>|<@|\\?[|&]?|\\|{2}|#-)/i;\nconst TOKEN_CAPTURE_REGEX = /^\\s*((?:([`\"'])(?:(?!\\2).|\\2{2})*\\2)|[\\w\\d\\s]+|[().,;+-])/i;\nconst FOREIGN_KEY_FIELDS = [\n  'CONSTRAINT_NAME as constraint_name',\n  'CONSTRAINT_NAME as constraintName',\n  'CONSTRAINT_SCHEMA as constraintSchema',\n  'CONSTRAINT_SCHEMA as constraintCatalog',\n  'TABLE_NAME as tableName',\n  'TABLE_SCHEMA as tableSchema',\n  'TABLE_SCHEMA as tableCatalog',\n  'COLUMN_NAME as columnName',\n  'REFERENCED_TABLE_SCHEMA as referencedTableSchema',\n  'REFERENCED_TABLE_SCHEMA as referencedTableCatalog',\n  'REFERENCED_TABLE_NAME as referencedTableName',\n  'REFERENCED_COLUMN_NAME as referencedColumnName'\n].join(',');\n\n/**\n * list of reserved words in Snowflake\n * source: https://docs.snowflake.com/en/sql-reference/reserved-keywords.html\n *\n * @private\n */\nconst SNOWFLAKE_RESERVED_WORDS = 'account,all,alter,and,any,as,between,by,case,cast,check,column,connect,connections,constraint,create,cross,current,current_date,current_time,current_timestamp,current_user,database,delete,distinct,drop,else,exists,false,following,for,from,full,grant,group,gscluster,having,ilike,in,increment,inner,insert,intersect,into,is,issue,join,lateral,left,like,localtime,localtimestamp,minus,natural,not,null,of,on,or,order,organization,qualify,regexp,revoke,right,rlike,row,rows,sample,schema,select,set,some,start,table,tablesample,then,to,trigger,true,try_cast,union,unique,update,using,values,view,when,whenever,where,with'.split(',');\n\nconst typeWithoutDefault = new Set(['BLOB', 'TEXT', 'GEOMETRY', 'JSON']);\n\nclass SnowflakeQueryGenerator extends AbstractQueryGenerator {\n  constructor(options) {\n    super(options);\n\n    this.OperatorMap = {\n      ...this.OperatorMap,\n      [Op.regexp]: 'REGEXP',\n      [Op.notRegexp]: 'NOT REGEXP'\n    };\n  }\n\n  createDatabaseQuery(databaseName, options) {\n    options = {\n      charset: null,\n      collate: null,\n      ...options\n    };\n\n    return Utils.joinSQLFragments([\n      'CREATE DATABASE IF NOT EXISTS',\n      this.quoteIdentifier(databaseName),\n      options.charset && `DEFAULT CHARACTER SET ${this.escape(options.charset)}`,\n      options.collate && `DEFAULT COLLATE ${this.escape(options.collate)}`,\n      ';'\n    ]);\n  }\n\n  dropDatabaseQuery(databaseName) {\n    return `DROP DATABASE IF EXISTS ${this.quoteIdentifier(databaseName)};`;\n  }\n\n  createSchema() {\n    return 'SHOW TABLES';\n  }\n\n  showSchemasQuery() {\n    return 'SHOW TABLES';\n  }\n\n  versionQuery() {\n    return 'SELECT CURRENT_VERSION()';\n  }\n\n  createTableQuery(tableName, attributes, options) {\n    options = {\n      charset: null,\n      rowFormat: null,\n      ...options\n    };\n\n    const primaryKeys = [];\n    const foreignKeys = {};\n    const attrStr = [];\n\n    for (const attr in attributes) {\n      if (!Object.prototype.hasOwnProperty.call(attributes, attr)) continue;\n      const dataType = attributes[attr];\n      let match;\n\n      if (dataType.includes('PRIMARY KEY')) {\n        primaryKeys.push(attr);\n\n        if (dataType.includes('REFERENCES')) {\n          match = dataType.match(/^(.+) (REFERENCES.*)$/);\n          attrStr.push(`${this.quoteIdentifier(attr)} ${match[1].replace('PRIMARY KEY', '')}`);\n          foreignKeys[attr] = match[2];\n        } else {\n          attrStr.push(`${this.quoteIdentifier(attr)} ${dataType.replace('PRIMARY KEY', '')}`);\n        }\n      } else if (dataType.includes('REFERENCES')) {\n        match = dataType.match(/^(.+) (REFERENCES.*)$/);\n        attrStr.push(`${this.quoteIdentifier(attr)} ${match[1]}`);\n        foreignKeys[attr] = match[2];\n      } else {\n        attrStr.push(`${this.quoteIdentifier(attr)} ${dataType}`);\n      }\n    }\n\n    const table = this.quoteTable(tableName);\n    let attributesClause = attrStr.join(', ');\n    const pkString = primaryKeys.map(pk => this.quoteIdentifier(pk)).join(', ');\n\n    if (options.uniqueKeys) {\n      _.each(options.uniqueKeys, (columns, indexName) => {\n        if (columns.customIndex) {\n          if (typeof indexName !== 'string') {\n            indexName = `uniq_${tableName}_${columns.fields.join('_')}`;\n          }\n          attributesClause += `, UNIQUE ${this.quoteIdentifier(indexName)} (${columns.fields.map(field => this.quoteIdentifier(field)).join(', ')})`;\n        }\n      });\n    }\n\n    if (pkString.length > 0) {\n      attributesClause += `, PRIMARY KEY (${pkString})`;\n    }\n\n    for (const fkey in foreignKeys) {\n      if (Object.prototype.hasOwnProperty.call(foreignKeys, fkey)) {\n        attributesClause += `, FOREIGN KEY (${this.quoteIdentifier(fkey)}) ${foreignKeys[fkey]}`;\n      }\n    }\n\n    return Utils.joinSQLFragments([\n      'CREATE TABLE IF NOT EXISTS',\n      table,\n      `(${attributesClause})`,\n      options.comment && typeof options.comment === 'string' && `COMMENT ${this.escape(options.comment)}`,\n      options.charset && `DEFAULT CHARSET=${options.charset}`,\n      options.collate && `COLLATE ${options.collate}`,\n      options.rowFormat && `ROW_FORMAT=${options.rowFormat}`,\n      ';'\n    ]);\n  }\n\n  describeTableQuery(tableName, schema, schemaDelimiter) {\n    const table = this.quoteTable(\n      this.addSchema({\n        tableName,\n        _schema: schema,\n        _schemaDelimiter: schemaDelimiter\n      })\n    );\n\n    return `SHOW FULL COLUMNS FROM ${table};`;\n  }\n\n  showTablesQuery(database) {\n    return Utils.joinSQLFragments([\n      'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = \\'BASE TABLE\\'',\n      database ? `AND TABLE_SCHEMA = ${this.escape(database)}` : 'AND TABLE_SCHEMA NOT IN ( \\'INFORMATION_SCHEMA\\', \\'PERFORMANCE_SCHEMA\\', \\'SYS\\')',\n      ';'\n    ]);\n  }\n\n  tableExistsQuery(table) {\n    const tableName = table.tableName || table;\n    const schema = table.schema;\n\n    return Utils.joinSQLFragments([\n      'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = \\'BASE TABLE\\'',\n      `AND TABLE_SCHEMA = ${schema !== undefined ? this.escape(schema) : 'CURRENT_SCHEMA()'}`,\n      `AND TABLE_NAME = ${this.escape(tableName)}`,\n      ';'\n    ]);\n  }\n\n  addColumnQuery(table, key, dataType) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(table),\n      'ADD',\n      this.quoteIdentifier(key),\n      this.attributeToSQL(dataType, {\n        context: 'addColumn',\n        tableName: table,\n        foreignKey: key\n      }),\n      ';'\n    ]);\n  }\n\n  removeColumnQuery(tableName, attributeName) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP',\n      this.quoteIdentifier(attributeName),\n      ';'\n    ]);\n  }\n\n  changeColumnQuery(tableName, attributes) {\n    const query = (...subQuerys) => Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'ALTER COLUMN',\n      ...subQuerys,\n      ';'\n    ]);\n    const sql = [];\n    for (const attributeName in attributes) {\n      let definition = this.dataTypeMapping(tableName, attributeName, attributes[attributeName]);\n      const attrSql = [];\n\n      if (definition.includes('NOT NULL')) {\n        attrSql.push(query(this.quoteIdentifier(attributeName), 'SET NOT NULL'));\n\n        definition = definition.replace('NOT NULL', '').trim();\n      } else if (!definition.includes('REFERENCES')) {\n        attrSql.push(query(this.quoteIdentifier(attributeName), 'DROP NOT NULL'));\n      }\n\n      if (definition.includes('DEFAULT')) {\n        attrSql.push(query(this.quoteIdentifier(attributeName), 'SET DEFAULT', definition.match(/DEFAULT ([^;]+)/)[1]));\n\n        definition = definition.replace(/(DEFAULT[^;]+)/, '').trim();\n      } else if (!definition.includes('REFERENCES')) {\n        attrSql.push(query(this.quoteIdentifier(attributeName), 'DROP DEFAULT'));\n      }\n\n      if (definition.match(/UNIQUE;*$/)) {\n        definition = definition.replace(/UNIQUE;*$/, '');\n        attrSql.push(query('ADD UNIQUE (', this.quoteIdentifier(attributeName), ')').replace('ALTER COLUMN', ''));\n      }\n\n      if (definition.includes('REFERENCES')) {\n        definition = definition.replace(/.+?(?=REFERENCES)/, '');\n        attrSql.push(query('ADD FOREIGN KEY (', this.quoteIdentifier(attributeName), ')', definition).replace('ALTER COLUMN', ''));\n      } else {\n        attrSql.push(query(this.quoteIdentifier(attributeName), 'TYPE', definition));\n      }\n\n      sql.push(attrSql.join(''));\n    }\n\n    return sql.join('');\n  }\n\n  renameColumnQuery(tableName, attrBefore, attributes) {\n    const attrString = [];\n\n    for (const attrName in attributes) {\n      const definition = attributes[attrName];\n      attrString.push(`'${attrBefore}' '${attrName}' ${definition}`);\n    }\n\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'RENAME COLUMN',\n      attrString.join(' to '),\n      ';'\n    ]);\n  }\n\n  handleSequelizeMethod(attr, tableName, factory, options, prepend) {\n    if (attr instanceof Utils.Json) {\n      // Parse nested object\n      if (attr.conditions) {\n        const conditions = this.parseConditionObject(attr.conditions).map(condition =>\n          `${this.jsonPathExtractionQuery(condition.path[0], _.tail(condition.path))} = '${condition.value}'`\n        );\n\n        return conditions.join(' AND ');\n      }\n      if (attr.path) {\n        let str;\n\n        // Allow specifying conditions using the sqlite json functions\n        if (this._checkValidJsonStatement(attr.path)) {\n          str = attr.path;\n        } else {\n          // Also support json property accessors\n          const paths = _.toPath(attr.path);\n          const column = paths.shift();\n          str = this.jsonPathExtractionQuery(column, paths);\n        }\n\n        if (attr.value) {\n          str += util.format(' = %s', this.escape(attr.value));\n        }\n\n        return str;\n      }\n    } else if (attr instanceof Utils.Cast) {\n      if (/timestamp/i.test(attr.type)) {\n        attr.type = 'datetime';\n      } else if (attr.json && /boolean/i.test(attr.type)) {\n        // true or false cannot be casted as booleans within a JSON structure\n        attr.type = 'char';\n      } else if (/double precision/i.test(attr.type) || /boolean/i.test(attr.type) || /integer/i.test(attr.type)) {\n        attr.type = 'decimal';\n      } else if (/text/i.test(attr.type)) {\n        attr.type = 'char';\n      }\n    }\n\n    return super.handleSequelizeMethod(attr, tableName, factory, options, prepend);\n  }\n\n  truncateTableQuery(tableName) {\n    return Utils.joinSQLFragments([\n      'TRUNCATE',\n      this.quoteTable(tableName)\n    ]);\n  }\n\n  deleteQuery(tableName, where, options = {}, model) {\n    const table = this.quoteTable(tableName);\n    let whereClause = this.getWhereConditions(where, null, model, options);\n    const limit = options.limit && ` LIMIT ${this.escape(options.limit)}`;\n    let primaryKeys = '';\n    let primaryKeysSelection = '';\n\n    if (whereClause) {\n      whereClause = `WHERE ${whereClause}`;\n    }\n\n    if (limit) {\n      if (!model) {\n        throw new Error('Cannot LIMIT delete without a model.');\n      }\n\n      const pks = Object.values(model.primaryKeys).map(pk => this.quoteIdentifier(pk.field)).join(',');\n\n      primaryKeys = model.primaryKeyAttributes.length > 1 ? `(${pks})` : pks;\n      primaryKeysSelection = pks;\n\n      return Utils.joinSQLFragments([\n        'DELETE FROM',\n        table,\n        'WHERE',\n        primaryKeys,\n        'IN (SELECT',\n        primaryKeysSelection,\n        'FROM',\n        table,\n        whereClause,\n        limit,\n        ')',\n        ';'\n      ]);\n    }\n    return Utils.joinSQLFragments([\n      'DELETE FROM',\n      table,\n      whereClause,\n      ';'\n    ]);\n  }\n\n  showIndexesQuery() {\n    return 'SELECT \\'\\' FROM DUAL';\n  }\n\n  showConstraintsQuery(table, constraintName) {\n    const tableName = table.tableName || table;\n    const schemaName = table.schema;\n\n    return Utils.joinSQLFragments([\n      'SELECT CONSTRAINT_CATALOG AS constraintCatalog,',\n      'CONSTRAINT_NAME AS constraintName,',\n      'CONSTRAINT_SCHEMA AS constraintSchema,',\n      'CONSTRAINT_TYPE AS constraintType,',\n      'TABLE_NAME AS tableName,',\n      'TABLE_SCHEMA AS tableSchema',\n      'from INFORMATION_SCHEMA.TABLE_CONSTRAINTS',\n      `WHERE table_name='${tableName}'`,\n      constraintName && `AND constraint_name = '${constraintName}'`,\n      schemaName && `AND TABLE_SCHEMA = '${schemaName}'`,\n      ';'\n    ]);\n  }\n\n  removeIndexQuery(tableName, indexNameOrAttributes) {\n    let indexName = indexNameOrAttributes;\n\n    if (typeof indexName !== 'string') {\n      indexName = Utils.underscore(`${tableName}_${indexNameOrAttributes.join('_')}`);\n    }\n\n    return Utils.joinSQLFragments([\n      'DROP INDEX',\n      this.quoteIdentifier(indexName),\n      'ON',\n      this.quoteTable(tableName),\n      ';'\n    ]);\n  }\n\n  attributeToSQL(attribute, options) {\n    if (!_.isPlainObject(attribute)) {\n      attribute = {\n        type: attribute\n      };\n    }\n\n    const attributeString = attribute.type.toString({ escape: this.escape.bind(this) });\n    let template = attributeString;\n\n    if (attribute.allowNull === false) {\n      template += ' NOT NULL';\n    }\n\n    if (attribute.autoIncrement) {\n      template += ' AUTOINCREMENT';\n    }\n\n    // BLOB/TEXT/GEOMETRY/JSON cannot have a default value\n    if (!typeWithoutDefault.has(attributeString)\n      && attribute.type._binary !== true\n      && Utils.defaultValueSchemable(attribute.defaultValue)) {\n      template += ` DEFAULT ${this.escape(attribute.defaultValue)}`;\n    }\n\n    if (attribute.unique === true) {\n      template += ' UNIQUE';\n    }\n\n    if (attribute.primaryKey) {\n      template += ' PRIMARY KEY';\n    }\n\n    if (attribute.comment) {\n      template += ` COMMENT ${this.escape(attribute.comment)}`;\n    }\n\n    if (attribute.first) {\n      template += ' FIRST';\n    }\n    if (attribute.after) {\n      template += ` AFTER ${this.quoteIdentifier(attribute.after)}`;\n    }\n\n    if (attribute.references) {\n      if (options && options.context === 'addColumn' && options.foreignKey) {\n        const attrName = this.quoteIdentifier(options.foreignKey);\n        const fkName = this.quoteIdentifier(`${options.tableName}_${attrName}_foreign_idx`);\n\n        template += `, ADD CONSTRAINT ${fkName} FOREIGN KEY (${attrName})`;\n      }\n\n      template += ` REFERENCES ${this.quoteTable(attribute.references.model)}`;\n\n      if (attribute.references.key) {\n        template += ` (${this.quoteIdentifier(attribute.references.key)})`;\n      } else {\n        template += ` (${this.quoteIdentifier('id')})`;\n      }\n\n      if (attribute.onDelete) {\n        template += ` ON DELETE ${attribute.onDelete.toUpperCase()}`;\n      }\n\n      if (attribute.onUpdate) {\n        template += ` ON UPDATE ${attribute.onUpdate.toUpperCase()}`;\n      }\n    }\n\n    return template;\n  }\n\n  attributesToSQL(attributes, options) {\n    const result = {};\n\n    for (const key in attributes) {\n      const attribute = attributes[key];\n      result[attribute.field || key] = this.attributeToSQL(attribute, options);\n    }\n\n    return result;\n  }\n\n  /**\n   * Check whether the statmement is json function or simple path\n   *\n   * @param   {string}  stmt  The statement to validate\n   * @returns {boolean}       true if the given statement is json function\n   * @throws  {Error}         throw if the statement looks like json function but has invalid token\n   * @private\n   */\n  _checkValidJsonStatement(stmt) {\n    if (typeof stmt !== 'string') {\n      return false;\n    }\n\n    let currentIndex = 0;\n    let openingBrackets = 0;\n    let closingBrackets = 0;\n    let hasJsonFunction = false;\n    let hasInvalidToken = false;\n\n    while (currentIndex < stmt.length) {\n      const string = stmt.substr(currentIndex);\n      const functionMatches = JSON_FUNCTION_REGEX.exec(string);\n      if (functionMatches) {\n        currentIndex += functionMatches[0].indexOf('(');\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const operatorMatches = JSON_OPERATOR_REGEX.exec(string);\n      if (operatorMatches) {\n        currentIndex += operatorMatches[0].length;\n        hasJsonFunction = true;\n        continue;\n      }\n\n      const tokenMatches = TOKEN_CAPTURE_REGEX.exec(string);\n      if (tokenMatches) {\n        const capturedToken = tokenMatches[1];\n        if (capturedToken === '(') {\n          openingBrackets++;\n        } else if (capturedToken === ')') {\n          closingBrackets++;\n        } else if (capturedToken === ';') {\n          hasInvalidToken = true;\n          break;\n        }\n        currentIndex += tokenMatches[0].length;\n        continue;\n      }\n\n      break;\n    }\n\n    // Check invalid json statement\n    if (hasJsonFunction && (hasInvalidToken || openingBrackets !== closingBrackets)) {\n      throw new Error(`Invalid json statement: ${stmt}`);\n    }\n\n    // return true if the statement has valid json function\n    return hasJsonFunction;\n  }\n\n  dataTypeMapping(tableName, attr, dataType) {\n    if (dataType.includes('PRIMARY KEY')) {\n      dataType = dataType.replace('PRIMARY KEY', '');\n    }\n\n    if (dataType.includes('SERIAL')) {\n      if (dataType.includes('BIGINT')) {\n        dataType = dataType.replace('SERIAL', 'BIGSERIAL');\n        dataType = dataType.replace('BIGINT', '');\n      } else if (dataType.includes('SMALLINT')) {\n        dataType = dataType.replace('SERIAL', 'SMALLSERIAL');\n        dataType = dataType.replace('SMALLINT', '');\n      } else {\n        dataType = dataType.replace('INTEGER', '');\n      }\n      dataType = dataType.replace('NOT NULL', '');\n    }\n\n    return dataType;\n  }\n\n  /**\n   * Generates an SQL query that returns all foreign keys of a table.\n   *\n   * @param  {object} table  The table.\n   * @param  {string} schemaName The name of the schema.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  getForeignKeysQuery(table, schemaName) {\n    const tableName = table.tableName || table;\n    return Utils.joinSQLFragments([\n      'SELECT',\n      FOREIGN_KEY_FIELDS,\n      `FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = '${tableName}'`,\n      `AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='${schemaName}'`,\n      'AND REFERENCED_TABLE_NAME IS NOT NULL',\n      ';'\n    ]);\n  }\n\n  /**\n   * Generates an SQL query that returns the foreign key constraint of a given column.\n   *\n   * @param  {object} table  The table.\n   * @param  {string} columnName The name of the column.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  getForeignKeyQuery(table, columnName) {\n    const quotedSchemaName = table.schema ? wrapSingleQuote(table.schema) : '';\n    const quotedTableName = wrapSingleQuote(table.tableName || table);\n    const quotedColumnName = wrapSingleQuote(columnName);\n\n    return Utils.joinSQLFragments([\n      'SELECT',\n      FOREIGN_KEY_FIELDS,\n      'FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE',\n      'WHERE (',\n      [\n        `REFERENCED_TABLE_NAME = ${quotedTableName}`,\n        table.schema && `AND REFERENCED_TABLE_SCHEMA = ${quotedSchemaName}`,\n        `AND REFERENCED_COLUMN_NAME = ${quotedColumnName}`\n      ],\n      ') OR (',\n      [\n        `TABLE_NAME = ${quotedTableName}`,\n        table.schema && `AND TABLE_SCHEMA = ${quotedSchemaName}`,\n        `AND COLUMN_NAME = ${quotedColumnName}`,\n        'AND REFERENCED_TABLE_NAME IS NOT NULL'\n      ],\n      ')'\n    ]);\n  }\n\n  /**\n   * Generates an SQL query that removes a foreign key from a table.\n   *\n   * @param  {string} tableName  The name of the table.\n   * @param  {string} foreignKey The name of the foreign key constraint.\n   * @returns {string}            The generated sql query.\n   * @private\n   */\n  dropForeignKeyQuery(tableName, foreignKey) {\n    return Utils.joinSQLFragments([\n      'ALTER TABLE',\n      this.quoteTable(tableName),\n      'DROP FOREIGN KEY',\n      this.quoteIdentifier(foreignKey),\n      ';'\n    ]);\n  }\n\n  addLimitAndOffset(options) {\n    let fragment = [];\n    if (options.offset !== null && options.offset !== undefined && options.offset !== 0) {\n      fragment = fragment.concat([' LIMIT ', this.escape(options.limit), ' OFFSET ', this.escape(options.offset)]);\n    } else if ( options.limit !== null && options.limit !== undefined ) {\n      fragment = [' LIMIT ', this.escape(options.limit)];\n    }\n    return fragment.join('');\n  }\n\n  /**\n   * Quote identifier in sql clause\n   *\n   * @param {string} identifier\n   * @param {boolean} force\n   *\n   * @returns {string}\n   */\n  quoteIdentifier(identifier, force) {\n    const optForceQuote = force || false;\n    const optQuoteIdentifiers = this.options.quoteIdentifiers !== false;\n    const rawIdentifier = Utils.removeTicks(identifier, '\"');\n\n    if (\n      optForceQuote === true ||\n      optQuoteIdentifiers !== false ||\n      identifier.includes('.') ||\n      identifier.includes('->') ||\n      SNOWFLAKE_RESERVED_WORDS.includes(rawIdentifier.toLowerCase())\n    ) {\n      // In Snowflake if tables or attributes are created double-quoted,\n      // they are also case sensitive. If they contain any uppercase\n      // characters, they must always be double-quoted. This makes it\n      // impossible to write queries in portable SQL if tables are created in\n      // this way. Hence, we strip quotes if we don't want case sensitivity.\n      return Utils.addTicks(rawIdentifier, '\"');\n    }\n    return rawIdentifier;\n  }\n}\n\n// private methods\nfunction wrapSingleQuote(identifier) {\n  return Utils.addTicks(identifier, '\\'');\n}\n\nmodule.exports = SnowflakeQueryGenerator;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAEA,MAAM,IAAI,QAAQ;AAClB,MAAM,QAAQ,QAAQ;AACtB,MAAM,yBAAyB,QAAQ;AACvC,MAAM,OAAO,QAAQ;AACrB,MAAM,KAAK,QAAQ;AAGnB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAK;AAQP,MAAM,2BAA2B,4mBAA4mB,MAAM;AAEnpB,MAAM,qBAAqB,oBAAI,IAAI,CAAC,QAAQ,QAAQ,YAAY;AAEhE,sCAAsC,uBAAuB;AAAA,EAC3D,YAAY,SAAS;AACnB,UAAM;AAEN,SAAK,cAAc,iCACd,KAAK,cADS;AAAA,OAEhB,GAAG,SAAS;AAAA,OACZ,GAAG,YAAY;AAAA;AAAA;AAAA,EAIpB,oBAAoB,cAAc,SAAS;AACzC,cAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,OACN;AAGL,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,QAAQ,WAAW,yBAAyB,KAAK,OAAO,QAAQ;AAAA,MAChE,QAAQ,WAAW,mBAAmB,KAAK,OAAO,QAAQ;AAAA,MAC1D;AAAA;AAAA;AAAA,EAIJ,kBAAkB,cAAc;AAC9B,WAAO,2BAA2B,KAAK,gBAAgB;AAAA;AAAA,EAGzD,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,eAAe;AACb,WAAO;AAAA;AAAA,EAGT,iBAAiB,WAAW,YAAY,SAAS;AAC/C,cAAU;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,OACR;AAGL,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,UAAM,UAAU;AAEhB,eAAW,QAAQ,YAAY;AAC7B,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,YAAY;AAAO;AAC7D,YAAM,WAAW,WAAW;AAC5B,UAAI;AAEJ,UAAI,SAAS,SAAS,gBAAgB;AACpC,oBAAY,KAAK;AAEjB,YAAI,SAAS,SAAS,eAAe;AACnC,kBAAQ,SAAS,MAAM;AACvB,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM,GAAG,QAAQ,eAAe;AAC9E,sBAAY,QAAQ,MAAM;AAAA,eACrB;AACL,kBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,SAAS,QAAQ,eAAe;AAAA;AAAA,iBAEvE,SAAS,SAAS,eAAe;AAC1C,gBAAQ,SAAS,MAAM;AACvB,gBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS,MAAM;AACpD,oBAAY,QAAQ,MAAM;AAAA,aACrB;AACL,gBAAQ,KAAK,GAAG,KAAK,gBAAgB,SAAS;AAAA;AAAA;AAIlD,UAAM,QAAQ,KAAK,WAAW;AAC9B,QAAI,mBAAmB,QAAQ,KAAK;AACpC,UAAM,WAAW,YAAY,IAAI,QAAM,KAAK,gBAAgB,KAAK,KAAK;AAEtE,QAAI,QAAQ,YAAY;AACtB,QAAE,KAAK,QAAQ,YAAY,CAAC,SAAS,cAAc;AACjD,YAAI,QAAQ,aAAa;AACvB,cAAI,OAAO,cAAc,UAAU;AACjC,wBAAY,QAAQ,aAAa,QAAQ,OAAO,KAAK;AAAA;AAEvD,8BAAoB,YAAY,KAAK,gBAAgB,eAAe,QAAQ,OAAO,IAAI,WAAS,KAAK,gBAAgB,QAAQ,KAAK;AAAA;AAAA;AAAA;AAKxI,QAAI,SAAS,SAAS,GAAG;AACvB,0BAAoB,kBAAkB;AAAA;AAGxC,eAAW,QAAQ,aAAa;AAC9B,UAAI,OAAO,UAAU,eAAe,KAAK,aAAa,OAAO;AAC3D,4BAAoB,kBAAkB,KAAK,gBAAgB,UAAU,YAAY;AAAA;AAAA;AAIrF,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,QAAQ,WAAW,OAAO,QAAQ,YAAY,YAAY,WAAW,KAAK,OAAO,QAAQ;AAAA,MACzF,QAAQ,WAAW,mBAAmB,QAAQ;AAAA,MAC9C,QAAQ,WAAW,WAAW,QAAQ;AAAA,MACtC,QAAQ,aAAa,cAAc,QAAQ;AAAA,MAC3C;AAAA;AAAA;AAAA,EAIJ,mBAAmB,WAAW,QAAQ,iBAAiB;AACrD,UAAM,QAAQ,KAAK,WACjB,KAAK,UAAU;AAAA,MACb;AAAA,MACA,SAAS;AAAA,MACT,kBAAkB;AAAA;AAItB,WAAO,0BAA0B;AAAA;AAAA,EAGnC,gBAAgB,UAAU;AACxB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,WAAW,sBAAsB,KAAK,OAAO,cAAc;AAAA,MAC3D;AAAA;AAAA;AAAA,EAIJ,iBAAiB,OAAO;AACtB,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,SAAS,MAAM;AAErB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,sBAAsB,WAAW,SAAY,KAAK,OAAO,UAAU;AAAA,MACnE,oBAAoB,KAAK,OAAO;AAAA,MAChC;AAAA;AAAA;AAAA,EAIJ,eAAe,OAAO,KAAK,UAAU;AACnC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB,KAAK,eAAe,UAAU;AAAA,QAC5B,SAAS;AAAA,QACT,WAAW;AAAA,QACX,YAAY;AAAA;AAAA,MAEd;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,eAAe;AAC1C,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAIJ,kBAAkB,WAAW,YAAY;AACvC,UAAM,QAAQ,IAAI,cAAc,MAAM,iBAAiB;AAAA,MACrD;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,GAAG;AAAA,MACH;AAAA;AAEF,UAAM,MAAM;AACZ,eAAW,iBAAiB,YAAY;AACtC,UAAI,aAAa,KAAK,gBAAgB,WAAW,eAAe,WAAW;AAC3E,YAAM,UAAU;AAEhB,UAAI,WAAW,SAAS,aAAa;AACnC,gBAAQ,KAAK,MAAM,KAAK,gBAAgB,gBAAgB;AAExD,qBAAa,WAAW,QAAQ,YAAY,IAAI;AAAA,iBACvC,CAAC,WAAW,SAAS,eAAe;AAC7C,gBAAQ,KAAK,MAAM,KAAK,gBAAgB,gBAAgB;AAAA;AAG1D,UAAI,WAAW,SAAS,YAAY;AAClC,gBAAQ,KAAK,MAAM,KAAK,gBAAgB,gBAAgB,eAAe,WAAW,MAAM,mBAAmB;AAE3G,qBAAa,WAAW,QAAQ,kBAAkB,IAAI;AAAA,iBAC7C,CAAC,WAAW,SAAS,eAAe;AAC7C,gBAAQ,KAAK,MAAM,KAAK,gBAAgB,gBAAgB;AAAA;AAG1D,UAAI,WAAW,MAAM,cAAc;AACjC,qBAAa,WAAW,QAAQ,aAAa;AAC7C,gBAAQ,KAAK,MAAM,gBAAgB,KAAK,gBAAgB,gBAAgB,KAAK,QAAQ,gBAAgB;AAAA;AAGvG,UAAI,WAAW,SAAS,eAAe;AACrC,qBAAa,WAAW,QAAQ,qBAAqB;AACrD,gBAAQ,KAAK,MAAM,qBAAqB,KAAK,gBAAgB,gBAAgB,KAAK,YAAY,QAAQ,gBAAgB;AAAA,aACjH;AACL,gBAAQ,KAAK,MAAM,KAAK,gBAAgB,gBAAgB,QAAQ;AAAA;AAGlE,UAAI,KAAK,QAAQ,KAAK;AAAA;AAGxB,WAAO,IAAI,KAAK;AAAA;AAAA,EAGlB,kBAAkB,WAAW,YAAY,YAAY;AACnD,UAAM,aAAa;AAEnB,eAAW,YAAY,YAAY;AACjC,YAAM,aAAa,WAAW;AAC9B,iBAAW,KAAK,IAAI,gBAAgB,aAAa;AAAA;AAGnD,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,WAAW,KAAK;AAAA,MAChB;AAAA;AAAA;AAAA,EAIJ,sBAAsB,MAAM,WAAW,SAAS,SAAS,SAAS;AAChE,QAAI,gBAAgB,MAAM,MAAM;AAE9B,UAAI,KAAK,YAAY;AACnB,cAAM,aAAa,KAAK,qBAAqB,KAAK,YAAY,IAAI,eAChE,GAAG,KAAK,wBAAwB,UAAU,KAAK,IAAI,EAAE,KAAK,UAAU,aAAa,UAAU;AAG7F,eAAO,WAAW,KAAK;AAAA;AAEzB,UAAI,KAAK,MAAM;AACb,YAAI;AAGJ,YAAI,KAAK,yBAAyB,KAAK,OAAO;AAC5C,gBAAM,KAAK;AAAA,eACN;AAEL,gBAAM,QAAQ,EAAE,OAAO,KAAK;AAC5B,gBAAM,SAAS,MAAM;AACrB,gBAAM,KAAK,wBAAwB,QAAQ;AAAA;AAG7C,YAAI,KAAK,OAAO;AACd,iBAAO,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK;AAAA;AAG/C,eAAO;AAAA;AAAA,eAEA,gBAAgB,MAAM,MAAM;AACrC,UAAI,aAAa,KAAK,KAAK,OAAO;AAChC,aAAK,OAAO;AAAA,iBACH,KAAK,QAAQ,WAAW,KAAK,KAAK,OAAO;AAElD,aAAK,OAAO;AAAA,iBACH,oBAAoB,KAAK,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,WAAW,KAAK,KAAK,OAAO;AAC1G,aAAK,OAAO;AAAA,iBACH,QAAQ,KAAK,KAAK,OAAO;AAClC,aAAK,OAAO;AAAA;AAAA;AAIhB,WAAO,MAAM,sBAAsB,MAAM,WAAW,SAAS,SAAS;AAAA;AAAA,EAGxE,mBAAmB,WAAW;AAC5B,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA;AAAA;AAAA,EAIpB,YAAY,WAAW,OAAO,UAAU,IAAI,OAAO;AACjD,UAAM,QAAQ,KAAK,WAAW;AAC9B,QAAI,cAAc,KAAK,mBAAmB,OAAO,MAAM,OAAO;AAC9D,UAAM,QAAQ,QAAQ,SAAS,UAAU,KAAK,OAAO,QAAQ;AAC7D,QAAI,cAAc;AAClB,QAAI,uBAAuB;AAE3B,QAAI,aAAa;AACf,oBAAc,SAAS;AAAA;AAGzB,QAAI,OAAO;AACT,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM;AAAA;AAGlB,YAAM,MAAM,OAAO,OAAO,MAAM,aAAa,IAAI,QAAM,KAAK,gBAAgB,GAAG,QAAQ,KAAK;AAE5F,oBAAc,MAAM,qBAAqB,SAAS,IAAI,IAAI,SAAS;AACnE,6BAAuB;AAEvB,aAAO,MAAM,iBAAiB;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA;AAGJ,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA,EAIJ,mBAAmB;AACjB,WAAO;AAAA;AAAA,EAGT,qBAAqB,OAAO,gBAAgB;AAC1C,UAAM,YAAY,MAAM,aAAa;AACrC,UAAM,aAAa,MAAM;AAEzB,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAAqB;AAAA,MACrB,kBAAkB,0BAA0B;AAAA,MAC5C,cAAc,uBAAuB;AAAA,MACrC;AAAA;AAAA;AAAA,EAIJ,iBAAiB,WAAW,uBAAuB;AACjD,QAAI,YAAY;AAEhB,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,MAAM,WAAW,GAAG,aAAa,sBAAsB,KAAK;AAAA;AAG1E,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA;AAAA;AAAA,EAIJ,eAAe,WAAW,SAAS;AACjC,QAAI,CAAC,EAAE,cAAc,YAAY;AAC/B,kBAAY;AAAA,QACV,MAAM;AAAA;AAAA;AAIV,UAAM,kBAAkB,UAAU,KAAK,SAAS,EAAE,QAAQ,KAAK,OAAO,KAAK;AAC3E,QAAI,WAAW;AAEf,QAAI,UAAU,cAAc,OAAO;AACjC,kBAAY;AAAA;AAGd,QAAI,UAAU,eAAe;AAC3B,kBAAY;AAAA;AAId,QAAI,CAAC,mBAAmB,IAAI,oBACvB,UAAU,KAAK,YAAY,QAC3B,MAAM,sBAAsB,UAAU,eAAe;AACxD,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,WAAW,MAAM;AAC7B,kBAAY;AAAA;AAGd,QAAI,UAAU,YAAY;AACxB,kBAAY;AAAA;AAGd,QAAI,UAAU,SAAS;AACrB,kBAAY,YAAY,KAAK,OAAO,UAAU;AAAA;AAGhD,QAAI,UAAU,OAAO;AACnB,kBAAY;AAAA;AAEd,QAAI,UAAU,OAAO;AACnB,kBAAY,UAAU,KAAK,gBAAgB,UAAU;AAAA;AAGvD,QAAI,UAAU,YAAY;AACxB,UAAI,WAAW,QAAQ,YAAY,eAAe,QAAQ,YAAY;AACpE,cAAM,WAAW,KAAK,gBAAgB,QAAQ;AAC9C,cAAM,SAAS,KAAK,gBAAgB,GAAG,QAAQ,aAAa;AAE5D,oBAAY,oBAAoB,uBAAuB;AAAA;AAGzD,kBAAY,eAAe,KAAK,WAAW,UAAU,WAAW;AAEhE,UAAI,UAAU,WAAW,KAAK;AAC5B,oBAAY,KAAK,KAAK,gBAAgB,UAAU,WAAW;AAAA,aACtD;AACL,oBAAY,KAAK,KAAK,gBAAgB;AAAA;AAGxC,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAG/C,UAAI,UAAU,UAAU;AACtB,oBAAY,cAAc,UAAU,SAAS;AAAA;AAAA;AAIjD,WAAO;AAAA;AAAA,EAGT,gBAAgB,YAAY,SAAS;AACnC,UAAM,SAAS;AAEf,eAAW,OAAO,YAAY;AAC5B,YAAM,YAAY,WAAW;AAC7B,aAAO,UAAU,SAAS,OAAO,KAAK,eAAe,WAAW;AAAA;AAGlE,WAAO;AAAA;AAAA,EAWT,yBAAyB,MAAM;AAC7B,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;AAAA;AAGT,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AAEtB,WAAO,eAAe,KAAK,QAAQ;AACjC,YAAM,SAAS,KAAK,OAAO;AAC3B,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG,QAAQ;AAC3C,0BAAkB;AAClB;AAAA;AAGF,YAAM,kBAAkB,oBAAoB,KAAK;AACjD,UAAI,iBAAiB;AACnB,wBAAgB,gBAAgB,GAAG;AACnC,0BAAkB;AAClB;AAAA;AAGF,YAAM,eAAe,oBAAoB,KAAK;AAC9C,UAAI,cAAc;AAChB,cAAM,gBAAgB,aAAa;AACnC,YAAI,kBAAkB,KAAK;AACzB;AAAA,mBACS,kBAAkB,KAAK;AAChC;AAAA,mBACS,kBAAkB,KAAK;AAChC,4BAAkB;AAClB;AAAA;AAEF,wBAAgB,aAAa,GAAG;AAChC;AAAA;AAGF;AAAA;AAIF,QAAI,mBAAoB,oBAAmB,oBAAoB,kBAAkB;AAC/E,YAAM,IAAI,MAAM,2BAA2B;AAAA;AAI7C,WAAO;AAAA;AAAA,EAGT,gBAAgB,WAAW,MAAM,UAAU;AACzC,QAAI,SAAS,SAAS,gBAAgB;AACpC,iBAAW,SAAS,QAAQ,eAAe;AAAA;AAG7C,QAAI,SAAS,SAAS,WAAW;AAC/B,UAAI,SAAS,SAAS,WAAW;AAC/B,mBAAW,SAAS,QAAQ,UAAU;AACtC,mBAAW,SAAS,QAAQ,UAAU;AAAA,iBAC7B,SAAS,SAAS,aAAa;AACxC,mBAAW,SAAS,QAAQ,UAAU;AACtC,mBAAW,SAAS,QAAQ,YAAY;AAAA,aACnC;AACL,mBAAW,SAAS,QAAQ,WAAW;AAAA;AAEzC,iBAAW,SAAS,QAAQ,YAAY;AAAA;AAG1C,WAAO;AAAA;AAAA,EAWT,oBAAoB,OAAO,YAAY;AACrC,UAAM,YAAY,MAAM,aAAa;AACrC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,gEAAgE;AAAA,MAChE,yDAAyD;AAAA,MACzD;AAAA,MACA;AAAA;AAAA;AAAA,EAYJ,mBAAmB,OAAO,YAAY;AACpC,UAAM,mBAAmB,MAAM,SAAS,gBAAgB,MAAM,UAAU;AACxE,UAAM,kBAAkB,gBAAgB,MAAM,aAAa;AAC3D,UAAM,mBAAmB,gBAAgB;AAEzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,2BAA2B;AAAA,QAC3B,MAAM,UAAU,iCAAiC;AAAA,QACjD,gCAAgC;AAAA;AAAA,MAElC;AAAA,MACA;AAAA,QACE,gBAAgB;AAAA,QAChB,MAAM,UAAU,sBAAsB;AAAA,QACtC,qBAAqB;AAAA,QACrB;AAAA;AAAA,MAEF;AAAA;AAAA;AAAA,EAYJ,oBAAoB,WAAW,YAAY;AACzC,WAAO,MAAM,iBAAiB;AAAA,MAC5B;AAAA,MACA,KAAK,WAAW;AAAA,MAChB;AAAA,MACA,KAAK,gBAAgB;AAAA,MACrB;AAAA;AAAA;AAAA,EAIJ,kBAAkB,SAAS;AACzB,QAAI,WAAW;AACf,QAAI,QAAQ,WAAW,QAAQ,QAAQ,WAAW,UAAa,QAAQ,WAAW,GAAG;AACnF,iBAAW,SAAS,OAAO,CAAC,WAAW,KAAK,OAAO,QAAQ,QAAQ,YAAY,KAAK,OAAO,QAAQ;AAAA,eACzF,QAAQ,UAAU,QAAQ,QAAQ,UAAU,QAAY;AAClE,iBAAW,CAAC,WAAW,KAAK,OAAO,QAAQ;AAAA;AAE7C,WAAO,SAAS,KAAK;AAAA;AAAA,EAWvB,gBAAgB,YAAY,OAAO;AACjC,UAAM,gBAAgB,SAAS;AAC/B,UAAM,sBAAsB,KAAK,QAAQ,qBAAqB;AAC9D,UAAM,gBAAgB,MAAM,YAAY,YAAY;AAEpD,QACE,kBAAkB,QAClB,wBAAwB,SACxB,WAAW,SAAS,QACpB,WAAW,SAAS,SACpB,yBAAyB,SAAS,cAAc,gBAChD;AAMA,aAAO,MAAM,SAAS,eAAe;AAAA;AAEvC,WAAO;AAAA;AAAA;AAKX,yBAAyB,YAAY;AACnC,SAAO,MAAM,SAAS,YAAY;AAAA;AAGpC,OAAO,UAAU;", "names": []}