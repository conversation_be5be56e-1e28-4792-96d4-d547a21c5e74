
// userInfo.js
const dataManager = require('../../../../utils/dataManager');

Page({
  data: {
    userInfo: {},
    isEditing: false
  },

  onLoad: function() {
    // 从本地数据获取用户信息
    const userInfo = dataManager.getData('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },

  // 进入编辑模式
  startEditing: function() {
    this.setData({
      isEditing: true
    });
  },

  // 取消编辑
  cancelEdit: function() {
    this.setData({
      isEditing: false
    });
  },

  // 保存编辑
  saveEdit: function(e) {
    const formData = e.detail.value;
    const validate = require('../../../../utils/validation');
    
    // 验证表单数据
    if (!validate.required(formData.name)) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    
    if (!validate.length(formData.name, 2, 20)) {
      wx.showToast({
        title: '姓名长度应在2-20个字符之间',
        icon: 'none'
      });
      return;
    }
    
    if (!validate.required(formData.role)) {
      wx.showToast({
        title: '请输入角色',
        icon: 'none'
      });
      return;
    }
    
    // 更新本地数据
    dataManager.updateData('userInfo', formData);
    
    this.setData({
      userInfo: formData,
      isEditing: false
    });
    
    wx.showToast({
      title: '保存成功',
      icon: 'success'
    });
    
    // 通知主页面更新
    const pages = getCurrentPages();
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];
      prevPage.refreshData && prevPage.refreshData();
    }
  },

  // 表单输入处理
  inputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`userInfo.${field}`]: value
    });
  }
})