module.exports = (sequelize, DataTypes) => {
  const NotificationSettings = sequelize.define('NotificationSettings', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    approval_notice: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    usage_reminder: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    maintenance_alert: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    system_notification: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    email_notification: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    sms_notification: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    }
  }, {
    tableName: 'notification_settings'
  });

  return NotificationSettings;
};
