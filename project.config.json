{"appid": "wxddb26c0264ff0c13", "compileType": "miniprogram", "libVersion": "3.5.5", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": true, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": true, "disableSWC": false, "disableUseStrict": false, "useCompilerPlugins": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}