
/* approvers.wxss */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}

/* 列表样式 */
.approver-list {
  background: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  background: #1aad19;
  color: #fff;
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  margin: 0;
}

/* 审批人项目 */
.approver-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.approver-item:last-child {
  border-bottom: none;
}

.approver-info {
  flex: 1;
}

.name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.details {
  font-size: 26rpx;
  color: #666;
}

.role {
  margin-right: 20rpx;
}

.actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin: 0;
  min-width: 100rpx;
}

.action-btn.edit {
  background: #f0f0f0;
  color: #666;
}

.action-btn.delete {
  background: #ff4d4f;
  color: #fff;
}

/* 编辑表单 */
.edit-form {
  background: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.cancel-btn, .save-btn {
  flex: 1;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 40rpx;
  margin: 0;
}

.cancel-btn {
  background: #f0f0f0;
  color: #666;
}

.save-btn {
  background: #1aad19;
  color: #fff;
}

/* 空提示 */
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}